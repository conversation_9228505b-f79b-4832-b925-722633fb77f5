"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-popper"],{75009:function(e,t,n){function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}function o(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}function i(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,p(e,t)}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}function c(e,t,n){return(t=s(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.r(t),n.d(t,{Manager:function(){return b},Popper:function(){return I},Reference:function(){return U},placements:function(){return S}});var u=n(27074),l=n.n(u),f=n(89526),d=n(65276),m=n(86397),h=n.n(m),y=h()(),v=h()(),b=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),p=0;p<n;p++)r[p]=arguments[p];return c(o(t=e.call.apply(e,[this].concat(r))||this),"referenceNode",void 0),c(o(t),"setReferenceNode",(function(e){e&&t.referenceNode!==e&&(t.referenceNode=e,t.forceUpdate())})),t}i(t,e);var n=t.prototype;return n.componentWillUnmount=function(){this.referenceNode=null},n.render=function(){return f.createElement(y.Provider,{value:this.referenceNode},f.createElement(v.Provider,{value:this.setReferenceNode},this.props.children))},t}(f.Component),w=function(e){return Array.isArray(e)?e[0]:e},P=function(e){if("function"===typeof e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e.apply(void 0,n)}},N=function(e,t){if("function"===typeof e)return P(e,t);null!=e&&(e.current=t)},E={position:"absolute",top:0,left:0,opacity:0,pointerEvents:"none"},g={},O=function(e){function t(){for(var t,n=arguments.length,p=new Array(n),i=0;i<n;i++)p[i]=arguments[i];return c(o(t=e.call.apply(e,[this].concat(p))||this),"state",{data:void 0,placement:void 0}),c(o(t),"popperInstance",void 0),c(o(t),"popperNode",null),c(o(t),"arrowNode",null),c(o(t),"setPopperNode",(function(e){e&&t.popperNode!==e&&(N(t.props.innerRef,e),t.popperNode=e,t.updatePopperInstance())})),c(o(t),"setArrowNode",(function(e){t.arrowNode=e})),c(o(t),"updateStateModifier",{enabled:!0,order:900,fn:function(e){var n=e.placement;return t.setState({data:e,placement:n}),e}}),c(o(t),"getOptions",(function(){return{placement:t.props.placement,eventsEnabled:t.props.eventsEnabled,positionFixed:t.props.positionFixed,modifiers:r({},t.props.modifiers,{arrow:r({},t.props.modifiers&&t.props.modifiers.arrow,{enabled:!!t.arrowNode,element:t.arrowNode}),applyStyle:{enabled:!1},updateStateModifier:t.updateStateModifier})}})),c(o(t),"getPopperStyle",(function(){return t.popperNode&&t.state.data?r({position:t.state.data.offsets.popper.position},t.state.data.styles):E})),c(o(t),"getPopperPlacement",(function(){return t.state.data?t.state.placement:void 0})),c(o(t),"getArrowStyle",(function(){return t.arrowNode&&t.state.data?t.state.data.arrowStyles:g})),c(o(t),"getOutOfBoundariesState",(function(){return t.state.data?t.state.data.hide:void 0})),c(o(t),"destroyPopperInstance",(function(){t.popperInstance&&(t.popperInstance.destroy(),t.popperInstance=null)})),c(o(t),"updatePopperInstance",(function(){t.destroyPopperInstance();var e=o(t).popperNode,n=t.props.referenceElement;n&&e&&(t.popperInstance=new d.Z(n,e,t.getOptions()))})),c(o(t),"scheduleUpdate",(function(){t.popperInstance&&t.popperInstance.scheduleUpdate()})),t}i(t,e);var n=t.prototype;return n.componentDidUpdate=function(e,t){this.props.placement===e.placement&&this.props.referenceElement===e.referenceElement&&this.props.positionFixed===e.positionFixed&&l()(this.props.modifiers,e.modifiers,{strict:!0})?this.props.eventsEnabled!==e.eventsEnabled&&this.popperInstance&&(this.props.eventsEnabled?this.popperInstance.enableEventListeners():this.popperInstance.disableEventListeners()):this.updatePopperInstance(),t.placement!==this.state.placement&&this.scheduleUpdate()},n.componentWillUnmount=function(){N(this.props.innerRef,null),this.destroyPopperInstance()},n.render=function(){return w(this.props.children)({ref:this.setPopperNode,style:this.getPopperStyle(),placement:this.getPopperPlacement(),outOfBoundaries:this.getOutOfBoundariesState(),scheduleUpdate:this.scheduleUpdate,arrowProps:{ref:this.setArrowNode,style:this.getArrowStyle()}})},t}(f.Component);c(O,"defaultProps",{placement:"bottom",eventsEnabled:!0,referenceElement:void 0,positionFixed:!1});var S=d.Z.placements;function I(e){var t=e.referenceElement,n=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,["referenceElement"]);return f.createElement(y.Consumer,null,(function(e){return f.createElement(O,r({referenceElement:void 0!==t?t:e},n))}))}var R=n(80919),j=n.n(R),A=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),p=0;p<n;p++)r[p]=arguments[p];return c(o(t=e.call.apply(e,[this].concat(r))||this),"refHandler",(function(e){N(t.props.innerRef,e),P(t.props.setReferenceNode,e)})),t}i(t,e);var n=t.prototype;return n.componentWillUnmount=function(){N(this.props.innerRef,null)},n.render=function(){return j()(Boolean(this.props.setReferenceNode),"`Reference` should not be used outside of a `Manager` component."),w(this.props.children)({ref:this.refHandler})},t}(f.Component);function U(e){return f.createElement(v.Consumer,null,(function(t){return f.createElement(A,r({setReferenceNode:t},e))}))}},80919:function(e){var t=function(){};e.exports=t}}]);
//# sourceMappingURL=react-popper.172effad0b57aa8c0a5ddf541a7ad7dc.js.map