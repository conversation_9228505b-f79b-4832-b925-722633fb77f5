// import { LogIn } from '@sr/shared-product-components';

// export function intercomBoot(accInfo: LogIn.IAccount) {
//   try {


//     /**
//      * 18-Jul-2024: many times intercom loading failed
//      * as our react app was loading before the intercom script had loaded
//      * and this fn call failed because of that
//      * 
//      * Therefore, now we are checking if the script has loaded every second
//      * for the first 20 seconds, and once the script has loaded we call the
//      * intercomBoot fn and stop the timer
//      */


//     // check and load intercom
//     let secondsPassed = 0;

//     const timer = setInterval(() => {

//       const intercom = (window as any).Intercom;

//       if(!!intercom) {

//         // stop the timer
//         clearInterval(timer);


//         console.log(`[intercom] intercomBoot: loading intercom after ${secondsPassed} seconds`)


//         const intercomUser = {
//           user_id: accInfo.internal_id,
//           email: accInfo.email,
//           user_hash: accInfo.intercom_hash,
//           name: accInfo.first_name + ' ' + accInfo.last_name,
//           "firstname": accInfo.first_name,
//           "lastname": accInfo.last_name,
//           // "teamAdmin": isTeamAdmin,
//           "createdAt": accInfo.created_at,
//           "orgRole": accInfo.org_role,
//           "email_verified": accInfo.email_verified,
//           company: {
//             company_id: accInfo.org.id,
//             name: accInfo.org.name,
//             // planType: this.props.logInStore.getPlanType,
//             planName: accInfo.org.plan.plan_name,
//             trialEndsAt: accInfo.org.trial_ends_at,
//             // nextBillingDate: accInfo.org.next_billing_date,
//             // accountType: accInfo.account_type,
//             // plan: this.props.logInStore.getPlanType || ''
//           }
//         };

//         intercom('boot', {
//           app_id: 'xmya8oga',
//           ...intercomUser
//         });


//       } else if (secondsPassed >= 20) {


//         // stop the timer after 20 seconds, we will no longer retry checking
//         clearInterval(timer);

//         console.error('[intercom] intercomBoot: 20 seconds passed, intercom still not found, ignoring loading intercom')

//       } else {

//         secondsPassed += 1

//       }
//     },
    
//     1000);




    
//   } catch (e) {
//     console.error('[intercom] intercomBoot: ', e)
//   }
// }


// export function intercomResetSession() {
//   try {
//     (window as any).Intercom('shutdown')
//   } catch (e) {
//     console.error('[intercom] intercomResetSession: ', e)
//   }
// }

// export function intercomShowChatBox() {
//   try {
//     // Opens chatbox in an async-safe way
//     (window as any).Intercom('show');
//   } catch (e) {
//     console.error('[intercom] intercomToggleChatBox: ', e)
//   }
// }

// export function intercomHideChatBox() {
//   try {
//     // Opens chatbox in an async-safe way
//     (window as any).Intercom('hide');
//   } catch (e) {
//     console.error('[intercom] intercomToggleChatBox: ', e)
//   }
// }

// export function intercomTrackEvent(event: string) {
//   try {
//     (window as any).Intercom('trackEvent', event);
//   } catch (e) {
//     console.error('[intercom] intercomTrackEvent trackEvent: ', event, e)
//   }
// }
