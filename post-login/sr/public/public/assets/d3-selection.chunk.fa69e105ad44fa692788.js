"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["d3-selection"],{82188:function(t,n,e){function r(t){return function(){return this.matches(t)}}function i(t){return function(n){return n.matches(t)}}e.d(n,{Z:function(){return r},P:function(){return i}})},51650:function(t,n,e){e.d(n,{Z:function(){return i}});var r=e(7705);function i(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),r.Z.hasOwnProperty(n)?{space:r.Z[n],local:t}:t}},7705:function(t,n,e){e.d(n,{P:function(){return r}});var r="http://www.w3.org/1999/xhtml";n.Z={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},35827:function(t,n,e){function r(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}e.d(n,{Z:function(){return r}})},22195:function(t,n,e){e.d(n,{Z:function(){return i}});var r=e(89724);function i(t){return"string"===typeof t?new r.Y1([[document.querySelector(t)]],[document.documentElement]):new r.Y1([[t]],r.Jz)}},89724:function(t,n,e){e.d(n,{Y1:function(){return ut},ZP:function(){return st},Jz:function(){return ot}});var r=e(63049);var i=e(37108);function o(t){return function(){return null==(n=t.apply(this,arguments))?[]:Array.isArray(n)?n:Array.from(n);var n}}var u=e(82188),c=Array.prototype.find;function s(){return this.firstElementChild}var a=Array.prototype.filter;function f(){return Array.from(this.children)}function l(t){return new Array(t.length)}function h(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function p(t,n,e,r,i,o){for(var u,c=0,s=n.length,a=o.length;c<a;++c)(u=n[c])?(u.__data__=o[c],r[c]=u):e[c]=new h(t,o[c]);for(;c<s;++c)(u=n[c])&&(i[c]=u)}function _(t,n,e,r,i,o,u){var c,s,a,f=new Map,l=n.length,p=o.length,_=new Array(l);for(c=0;c<l;++c)(s=n[c])&&(_[c]=a=u.call(s,s.__data__,c,n)+"",f.has(a)?i[c]=s:f.set(a,s));for(c=0;c<p;++c)a=u.call(t,o[c],c,o)+"",(s=f.get(a))?(r[c]=s,s.__data__=o[c],f.delete(a)):e[c]=new h(t,o[c]);for(c=0;c<l;++c)(s=n[c])&&f.get(_[c])===s&&(i[c]=s)}function d(t){return t.__data__}function v(t){return"object"===typeof t&&"length"in t?t:Array.from(t)}function y(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}h.prototype={constructor:h,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var m=e(51650);function g(t){return function(){this.removeAttribute(t)}}function w(t){return function(){this.removeAttributeNS(t.space,t.local)}}function A(t,n){return function(){this.setAttribute(t,n)}}function x(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function b(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function S(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}var Z=e(55550);function C(t){return function(){delete this[t]}}function E(t,n){return function(){this[t]=n}}function N(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function P(t){return t.trim().split(/^|\s+/)}function L(t){return t.classList||new B(t)}function B(t){this._node=t,this._names=P(t.getAttribute("class")||"")}function M(t,n){for(var e=L(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function T(t,n){for(var e=L(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function q(t){return function(){M(this,t)}}function D(t){return function(){T(this,t)}}function V(t,n){return function(){(n.apply(this,arguments)?M:T)(this,t)}}function k(){this.textContent=""}function O(t){return function(){this.textContent=t}}function R(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}function Y(){this.innerHTML=""}function j(t){return function(){this.innerHTML=t}}function H(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}function I(){this.nextSibling&&this.parentNode.appendChild(this)}function U(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}B.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var X=e(7705);function z(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===X.P&&n.documentElement.namespaceURI===X.P?n.createElement(t):n.createElementNS(e,t)}}function G(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function J(t){var n=(0,m.Z)(t);return(n.local?G:z)(n)}function F(){return null}function K(){var t=this.parentNode;t&&t.removeChild(this)}function Q(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function W(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function $(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function tt(t,n,e){return function(){var r,i=this.__on,o=function(t){return function(n){t.call(this,n,this.__data__)}}(n);if(i)for(var u=0,c=i.length;u<c;++u)if((r=i[u]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),void(r.value=n);this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}var nt=e(36591);function et(t,n,e){var r=(0,nt.Z)(t),i=r.CustomEvent;"function"===typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}function rt(t,n){return function(){return et(this,t,n)}}function it(t,n){return function(){return et(this,t,n.apply(this,arguments))}}var ot=[null];function ut(t,n){this._groups=t,this._parents=n}function ct(){return new ut([[document.documentElement]],ot)}ut.prototype=ct.prototype={constructor:ut,select:function(t){"function"!==typeof t&&(t=(0,r.Z)(t));for(var n=this._groups,e=n.length,i=new Array(e),o=0;o<e;++o)for(var u,c,s=n[o],a=s.length,f=i[o]=new Array(a),l=0;l<a;++l)(u=s[l])&&(c=t.call(u,u.__data__,l,s))&&("__data__"in u&&(c.__data__=u.__data__),f[l]=c);return new ut(i,this._parents)},selectAll:function(t){t="function"===typeof t?o(t):(0,i.Z)(t);for(var n=this._groups,e=n.length,r=[],u=[],c=0;c<e;++c)for(var s,a=n[c],f=a.length,l=0;l<f;++l)(s=a[l])&&(r.push(t.call(s,s.__data__,l,a)),u.push(s));return new ut(r,u)},selectChild:function(t){return this.select(null==t?s:function(t){return function(){return c.call(this.children,t)}}("function"===typeof t?t:(0,u.P)(t)))},selectChildren:function(t){return this.selectAll(null==t?f:function(t){return function(){return a.call(this.children,t)}}("function"===typeof t?t:(0,u.P)(t)))},filter:function(t){"function"!==typeof t&&(t=(0,u.Z)(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,c=n[i],s=c.length,a=r[i]=[],f=0;f<s;++f)(o=c[f])&&t.call(o,o.__data__,f,c)&&a.push(o);return new ut(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,d);var e,r=n?_:p,i=this._parents,o=this._groups;"function"!==typeof t&&(e=t,t=function(){return e});for(var u=o.length,c=new Array(u),s=new Array(u),a=new Array(u),f=0;f<u;++f){var l=i[f],h=o[f],y=h.length,m=v(t.call(l,l&&l.__data__,f,i)),g=m.length,w=s[f]=new Array(g),A=c[f]=new Array(g);r(l,h,w,A,a[f]=new Array(y),m,n);for(var x,b,S=0,Z=0;S<g;++S)if(x=w[S]){for(S>=Z&&(Z=S+1);!(b=A[Z])&&++Z<g;);x._next=b||null}}return(c=new ut(c,i))._enter=s,c._exit=a,c},enter:function(){return new ut(this._enter||this._groups.map(l),this._parents)},exit:function(){return new ut(this._exit||this._groups.map(l),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"===typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),c=new Array(i),s=0;s<u;++s)for(var a,f=e[s],l=r[s],h=f.length,p=c[s]=new Array(h),_=0;_<h;++_)(a=f[_]||l[_])&&(p[_]=a);for(;s<i;++s)c[s]=e[s];return new ut(c,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=y);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u,c=e[o],s=c.length,a=i[o]=new Array(s),f=0;f<s;++f)(u=c[f])&&(a[f]=u);a.sort(n)}return new ut(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(const n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,c=o.length;u<c;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=(0,m.Z)(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?w:g:"function"===typeof n?e.local?S:b:e.local?x:A)(e,n))},style:Z.Z,property:function(t,n){return arguments.length>1?this.each((null==n?C:"function"===typeof n?N:E)(t,n)):this.node()[t]},classed:function(t,n){var e=P(t+"");if(arguments.length<2){for(var r=L(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"===typeof n?V:n?q:D)(e,n))},text:function(t){return arguments.length?this.each(null==t?k:("function"===typeof t?R:O)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?Y:("function"===typeof t?H:j)(t)):this.node().innerHTML},raise:function(){return this.each(I)},lower:function(){return this.each(U)},append:function(t){var n="function"===typeof t?t:J(t);return this.select((function(){return this.appendChild(n.apply(this,arguments))}))},insert:function(t,n){var e="function"===typeof t?t:J(t),i=null==n?F:"function"===typeof n?n:(0,r.Z)(n);return this.select((function(){return this.insertBefore(e.apply(this,arguments),i.apply(this,arguments)||null)}))},remove:function(){return this.each(K)},clone:function(t){return this.select(t?W:Q)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=function(t){return t.trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}))}(t+""),u=o.length;if(!(arguments.length<2)){for(c=n?tt:$,r=0;r<u;++r)this.each(c(o[r],n,e));return this}var c=this.node().__on;if(c)for(var s,a=0,f=c.length;a<f;++a)for(r=0,s=c[a];r<u;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value},dispatch:function(t,n){return this.each(("function"===typeof n?it:rt)(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};var st=ct},55550:function(t,n,e){e.d(n,{Z:function(){return c},S:function(){return s}});var r=e(36591);function i(t){return function(){this.style.removeProperty(t)}}function o(t,n,e){return function(){this.style.setProperty(t,n,e)}}function u(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function c(t,n,e){return arguments.length>1?this.each((null==n?i:"function"===typeof n?u:o)(t,n,null==e?"":e)):s(this.node(),t)}function s(t,n){return t.style.getPropertyValue(n)||(0,r.Z)(t).getComputedStyle(t,null).getPropertyValue(n)}},63049:function(t,n,e){function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}e.d(n,{Z:function(){return i}})},37108:function(t,n,e){function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}e.d(n,{Z:function(){return i}})},36591:function(t,n,e){function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}e.d(n,{Z:function(){return r}})}}]);
//# sourceMappingURL=d3-selection.2a3807139c1ec29db9b488650112657d.js.map