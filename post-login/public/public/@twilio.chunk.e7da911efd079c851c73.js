"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@twilio"],{14881:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.TwilioError=t.<PERSON>gger=t.PreflightTest=t.Device=t.Call=void 0;var o=n(55765);t.Call=o.default;var i=n(56546);t.Device=i.default;var r=n(18987);t.TwilioError=r;var a=n(24468);Object.defineProperty(t,"Logger",{enumerable:!0,get:function(){return a.Logger}});var s=n(37919);Object.defineProperty(t,"PreflightTest",{enumerable:!0,get:function(){return s.PreflightTest}})},68884:function(e,t,n){var o=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},i=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncQueue=void 0;var r=n(36135),a=function(){function e(){this._operations=[]}return e.prototype.enqueue=function(e){var t=!!this._operations.length,n=new r.default;return this._operations.push({deferred:n,callback:e}),t||this._processQueue(),n.promise},e.prototype._processQueue=function(){return o(this,void 0,void 0,(function(){var e,t,n,o,r,a,s;return i(this,(function(i){switch(i.label){case 0:if(!this._operations.length)return[3,5];e=this._operations[0],t=e.deferred,n=e.callback,o=void 0,r=void 0,a=void 0,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,n()];case 2:return o=i.sent(),a=!0,[3,4];case 3:return s=i.sent(),r=s,[3,4];case 4:return this._operations.shift(),a?t.resolve(o):t.reject(r),[3,0];case 5:return[2]}}))}))},e}();t.AsyncQueue=a},45996:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=n(5939),r=n(56546),a=n(18987),s=n(24468),c=n(56553),u=n(46624),d=n(62997),l={audioinput:"Audio Input",audiooutput:"Audio Output"},p=function(e){function t(t,n,o){var i,a=e.call(this)||this;a.availableInputDevices=new Map,a.availableOutputDevices=new Map,a._audioConstraints=null,a._defaultInputDeviceStream=null,a._enabledSounds=((i={})[r.default.SoundName.Disconnect]=!0,i[r.default.SoundName.Incoming]=!0,i[r.default.SoundName.Outgoing]=!0,i),a._inputDevice=null,a._isPollingInputVolume=!1,a._log=new s.default("AudioHelper"),a._processedStream=null,a._selectedInputDeviceStream=null,a._unknownDeviceIndexes={audioinput:{},audiooutput:{}},a._updateAvailableDevices=function(){return a._mediaDevices&&a._enumerateDevices?a._enumerateDevices().then((function(e){a._updateDevices(e.filter((function(e){return"audiooutput"===e.kind})),a.availableOutputDevices,a._removeLostOutput),a._updateDevices(e.filter((function(e){return"audioinput"===e.kind})),a.availableInputDevices,a._removeLostInput);var t=a.availableOutputDevices.get("default")||Array.from(a.availableOutputDevices.values())[0];[a.speakerDevices,a.ringtoneDevices].forEach((function(e){!e.get().size&&a.availableOutputDevices.size&&a.isOutputSelectionSupported&&e.set(t.deviceId).catch((function(e){a._log.warn("Unable to set audio output devices. "+e)}))}))})):Promise.reject("Enumeration not supported")},a._removeLostInput=function(e){if(!a.inputDevice||a.inputDevice.deviceId!==e.deviceId)return!1;a._destroyProcessedStream(),a._replaceStream(null),a._inputDevice=null,a._maybeStopPollingVolume();var t=a.availableInputDevices.get("default")||Array.from(a.availableInputDevices.values())[0];return t&&a.setInputDevice(t.deviceId),!0},a._removeLostOutput=function(e){var t=a.speakerDevices.delete(e),n=a.ringtoneDevices.delete(e);return t||n},o=Object.assign({AudioContext:"undefined"!==typeof AudioContext&&AudioContext,setSinkId:"undefined"!==typeof HTMLAudioElement&&HTMLAudioElement.prototype.setSinkId},o),a._updateUserOptions(o),a._audioProcessorEventObserver=o.audioProcessorEventObserver,a._mediaDevices=o.mediaDevices||navigator.mediaDevices,a._onActiveInputChanged=n,a._enumerateDevices="function"===typeof o.enumerateDevices?o.enumerateDevices:a._mediaDevices&&a._mediaDevices.enumerateDevices.bind(a._mediaDevices);var u=!(!o.AudioContext&&!o.audioContext),d=!!a._enumerateDevices;o.enabledSounds&&(a._enabledSounds=o.enabledSounds);var l="function"===typeof o.setSinkId;return a.isOutputSelectionSupported=d&&l,a.isVolumeSupported=u,a.isVolumeSupported&&(a._audioContext=o.audioContext||o.AudioContext&&new o.AudioContext,a._audioContext&&(a._inputVolumeAnalyser=a._audioContext.createAnalyser(),a._inputVolumeAnalyser.fftSize=32,a._inputVolumeAnalyser.smoothingTimeConstant=.3)),a.ringtoneDevices=new c.default("ringtone",a.availableOutputDevices,t,a.isOutputSelectionSupported),a.speakerDevices=new c.default("speaker",a.availableOutputDevices,t,a.isOutputSelectionSupported),a.addListener("newListener",(function(e){"inputVolume"===e&&a._maybeStartPollingVolume()})),a.addListener("removeListener",(function(e){"inputVolume"===e&&a._maybeStopPollingVolume()})),a.once("newListener",(function(){a.isOutputSelectionSupported||a._log.warn("Warning: This browser does not support audio output selection."),a.isVolumeSupported||a._log.warn("Warning: This browser does not support Twilio's volume indicator feature.")})),d&&a._initializeEnumeration(),a}return o(t,e),Object.defineProperty(t.prototype,"audioConstraints",{get:function(){return this._audioConstraints},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"inputDevice",{get:function(){return this._inputDevice},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"inputStream",{get:function(){return this._processedStream||this._selectedInputDeviceStream},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"processedStream",{get:function(){return this._processedStream},enumerable:!1,configurable:!0}),t.prototype._destroy=function(){this._stopDefaultInputDeviceStream(),this._stopSelectedInputDeviceStream(),this._destroyProcessedStream(),this._maybeStopPollingVolume(),this.removeAllListeners(),this._unbind()},t.prototype._maybeStartPollingVolume=function(){var e=this;if(this.isVolumeSupported&&this.inputStream&&(this._updateVolumeSource(),!this._isPollingInputVolume&&this._inputVolumeAnalyser)){var t=this._inputVolumeAnalyser.frequencyBinCount,n=new Uint8Array(t);this._isPollingInputVolume=!0;var o=function(){if(e._isPollingInputVolume){if(e._inputVolumeAnalyser){e._inputVolumeAnalyser.getByteFrequencyData(n);var t=d.average(n);e.emit("inputVolume",t/255)}requestAnimationFrame(o)}};requestAnimationFrame(o)}},t.prototype._maybeStopPollingVolume=function(){this.isVolumeSupported&&(!this._isPollingInputVolume||this.inputStream&&this.listenerCount("inputVolume")||(this._inputVolumeSource&&(this._inputVolumeSource.disconnect(),delete this._inputVolumeSource),this._isPollingInputVolume=!1))},t.prototype._openDefaultDeviceWithConstraints=function(e){var t=this;return this._log.info("Opening default device with constraints",e),this._getUserMedia(e).then((function(e){return t._log.info("Opened default device. Updating available devices."),t._updateAvailableDevices().catch((function(e){t._log.warn("Unable to updateAvailableDevices after gUM call",e)})),t._defaultInputDeviceStream=e,t._maybeCreateProcessedStream(e)}))},t.prototype._stopDefaultInputDeviceStream=function(){this._defaultInputDeviceStream&&(this._log.info("stopping default device stream"),this._defaultInputDeviceStream.getTracks().forEach((function(e){return e.stop()})),this._defaultInputDeviceStream=null,this._destroyProcessedStream())},t.prototype._unbind=function(){if(!this._mediaDevices||!this._enumerateDevices)throw new a.NotSupportedError("Enumeration is not supported");this._mediaDevices.removeEventListener&&this._mediaDevices.removeEventListener("devicechange",this._updateAvailableDevices)},t.prototype._updateUserOptions=function(e){"function"===typeof e.enumerateDevices&&(this._enumerateDevices=e.enumerateDevices),"function"===typeof e.getUserMedia&&(this._getUserMedia=e.getUserMedia)},t.prototype.addProcessor=function(e){if(this._log.debug(".addProcessor"),this._processor)throw new a.NotSupportedError("Adding multiple AudioProcessors is not supported at this time.");if("object"!==typeof e||null===e)throw new a.InvalidArgumentError("Missing AudioProcessor argument.");if("function"!==typeof e.createProcessedStream)throw new a.InvalidArgumentError("Missing createProcessedStream() method.");if("function"!==typeof e.destroyProcessedStream)throw new a.InvalidArgumentError("Missing destroyProcessedStream() method.");return this._processor=e,this._audioProcessorEventObserver.emit("add"),this._restartStreams()},t.prototype.disconnect=function(e){return this._log.debug(".disconnect",e),this._maybeEnableSound(r.default.SoundName.Disconnect,e)},t.prototype.incoming=function(e){return this._log.debug(".incoming",e),this._maybeEnableSound(r.default.SoundName.Incoming,e)},t.prototype.outgoing=function(e){return this._log.debug(".outgoing",e),this._maybeEnableSound(r.default.SoundName.Outgoing,e)},t.prototype.removeProcessor=function(e){if(this._log.debug(".removeProcessor"),"object"!==typeof e||null===e)throw new a.InvalidArgumentError("Missing AudioProcessor argument.");if(this._processor!==e)throw new a.InvalidArgumentError("Cannot remove an AudioProcessor that has not been previously added.");return this._destroyProcessedStream(),this._processor=null,this._audioProcessorEventObserver.emit("remove"),this._restartStreams()},t.prototype.setAudioConstraints=function(e){return this._log.debug(".setAudioConstraints",e),this._audioConstraints=Object.assign({},e),delete this._audioConstraints.deviceId,this.inputDevice?this._setInputDevice(this.inputDevice.deviceId,!0):Promise.resolve()},t.prototype.setInputDevice=function(e){return this._log.debug(".setInputDevice",e),this._setInputDevice(e,!1)},t.prototype.unsetAudioConstraints=function(){return this._log.debug(".unsetAudioConstraints"),this._audioConstraints=null,this.inputDevice?this._setInputDevice(this.inputDevice.deviceId,!0):Promise.resolve()},t.prototype.unsetInputDevice=function(){var e=this;return this._log.debug(".unsetInputDevice",this.inputDevice),this.inputDevice?(this._destroyProcessedStream(),this._onActiveInputChanged(null).then((function(){e._replaceStream(null),e._inputDevice=null,e._maybeStopPollingVolume()}))):Promise.resolve()},t.prototype._destroyProcessedStream=function(){if(this._processor&&this._processedStream){this._log.info("destroying processed stream");var e=this._processedStream;this._processedStream.getTracks().forEach((function(e){return e.stop()})),this._processedStream=null,this._processor.destroyProcessedStream(e),this._audioProcessorEventObserver.emit("destroy")}},t.prototype._getUnknownDeviceIndex=function(e){var t=e.deviceId,n=e.kind,o=this._unknownDeviceIndexes[n][t];return o||(o=Object.keys(this._unknownDeviceIndexes[n]).length+1,this._unknownDeviceIndexes[n][t]=o),o},t.prototype._initializeEnumeration=function(){var e=this;if(!this._mediaDevices||!this._enumerateDevices)throw new a.NotSupportedError("Enumeration is not supported");this._mediaDevices.addEventListener&&this._mediaDevices.addEventListener("devicechange",this._updateAvailableDevices),this._updateAvailableDevices().then((function(){e.isOutputSelectionSupported&&Promise.all([e.speakerDevices.set("default"),e.ringtoneDevices.set("default")]).catch((function(t){e._log.warn("Warning: Unable to set audio output devices. "+t)}))}))},t.prototype._maybeCreateProcessedStream=function(e){var t=this;return this._processor?(this._log.info("Creating processed stream"),this._processor.createProcessedStream(e).then((function(e){return t._processedStream=e,t._audioProcessorEventObserver.emit("create"),t._processedStream}))):Promise.resolve(e)},t.prototype._maybeEnableSound=function(e,t){return"undefined"!==typeof t&&(this._enabledSounds[e]=t),this._enabledSounds[e]},t.prototype._replaceStream=function(e){this._log.info("Replacing with new stream."),this._selectedInputDeviceStream&&(this._log.info("Old stream detected. Stopping tracks."),this._stopSelectedInputDeviceStream()),this._selectedInputDeviceStream=e},t.prototype._restartStreams=function(){if(this.inputDevice&&this._selectedInputDeviceStream)return this._log.info("Restarting selected input device"),this._setInputDevice(this.inputDevice.deviceId,!0);if(this._defaultInputDeviceStream){var e=this.availableInputDevices.get("default")||Array.from(this.availableInputDevices.values())[0];return this._log.info("Restarting default input device, now becoming selected."),this._setInputDevice(e.deviceId,!0)}return Promise.resolve()},t.prototype._setInputDevice=function(e,t){var n=this;if("string"!==typeof e)return Promise.reject(new a.InvalidArgumentError("Must specify the device to set"));var o=this.availableInputDevices.get(e);if(!o)return Promise.reject(new a.InvalidArgumentError("Device not found: "+e));if(this._log.info("Setting input device. ID: "+e),this._inputDevice&&this._inputDevice.deviceId===e&&this._selectedInputDeviceStream){if(!t)return Promise.resolve();this._log.info("Same track detected on setInputDevice, stopping old tracks."),this._stopSelectedInputDeviceStream()}this._stopDefaultInputDeviceStream();var i={audio:Object.assign({deviceId:{exact:e}},this.audioConstraints)};return this._log.info("setInputDevice: getting new tracks."),this._getUserMedia(i).then((function(e){return n._destroyProcessedStream(),n._maybeCreateProcessedStream(e).then((function(t){return n._log.info("setInputDevice: invoking _onActiveInputChanged."),n._onActiveInputChanged(t).then((function(){n._replaceStream(e),n._inputDevice=o,n._maybeStartPollingVolume()}))}))}))},t.prototype._stopSelectedInputDeviceStream=function(){this._selectedInputDeviceStream&&(this._log.info("Stopping selected device stream"),this._selectedInputDeviceStream.getTracks().forEach((function(e){return e.stop()})))},t.prototype._updateDevices=function(e,t,n){var o=this,i=e.map((function(e){return e.deviceId})),r=Array.from(t.values()).map((function(e){return e.deviceId})),a=[],s=d.difference(r,i);s.forEach((function(e){var o=t.get(e);o&&(t.delete(e),n(o)&&a.push(o))}));var c=!1;e.forEach((function(e){var n=t.get(e.deviceId),i=o._wrapMediaDeviceInfo(e);n&&n.label===i.label||(t.set(e.deviceId,i),c=!0)})),(c||s.length)&&(null!==this.inputDevice&&"default"===this.inputDevice.deviceId&&(this._log.warn("Calling getUserMedia after device change to ensure that the           tracks of the active device (default) have not gone stale."),this._setInputDevice(this.inputDevice.deviceId,!0)),this._log.debug("#deviceChange",a),this.emit("deviceChange",a))},t.prototype._updateVolumeSource=function(){if(this.inputStream&&this._audioContext&&this._inputVolumeAnalyser){this._inputVolumeSource&&this._inputVolumeSource.disconnect();try{this._inputVolumeSource=this._audioContext.createMediaStreamSource(this.inputStream),this._inputVolumeSource.connect(this._inputVolumeAnalyser)}catch(e){this._log.warn("Unable to update volume source",e),delete this._inputVolumeSource}}},t.prototype._wrapMediaDeviceInfo=function(e){var t={deviceId:e.deviceId,groupId:e.groupId,kind:e.kind,label:e.label};if(!t.label)if("default"===t.deviceId)t.label="Default";else{var n=this._getUnknownDeviceIndex(e);t.label="Unknown "+l[t.kind]+" Device "+n}return new u.default(t)},t}(i.EventEmitter);p||(p={}),t.default=p},745:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),i=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},r=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}};Object.defineProperty(t,"__esModule",{value:!0});var a=n(12610),s=function(e){function t(t,n,o){void 0===n&&(n={}),void 0===o&&(o={});var i=e.call(this)||this;return i._audioNode=null,i._loop=!1,i._pendingPlayDeferreds=[],i._sinkId="default",i._src="","string"!==typeof n&&(o=n),i._audioContext=t,i._audioElement=new(o.AudioFactory||Audio),i._bufferPromise=i._createPlayDeferred().promise,i._destination=i._audioContext.destination,i._gainNode=i._audioContext.createGain(),i._gainNode.connect(i._destination),i._XMLHttpRequest=o.XMLHttpRequestFactory||XMLHttpRequest,i.addEventListener("canplaythrough",(function(){i._resolvePlayDeferreds()})),"string"===typeof n&&(i.src=n),i}return o(t,e),Object.defineProperty(t.prototype,"destination",{get:function(){return this._destination},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"loop",{get:function(){return this._loop},set:function(e){var t=this;e||!this.loop||this.paused||this._audioNode.addEventListener("ended",(function e(){t._audioNode.removeEventListener("ended",e),t.pause()})),this._loop=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"muted",{get:function(){return 0===this._gainNode.gain.value},set:function(e){this._gainNode.gain.value=e?0:1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paused",{get:function(){return null===this._audioNode},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"src",{get:function(){return this._src},set:function(e){this._load(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"srcObject",{get:function(){return this._audioElement.srcObject},set:function(e){this._audioElement.srcObject=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sinkId",{get:function(){return this._sinkId},enumerable:!1,configurable:!0}),t.prototype.load=function(){this._load(this._src)},t.prototype.pause=function(){this.paused||(this._audioElement.pause(),this._audioNode.stop(),this._audioNode.disconnect(this._gainNode),this._audioNode=null,this._rejectPlayDeferreds(new Error("The play() request was interrupted by a call to pause().")))},t.prototype.play=function(){return i(this,void 0,void 0,(function(){var e,t=this;return r(this,(function(n){switch(n.label){case 0:return this.paused?[3,2]:[4,this._bufferPromise];case 1:if(n.sent(),!this.paused)return[2];throw new Error("The play() request was interrupted by a call to pause().");case 2:return this._audioNode=this._audioContext.createBufferSource(),this._audioNode.loop=this.loop,this._audioNode.addEventListener("ended",(function(){t._audioNode&&t._audioNode.loop||t.dispatchEvent("ended")})),[4,this._bufferPromise];case 3:if(e=n.sent(),this.paused)throw new Error("The play() request was interrupted by a call to pause().");return this._audioNode.buffer=e,this._audioNode.connect(this._gainNode),this._audioNode.start(),this._audioElement.srcObject?[2,this._audioElement.play()]:[2]}}))}))},t.prototype.setSinkId=function(e){return i(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:if("function"!==typeof this._audioElement.setSinkId)throw new Error("This browser does not support setSinkId.");return e===this.sinkId?[2]:"default"===e?(this.paused||this._gainNode.disconnect(this._destination),this._audioElement.srcObject=null,this._destination=this._audioContext.destination,this._gainNode.connect(this._destination),this._sinkId=e,[2]):[4,this._audioElement.setSinkId(e)];case 1:return t.sent(),this._audioElement.srcObject?[2]:(this._gainNode.disconnect(this._audioContext.destination),this._destination=this._audioContext.createMediaStreamDestination(),this._audioElement.srcObject=this._destination.stream,this._sinkId=e,this._gainNode.connect(this._destination),[2])}}))}))},t.prototype._createPlayDeferred=function(){var e=new a.default;return this._pendingPlayDeferreds.push(e),e},t.prototype._load=function(e){var t=this;this._src&&this._src!==e&&this.pause(),this._src=e,this._bufferPromise=new Promise((function(n,o){return i(t,void 0,void 0,(function(){var t;return r(this,(function(o){switch(o.label){case 0:return e?[4,c(this._audioContext,this._XMLHttpRequest,e)]:[2,this._createPlayDeferred().promise];case 1:return t=o.sent(),this.dispatchEvent("canplaythrough"),n(t),[2]}}))}))}))},t.prototype._rejectPlayDeferreds=function(e){var t=this._pendingPlayDeferreds;t.splice(0,t.length).forEach((function(t){return(0,t.reject)(e)}))},t.prototype._resolvePlayDeferreds=function(e){var t=this._pendingPlayDeferreds;t.splice(0,t.length).forEach((function(t){return(0,t.resolve)(e)}))},t}(n(20668).default);function c(e,t,n){return i(this,void 0,void 0,(function(){var o,i;return r(this,(function(r){switch(r.label){case 0:return(o=new t).open("GET",n,!0),o.responseType="arraybuffer",[4,new Promise((function(e){o.addEventListener("load",e),o.send()}))];case 1:i=r.sent();try{return[2,e.decodeAudioData(i.target.response)]}catch(a){return[2,new Promise((function(t){e.decodeAudioData(i.target.response,t)}))]}return[2]}}))}))}t.default=s},12610:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){var e=this;this.promise=new Promise((function(t,n){e._resolve=t,e._reject=n}))}return Object.defineProperty(e.prototype,"reject",{get:function(){return this._reject},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resolve",{get:function(){return this._resolve},enumerable:!1,configurable:!0}),e}();t.default=n},20668:function(e,t,n){var o=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o};Object.defineProperty(t,"__esModule",{value:!0});var i=n(5939),r=function(){function e(){this._eventEmitter=new i.EventEmitter}return e.prototype.addEventListener=function(e,t){return this._eventEmitter.addListener(e,t)},e.prototype.dispatchEvent=function(e){for(var t,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return(t=this._eventEmitter).emit.apply(t,o([e],n))},e.prototype.removeEventListener=function(e,t){return this._eventEmitter.removeListener(e,t)},e}();t.default=r},19678:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AudioProcessorEventObserver=void 0;var i=n(5939),r=n(24468),a=function(e){function t(){var t=e.call(this)||this;return t._log=new r.default("AudioProcessorEventObserver"),t._log.info("Creating AudioProcessorEventObserver instance"),t.on("enabled",(function(){return t._reEmitEvent("enabled")})),t.on("add",(function(){return t._reEmitEvent("add")})),t.on("remove",(function(){return t._reEmitEvent("remove")})),t.on("create",(function(){return t._reEmitEvent("create-processed-stream")})),t.on("destroy",(function(){return t._reEmitEvent("destroy-processed-stream")})),t}return o(t,e),t.prototype.destroy=function(){this.removeAllListeners()},t.prototype._reEmitEvent=function(e){this._log.info("AudioProcessor:"+e),this.emit("event",{name:e,group:"audio-processor"})},t}(i.EventEmitter);t.AudioProcessorEventObserver=a},77343:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=function(e){function t(t){var n=e.call(this)||this;return Object.defineProperties(n,{_attempts:{value:0,writable:!0},_duration:{enumerable:!1,get:function(){var e=this._min*Math.pow(this._factor,this._attempts);if(this._jitter){var t=Math.random(),n=Math.floor(t*this._jitter*e);e=0===(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this._max)}},_factor:{value:t.factor||2},_jitter:{value:t.jitter>0&&t.jitter<=1?t.jitter:0},_max:{value:t.max||1e4},_min:{value:t.min||100},_timeoutID:{value:null,writable:!0}}),n}return o(t,e),t.prototype.backoff=function(){var e=this,t=this._duration;this._timeoutID&&(clearTimeout(this._timeoutID),this._timeoutID=null),this.emit("backoff",this._attempts,t),this._timeoutID=setTimeout((function(){e.emit("ready",e._attempts,t),e._attempts++}),t)},t.prototype.reset=function(){this._attempts=0,this._timeoutID&&(clearTimeout(this._timeoutID),this._timeoutID=null)},t}(n(5939).EventEmitter);t.default=i},55765:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var r=n(5939),a=n(77343),s=n(56546),c=n(18987),u=n(24468),d=n(48785),l=n(55848),p=n(40230),f=n(49017),h=n(62997),m=n(88342),_=n(68752),g={factor:1.1,jitter:.5,max:3e4,min:1},v={disconnect:!0,info:{code:31003,message:"Connection with Twilio was interrupted.",twilioError:new c.MediaErrors.ConnectionError}},y={packetsLostFraction:{max:"packet-loss",maxAverage:"packets-lost-fraction"}},b={audioInputLevel:"audio-input-level",audioOutputLevel:"audio-output-level",bytesReceived:"bytes-received",bytesSent:"bytes-sent",jitter:"jitter",mos:"mos",rtt:"rtt"},S={max:"high-",maxAverage:"high-",maxDuration:"constant-",min:"low-",minStandardDeviation:"constant-"},w=function(e){function t(n,o){var r=e.call(this)||this;r.parameters={},r._inputVolumeStreak=0,r._isAnswered=!1,r._isCancelled=!1,r._isRejected=!1,r._latestInputVolume=0,r._latestOutputVolume=0,r._log=new u.default("Call"),r._mediaStatus=t.State.Pending,r._messages=new Map,r._metricsSamples=[],r._options={MediaHandler:d.PeerConnection,enableImprovedSignalingErrorPrecision:!1,offerSdp:null,shouldPlayDisconnect:function(){return!0},voiceEventSidGenerator:m.generateVoiceEventSid},r._outputVolumeStreak=0,r._shouldSendHangup=!0,r._signalingStatus=t.State.Pending,r._soundcache=new Map,r._status=t.State.Pending,r._wasConnected=!1,r.toString=function(){return"[Twilio.Call instance]"},r._emitWarning=function(e,t,n,o,i,a){var s=e+"warning"+(i?"-cleared":"-raised");if("constant-audio-input-level"!==t||!r.isMuted()){var c=i?"info":"warning";"constant-audio-output-level"===t&&(c="info");var u={threshold:n};if(o&&(o instanceof Array?u.values=o.map((function(e){return"number"===typeof e?Math.round(100*e)/100:o})):u.value=o),r._publisher.post(c,s,t,{data:u},r),"constant-audio-output-level"!==t){var d=i?"warning-cleared":"warning";r._log.debug("#"+d,t),r.emit(d,t,a&&!i?a:null)}}},r._onAck=function(e){var t=e.acktype,n=e.callsid,o=e.voiceeventsid;r.parameters.CallSid===n?"message"===t&&r._onMessageSent(o):r._log.warn("Received ack from a different callsid: "+n)},r._onAnswer=function(e){"string"===typeof e.reconnect&&(r._signalingReconnectToken=e.reconnect),r._isAnswered&&r._status!==t.State.Reconnecting||(r._setCallSid(e),r._isAnswered=!0,r._maybeTransitionToOpen())},r._onCancel=function(e){var n=e.callsid;r.parameters.CallSid===n&&(r._isCancelled=!0,r._publisher.info("connection","cancel",null,r),r._cleanupEventListeners(),r._mediaHandler.close(),r._status=t.State.Closed,r._log.debug("#cancel"),r.emit("cancel"),r._pstream.removeListener("cancel",r._onCancel))},r._onConnected=function(){r._log.info("Received connected from pstream"),r._signalingReconnectToken&&r._mediaHandler.version&&r._pstream.reconnect(r._mediaHandler.version.getSDP(),r.parameters.CallSid,r._signalingReconnectToken)},r._onHangup=function(e){if(r.status()!==t.State.Closed){if(e.callsid&&(r.parameters.CallSid||r.outboundConnectionId)){if(e.callsid!==r.parameters.CallSid&&e.callsid!==r.outboundConnectionId)return}else if(e.callsid)return;if(r._log.info("Received HANGUP from gateway"),e.error){var n=e.error.code,o=c.getPreciseSignalingErrorByCode(r._options.enableImprovedSignalingErrorPrecision,n),i="undefined"!==typeof o?new o(e.error.message):new c.GeneralErrors.ConnectionError("Error sent from gateway in HANGUP");r._log.error("Received an error from the gateway:",i),r._log.debug("#error",i),r.emit("error",i)}r._shouldSendHangup=!1,r._publisher.info("connection","disconnected-by-remote",null,r),r._disconnect(null,!0),r._cleanupEventListeners()}},r._onMediaFailure=function(e){var n=t.MediaFailure,o=n.ConnectionDisconnected,i=n.ConnectionFailed,a=n.IceGatheringFailed,s=n.LowBytes,u=e===i||e===a;if(!h.isChrome(window,window.navigator)&&e===i)return r._mediaHandler.onerror(v);if(r._mediaStatus!==t.State.Reconnecting){var d=r._mediaHandler.version.pc,l=d&&"disconnected"===d.iceConnectionState,p=r._monitor.hasActiveWarning("bytesSent","min")||r._monitor.hasActiveWarning("bytesReceived","min");if(e===s&&l||e===o&&p||u){var f=new c.MediaErrors.ConnectionError("Media connection failed.");r._log.warn("ICE Connection disconnected."),r._publisher.warn("connection","error",f,r),r._publisher.info("connection","reconnecting",null,r),r._mediaReconnectStartTime=Date.now(),r._status=t.State.Reconnecting,r._mediaStatus=t.State.Reconnecting,r._mediaReconnectBackoff.reset(),r._mediaReconnectBackoff.backoff(),r._log.debug("#reconnecting"),r.emit("reconnecting",f)}}else if(u){if(Date.now()-r._mediaReconnectStartTime>g.max)return r._log.warn("Exceeded max ICE retries"),r._mediaHandler.onerror(v);try{r._mediaReconnectBackoff.backoff()}catch(m){if(!m.message||"Backoff in progress."!==m.message)throw m}}},r._onMediaReconnected=function(){r._mediaStatus===t.State.Reconnecting&&(r._log.info("ICE Connection reestablished."),r._mediaStatus=t.State.Open,r._signalingStatus===t.State.Open&&(r._publisher.info("connection","reconnected",null,r),r._log.debug("#reconnected"),r.emit("reconnected"),r._status=t.State.Open))},r._onMessageReceived=function(e){var t=e.callsid,n=e.content,o=e.contenttype,i=e.messagetype,a=e.voiceeventsid;if(r.parameters.CallSid===t){var s={content:n,contentType:o,messageType:i,voiceEventSid:a};r._log.debug("#messageReceived",JSON.stringify(s)),r.emit("messageReceived",s)}else r._log.warn("Received a message from a different callsid: "+t)},r._onMessageSent=function(e){if(r._messages.has(e)){var t=r._messages.get(e);r._messages.delete(e),r._log.debug("#messageSent",JSON.stringify(t)),r.emit("messageSent",t)}else r._log.warn("Received a messageSent with a voiceEventSid that doesn't exists: "+e)},r._onRinging=function(e){if(r._setCallSid(e),r._status===t.State.Connecting||r._status===t.State.Ringing){var n=!!e.sdp;r._status=t.State.Ringing,r._publisher.info("connection","outgoing-ringing",{hasEarlyMedia:n},r),r._log.debug("#ringing"),r.emit("ringing",n)}},r._onRTCSample=function(e){var t=i(i({},e),{inputVolume:r._latestInputVolume,outputVolume:r._latestOutputVolume});r._codec=t.codecName,r._metricsSamples.push(t),r._metricsSamples.length>=10&&r._publishMetrics(),r.emit("sample",e)},r._onSignalingError=function(e){var t=e.callsid,n=e.voiceeventsid;r.parameters.CallSid===t?n&&r._messages.has(n)&&(r._messages.delete(n),r._log.warn("Received an error while sending a message.",e)):r._log.warn("Received an error from a different callsid: "+t)},r._onSignalingReconnected=function(){r._signalingStatus===t.State.Reconnecting&&(r._log.info("Signaling Connection reestablished."),r._signalingStatus=t.State.Open,r._mediaStatus===t.State.Open&&(r._publisher.info("connection","reconnected",null,r),r._log.debug("#reconnected"),r.emit("reconnected"),r._status=t.State.Open))},r._onTransportClose=function(){r._log.error("Received transportClose from pstream"),r._log.debug("#transportClose"),r.emit("transportClose"),r._signalingReconnectToken?(r._status=t.State.Reconnecting,r._signalingStatus=t.State.Reconnecting,r._log.debug("#reconnecting"),r.emit("reconnecting",new c.SignalingErrors.ConnectionDisconnected)):(r._status=t.State.Closed,r._signalingStatus=t.State.Closed)},r._reemitWarning=function(e,t){var n,o=/^audio/.test(e.name)?"audio-level-":"network-quality-",i=S[e.threshold.name];e.name in y?n=y[e.name][e.threshold.name]:e.name in b&&(n=b[e.name]);var a=i+n;r._emitWarning(o,a,e.threshold.value,e.values||e.value,t,e)},r._reemitWarningCleared=function(e){r._reemitWarning(e,!0)},r._isUnifiedPlanDefault=n.isUnifiedPlanDefault,r._soundcache=n.soundcache,"function"===typeof n.onIgnore&&(r._onIgnore=n.onIgnore);var p=o&&o.twimlParams||{};r.customParameters=new Map(Object.entries(p).map((function(e){var t=e[0],n=e[1];return[t,String(n)]}))),Object.assign(r._options,o),r._options.callParameters&&(r.parameters=r._options.callParameters),r._options.reconnectToken&&(r._signalingReconnectToken=r._options.reconnectToken),r._voiceEventSidGenerator=r._options.voiceEventSidGenerator||m.generateVoiceEventSid,r._direction=r.parameters.CallSid&&!r._options.reconnectCallSid?t.CallDirection.Incoming:t.CallDirection.Outgoing,r.parameters?r.callerInfo=r.parameters.StirStatus?{isVerified:"TN-Validation-Passed-A"===r.parameters.StirStatus}:null:r.callerInfo=null,r._mediaReconnectBackoff=new a.default(g),r._mediaReconnectBackoff.on("ready",(function(){return r._mediaHandler.iceRestart()})),r.outboundConnectionId="TJSxxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}));var _=r._publisher=n.publisher;r._direction===t.CallDirection.Incoming?_.info("connection","incoming",null,r):_.info("connection","outgoing",{preflight:r._options.preflight,reconnect:!!r._options.reconnectCallSid},r);var w=r._monitor=new(r._options.StatsMonitor||f.default);return w.on("sample",r._onRTCSample),w.disableWarnings(),setTimeout((function(){return w.enableWarnings()}),5e3),w.on("warning",(function(e,n){"bytesSent"!==e.name&&"bytesReceived"!==e.name||r._onMediaFailure(t.MediaFailure.LowBytes),r._reemitWarning(e,n)})),w.on("warning-cleared",(function(e){r._reemitWarningCleared(e)})),r._mediaHandler=new r._options.MediaHandler(n.audioHelper,n.pstream,{RTCPeerConnection:r._options.RTCPeerConnection,codecPreferences:r._options.codecPreferences,dscp:r._options.dscp,forceAggressiveIceNomination:r._options.forceAggressiveIceNomination,isUnifiedPlan:r._isUnifiedPlanDefault,maxAverageBitrate:r._options.maxAverageBitrate}),r.on("volume",(function(e,t){r._inputVolumeStreak=r._checkVolume(e,r._inputVolumeStreak,r._latestInputVolume,"input"),r._outputVolumeStreak=r._checkVolume(t,r._outputVolumeStreak,r._latestOutputVolume,"output"),r._latestInputVolume=e,r._latestOutputVolume=t})),r._mediaHandler.onaudio=function(e){r._log.debug("#audio"),r.emit("audio",e)},r._mediaHandler.onvolume=function(e,t,n,o){w.addVolumes(n/255*32767,o/255*32767),r.emit("volume",e,t)},r._mediaHandler.ondtlstransportstatechange=function(e){var t="failed"===e?"error":"debug";r._publisher.post(t,"dtls-transport-state",e,null,r)},r._mediaHandler.onpcconnectionstatechange=function(e){var t="debug",n=r._mediaHandler.getRTCDtlsTransport();"failed"===e&&(t=n&&"failed"===n.state?"error":"warning"),r._publisher.post(t,"pc-connection-state",e,null,r)},r._mediaHandler.onicecandidate=function(e){var t=new l.IceCandidate(e).toPayload();r._publisher.debug("ice-candidate","ice-candidate",t,r)},r._mediaHandler.onselectedcandidatepairchange=function(e){var t=new l.IceCandidate(e.local).toPayload(),n=new l.IceCandidate(e.remote,!0).toPayload();r._publisher.debug("ice-candidate","selected-ice-candidate-pair",{local_candidate:t,remote_candidate:n},r)},r._mediaHandler.oniceconnectionstatechange=function(e){var t="failed"===e?"error":"debug";r._publisher.post(t,"ice-connection-state",e,null,r)},r._mediaHandler.onicegatheringfailure=function(e){r._publisher.warn("ice-gathering-state",e,null,r),r._onMediaFailure(t.MediaFailure.IceGatheringFailed)},r._mediaHandler.onicegatheringstatechange=function(e){r._publisher.debug("ice-gathering-state",e,null,r)},r._mediaHandler.onsignalingstatechange=function(e){r._publisher.debug("signaling-state",e,null,r)},r._mediaHandler.ondisconnected=function(e){r._log.warn(e),r._publisher.warn("network-quality-warning-raised","ice-connectivity-lost",{message:e},r),r._log.debug("#warning","ice-connectivity-lost"),r.emit("warning","ice-connectivity-lost"),r._onMediaFailure(t.MediaFailure.ConnectionDisconnected)},r._mediaHandler.onfailed=function(e){r._onMediaFailure(t.MediaFailure.ConnectionFailed)},r._mediaHandler.onconnected=function(){r._status===t.State.Reconnecting&&r._onMediaReconnected()},r._mediaHandler.onreconnected=function(e){r._log.info(e),r._publisher.info("network-quality-warning-cleared","ice-connectivity-lost",{message:e},r),r._log.debug("#warning-cleared","ice-connectivity-lost"),r.emit("warning-cleared","ice-connectivity-lost"),r._onMediaReconnected()},r._mediaHandler.onerror=function(e){!0===e.disconnect&&r._disconnect(e.info&&e.info.message);var t=e.info.twilioError||new c.GeneralErrors.UnknownError(e.info.message);r._log.error("Received an error from MediaStream:",e),r._log.debug("#error",t),r.emit("error",t)},r._mediaHandler.onopen=function(){r._status!==t.State.Open&&r._status!==t.State.Reconnecting&&(r._status===t.State.Ringing||r._status===t.State.Connecting?(r.mute(r._mediaHandler.isMuted),r._mediaStatus=t.State.Open,r._maybeTransitionToOpen()):r._mediaHandler.close())},r._mediaHandler.onclose=function(){r._status=t.State.Closed,r._options.shouldPlayDisconnect&&r._options.shouldPlayDisconnect()&&!r._isCancelled&&!r._isRejected&&r._soundcache.get(s.default.SoundName.Disconnect).play(),w.disable(),r._publishMetrics(),r._isCancelled||r._isRejected||(r._log.debug("#disconnect"),r.emit("disconnect",r))},r._pstream=n.pstream,r._pstream.on("ack",r._onAck),r._pstream.on("cancel",r._onCancel),r._pstream.on("error",r._onSignalingError),r._pstream.on("ringing",r._onRinging),r._pstream.on("transportClose",r._onTransportClose),r._pstream.on("connected",r._onConnected),r._pstream.on("message",r._onMessageReceived),r.on("error",(function(e){r._publisher.error("connection","error",{code:e.code,message:e.message},r),r._pstream&&"disconnected"===r._pstream.status&&r._cleanupEventListeners()})),r.on("disconnect",(function(){r._cleanupEventListeners()})),r}return o(t,e),Object.defineProperty(t.prototype,"direction",{get:function(){return this._direction},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"codec",{get:function(){return this._codec},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"connectToken",{get:function(){var e=this,t=this._signalingReconnectToken,n=this.parameters&&this.parameters.CallSid?this.parameters.CallSid:void 0;if(t&&n){var o=this.customParameters&&"function"===typeof this.customParameters.keys?Array.from(this.customParameters.keys()).reduce((function(t,n){return t[n]=e.customParameters.get(n),t}),{}):{},i=this.parameters||{};return btoa(encodeURIComponent(JSON.stringify({customParameters:o,parameters:i,signalingReconnectToken:t})))}},enumerable:!1,configurable:!0}),t.prototype._setInputTracksFromStream=function(e){return this._mediaHandler.setInputTracksFromStream(e)},t.prototype._setSinkIds=function(e){return this._mediaHandler._setSinkIds(e)},t.prototype.accept=function(e){var n=this;if(this._log.debug(".accept",e),this._status===t.State.Pending){var o=(e=e||{}).rtcConfiguration||this._options.rtcConfiguration,i=e.rtcConstraints||this._options.rtcConstraints||{},r={audio:"undefined"===typeof i.audio||i.audio};this._status=t.State.Connecting;this._options.beforeAccept&&this._options.beforeAccept(this);var a="function"===typeof this._options.getInputStream&&this._options.getInputStream();(a?this._mediaHandler.setInputTracksFromStream(a):this._mediaHandler.openDefaultDeviceWithConstraints(r)).then((function(){n._publisher.info("get-user-media","succeeded",{data:{audioConstraints:r}},n),function(){if(n._status!==t.State.Connecting)return n._cleanupEventListeners(),void n._mediaHandler.close();var e=function(e){var o=n._direction===t.CallDirection.Incoming?"accepted-by-local":"accepted-by-remote";n._publisher.info("connection",o,null,n);var i=p.getPreferredCodecInfo(n._mediaHandler.version.getSDP()),r=i.codecName,a=i.codecParams;n._publisher.info("settings","codec",{codec_params:a,selected_codec:r},n),n._monitor.enable(e)},i="function"===typeof n._options.getSinkIds&&n._options.getSinkIds();if(Array.isArray(i)&&n._mediaHandler._setSinkIds(i).catch((function(){})),n._pstream.addListener("hangup",n._onHangup),n._direction===t.CallDirection.Incoming)n._isAnswered=!0,n._pstream.on("answer",n._onAnswer),n._mediaHandler.answerIncomingCall(n.parameters.CallSid,n._options.offerSdp,o,e);else{var r=Array.from(n.customParameters.entries()).map((function(e){return encodeURIComponent(e[0])+"="+encodeURIComponent(e[1])})).join("&");n._pstream.on("answer",n._onAnswer),n._mediaHandler.makeOutgoingCall(r,n._signalingReconnectToken,n._options.reconnectCallSid||n.outboundConnectionId,o,e)}}()}),(function(e){var t;31208===e.code||-1!==["PermissionDeniedError","NotAllowedError"].indexOf(e.name)?(t=new c.UserMediaErrors.PermissionDeniedError,n._publisher.error("get-user-media","denied",{data:{audioConstraints:r,error:e}},n)):(t=new c.UserMediaErrors.AcquisitionFailedError,n._publisher.error("get-user-media","failed",{data:{audioConstraints:r,error:e}},n)),n._disconnect(),n._log.debug("#error",e),n.emit("error",t)}))}else this._log.debug(".accept noop. status is '"+this._status+"'")},t.prototype.disconnect=function(){this._log.debug(".disconnect"),this._disconnect()},t.prototype.getLocalStream=function(){return this._mediaHandler&&this._mediaHandler.stream},t.prototype.getRemoteStream=function(){return this._mediaHandler&&this._mediaHandler._remoteStream},t.prototype.ignore=function(){this._log.debug(".ignore"),this._status===t.State.Pending?(this._status=t.State.Closed,this._mediaHandler.ignore(this.parameters.CallSid),this._publisher.info("connection","ignored-by-local",null,this),this._onIgnore&&this._onIgnore()):this._log.debug(".ignore noop. status is '"+this._status+"'")},t.prototype.isMuted=function(){return this._mediaHandler.isMuted},t.prototype.mute=function(e){void 0===e&&(e=!0),this._log.debug(".mute",e);var t=this._mediaHandler.isMuted;this._mediaHandler.mute(e);var n=this._mediaHandler.isMuted;t!==n&&(this._publisher.info("connection",n?"muted":"unmuted",null,this),this._log.debug("#mute",n),this.emit("mute",n,this))},t.prototype.postFeedback=function(e,n){if("undefined"===typeof e||null===e)return this._postFeedbackDeclined();if(!Object.values(t.FeedbackScore).includes(e))throw new c.InvalidArgumentError("Feedback score must be one of: "+Object.values(t.FeedbackScore));if("undefined"!==typeof n&&null!==n&&!Object.values(t.FeedbackIssue).includes(n))throw new c.InvalidArgumentError("Feedback issue must be one of: "+Object.values(t.FeedbackIssue));return this._publisher.info("feedback","received",{issue_name:n,quality_score:e},this,!0)},t.prototype.reject=function(){this._log.debug(".reject"),this._status===t.State.Pending?(this._isRejected=!0,this._pstream.reject(this.parameters.CallSid),this._mediaHandler.reject(this.parameters.CallSid),this._publisher.info("connection","rejected-by-local",null,this),this._cleanupEventListeners(),this._mediaHandler.close(),this._status=t.State.Closed,this._log.debug("#reject"),this.emit("reject")):this._log.debug(".reject noop. status is '"+this._status+"'")},t.prototype.sendDigits=function(e){var t=this;if(this._log.debug(".sendDigits",e),e.match(/[^0-9*#w]/))throw new c.InvalidArgumentError("Illegal character passed into sendDigits");var n=this._options.customSounds||{},o=[];e.split("").forEach((function(e){var t="w"!==e?"dtmf"+e:"";"dtmf*"===t&&(t="dtmfs"),"dtmf#"===t&&(t="dtmfh"),o.push(t)}));var i=function(){var e=o.shift();e&&(t._options.dialtonePlayer&&!n[e]?t._options.dialtonePlayer.play(e):t._soundcache.get(e).play()),o.length&&setTimeout((function(){return i()}),200)};i();var r=this._mediaHandler.getOrCreateDTMFSender();if(r){if(!("canInsertDTMF"in r)||r.canInsertDTMF)return this._log.info("Sending digits using RTCDTMFSender"),void function e(t){if(t.length){var n=t.shift();n&&n.length&&r.insertDTMF(n,160,70),setTimeout(e.bind(null,t),500)}}(e.split("w"));this._log.info("RTCDTMFSender cannot insert DTMF")}if(this._log.info("Sending digits over PStream"),null!==this._pstream&&"disconnected"!==this._pstream.status)this._pstream.dtmf(this.parameters.CallSid,e);else{var a=new c.GeneralErrors.ConnectionError("Could not send DTMF: Signaling channel is disconnected");this._log.debug("#error",a),this.emit("error",a)}},t.prototype.sendMessage=function(e){this._log.debug(".sendMessage",JSON.stringify(e));var t=e.content,n=e.contentType,o=e.messageType;if("undefined"===typeof t||null===t)throw new c.InvalidArgumentError("`content` is empty");if("string"!==typeof o)throw new c.InvalidArgumentError("`messageType` must be an enumeration value of `Call.MessageType` or a string.");if(0===o.length)throw new c.InvalidArgumentError("`messageType` must be a non-empty string.");if(null===this._pstream)throw new c.InvalidStateError("Could not send CallMessage; Signaling channel is disconnected");var i=this.parameters.CallSid;if("undefined"===typeof this.parameters.CallSid)throw new c.InvalidStateError("Could not send CallMessage; Call has no CallSid");var r=this._voiceEventSidGenerator();return this._messages.set(r,{content:t,contentType:n,messageType:o,voiceEventSid:r}),this._pstream.sendMessage(i,t,n,o,r),r},t.prototype.status=function(){return this._status},t.prototype._checkVolume=function(e,t,n,o){var i=t>=10,r=0;return n===e&&(r=t),r>=10?this._emitWarning("audio-level-","constant-audio-"+o+"-level",10,r,!1):i&&this._emitWarning("audio-level-","constant-audio-"+o+"-level",10,r,!0),r},t.prototype._cleanupEventListeners=function(){var e=this,t=function(){e._pstream&&(e._pstream.removeListener("ack",e._onAck),e._pstream.removeListener("answer",e._onAnswer),e._pstream.removeListener("cancel",e._onCancel),e._pstream.removeListener("error",e._onSignalingError),e._pstream.removeListener("hangup",e._onHangup),e._pstream.removeListener("ringing",e._onRinging),e._pstream.removeListener("transportClose",e._onTransportClose),e._pstream.removeListener("connected",e._onConnected),e._pstream.removeListener("message",e._onMessageReceived))};t(),setTimeout(t,0)},t.prototype._createMetricPayload=function(){var e={call_sid:this.parameters.CallSid,dscp:!!this._options.dscp,sdk_version:_.RELEASE_VERSION};return this._options.gateway&&(e.gateway=this._options.gateway),e.direction=this._direction,e},t.prototype._disconnect=function(e,n){if(e="string"===typeof e?e:null,this._status===t.State.Open||this._status===t.State.Connecting||this._status===t.State.Reconnecting||this._status===t.State.Ringing){if(this._log.info("Disconnecting..."),null!==this._pstream&&"disconnected"!==this._pstream.status&&this._shouldSendHangup){var o=this.parameters.CallSid||this.outboundConnectionId;o&&this._pstream.hangup(o,e)}this._cleanupEventListeners(),this._mediaHandler.close(),n||this._publisher.info("connection","disconnected-by-local",null,this)}},t.prototype._maybeTransitionToOpen=function(){this._wasConnected;this._isAnswered&&(this._onSignalingReconnected(),this._signalingStatus=t.State.Open,this._mediaHandler&&"open"===this._mediaHandler.status&&(this._status=t.State.Open,this._wasConnected||(this._wasConnected=!0,this._log.debug("#accept"),this.emit("accept",this))))},t.prototype._postFeedbackDeclined=function(){return this._publisher.info("feedback","received-none",null,this,!0)},t.prototype._publishMetrics=function(){var e=this;0!==this._metricsSamples.length&&this._publisher.postMetrics("quality-metrics-samples","metrics-sample",this._metricsSamples.splice(0),this._createMetricPayload(),this).catch((function(t){e._log.warn("Unable to post metrics to Insights. Received error:",t)}))},t.prototype._setCallSid=function(e){var t=e.callsid;t&&(this.parameters.CallSid=t,this._mediaHandler.callSid=t)},t.toString=function(){return"[Twilio.Call class]"},t}(r.EventEmitter);!function(e){!function(e){e.Closed="closed",e.Connecting="connecting",e.Open="open",e.Pending="pending",e.Reconnecting="reconnecting",e.Ringing="ringing"}(e.State||(e.State={})),function(e){e.AudioLatency="audio-latency",e.ChoppyAudio="choppy-audio",e.DroppedCall="dropped-call",e.Echo="echo",e.NoisyCall="noisy-call",e.OneWayAudio="one-way-audio"}(e.FeedbackIssue||(e.FeedbackIssue={})),function(e){e[e.One=1]="One",e[e.Two=2]="Two",e[e.Three=3]="Three",e[e.Four=4]="Four",e[e.Five=5]="Five"}(e.FeedbackScore||(e.FeedbackScore={})),function(e){e.Incoming="INCOMING",e.Outgoing="OUTGOING"}(e.CallDirection||(e.CallDirection={})),function(e){e.Opus="opus",e.PCMU="pcmu"}(e.Codec||(e.Codec={})),function(e){e.None="none",e.Timeout="timeout"}(e.IceGatheringFailureReason||(e.IceGatheringFailureReason={})),function(e){e.ConnectionDisconnected="ConnectionDisconnected",e.ConnectionFailed="ConnectionFailed",e.IceGatheringFailed="IceGatheringFailed",e.LowBytes="LowBytes"}(e.MediaFailure||(e.MediaFailure={})),function(e){e.UserDefinedMessage="user-defined-message"}(e.MessageType||(e.MessageType={}))}(w||(w={})),t.default=w},68752:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SOUNDS_BASE_URL=t.RELEASE_VERSION=t.PACKAGE_NAME=t.ECHO_TEST_DURATION=t.COWBELL_AUDIO_URL=void 0;t.PACKAGE_NAME="@twilio/voice-sdk";var n="2.11.0";t.RELEASE_VERSION=n;var o="https://sdk.twilio.com/js/client/sounds/releases/1.0.0";t.SOUNDS_BASE_URL=o;var i=o+"/cowbell.mp3?cache="+n;t.COWBELL_AUDIO_URL=i;t.ECHO_TEST_DURATION=2e4},36135:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){var e=this;this._promise=new Promise((function(t,n){e._resolve=t,e._reject=n}))}return Object.defineProperty(e.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),e.prototype.reject=function(e){this._reject(e)},e.prototype.resolve=function(e){this._resolve(e)},e}();t.default=n},56546:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)},r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}};Object.defineProperty(t,"__esModule",{value:!0});var s=n(5939),c=n(831),u=n(45996),d=n(19678),l=n(55765),p=n(68752),f=n(19326),h=n(18987),m=n(40202),_=n(24468),g=n(37919),v=n(11149),y=n(59057),b=n(48785),S=n(60658),w=n(32769),C=n(62997),E=n(88342),P=function(e){function t(n,o){var i;void 0===o&&(o={});var s=e.call(this)||this;if(s._activeCall=null,s._audio=null,s._audioProcessorEventObserver=null,s._callInputStream=null,s._calls=[],s._callSinkIds=["default"],s._chunderURIs=[],s._defaultOptions={allowIncomingWhileBusy:!1,closeProtection:!1,codecPreferences:[l.default.Codec.PCMU,l.default.Codec.Opus],dscp:!0,enableImprovedSignalingErrorPrecision:!1,forceAggressiveIceNomination:!1,logLevel:c.levels.ERROR,maxCallSignalingTimeoutMs:0,preflight:!1,sounds:{},tokenRefreshMs:1e4,voiceEventSidGenerator:E.generateVoiceEventSid},s._edge=null,s._home=null,s._identity=null,s._log=new _.default("Device"),s._options={},s._preferredURI=null,s._publisher=null,s._region=null,s._regTimer=null,s._shouldReRegister=!1,s._soundcache=new Map,s._state=t.State.Unregistered,s._stateEventMapping=((i={})[t.State.Destroyed]=t.EventName.Destroyed,i[t.State.Unregistered]=t.EventName.Unregistered,i[t.State.Registering]=t.EventName.Registering,i[t.State.Registered]=t.EventName.Registered,i),s._stream=null,s._streamConnectedPromise=null,s._tokenWillExpireTimeout=null,s._createDefaultPayload=function(e){var t={aggressive_nomination:s._options.forceAggressiveIceNomination,browser_extension:s._isBrowserExtension,dscp:!!s._options.dscp,ice_restart_enabled:!0,platform:b.getMediaEngine(),sdk_version:p.RELEASE_VERSION};function n(e,n){n&&(t[e]=n)}if(e){var o=e.parameters.CallSid;n("call_sid",/^TJ/.test(o)?void 0:o),n("temp_call_sid",e.outboundConnectionId),n("audio_codec",e.codec),t.direction=e.direction}return n("gateway",s._stream&&s._stream.gateway),n("region",s._stream&&s._stream.region),t},s._onSignalingClose=function(){s._stream=null,s._streamConnectedPromise=null},s._onSignalingConnected=function(e){var t,n=y.getRegionShortcode(e.region);if(s._edge=e.edge||y.regionToEdge[n]||e.region,s._region=n||e.region,s._home=e.home,null===(t=s._publisher)||void 0===t||t.setHost(y.createEventGatewayURI(e.home)),e.token&&(s._identity=e.token.identity,"number"===typeof e.token.ttl&&"number"===typeof s._options.tokenRefreshMs)){var o=1e3*e.token.ttl,i=Math.max(0,o-s._options.tokenRefreshMs);s._tokenWillExpireTimeout=setTimeout((function(){s._log.debug("#tokenWillExpire"),s.emit("tokenWillExpire",s),s._tokenWillExpireTimeout&&(clearTimeout(s._tokenWillExpireTimeout),s._tokenWillExpireTimeout=null)}),i)}var r=y.getChunderURIs(s._edge);if(r.length>0){var a=r[0];s._preferredURI=y.createSignalingEndpointURL(a)}else s._log.warn("Could not parse a preferred URI from the stream#connected event.");s._shouldReRegister&&s.register()},s._onSignalingError=function(e){if("object"===typeof e){var n=e.error,o=e.callsid;if("object"===typeof n){var i="string"===typeof o&&s._findCall(o)||void 0,r=n.code,a=n.message,c=n.twilioError;if("number"===typeof r)if(31201===r)c=new h.AuthorizationErrors.AuthenticationFailed(n);else if(31204===r)c=new h.AuthorizationErrors.AccessTokenInvalid(n);else if(31205===r)s._stopRegistrationTimer(),c=new h.AuthorizationErrors.AccessTokenExpired(n);else{var u=h.getPreciseSignalingErrorByCode(!!s._options.enableImprovedSignalingErrorPrecision,r);"undefined"!==typeof u&&(c=new u(n))}c||(s._log.error("Unknown signaling error: ",n),c=new h.GeneralErrors.UnknownError(a,n)),s._log.error("Received error: ",c),s._log.debug("#error",n),s.emit(t.EventName.Error,c,i)}}},s._onSignalingInvite=function(e){return r(s,void 0,void 0,(function(){var n,o,i,r,s,c,u=this;return a(this,(function(a){switch(a.label){case 0:return(n=!!this._activeCall)&&!this._options.allowIncomingWhileBusy?(this._log.info("Device busy; ignoring incoming invite"),[2]):e.callsid&&e.sdp?((o=e.parameters||{}).CallSid=o.CallSid||e.callsid,i=Object.assign({},C.queryToJson(o.Params)),[4,this._makeCall(i,{callParameters:o,enableImprovedSignalingErrorPrecision:!!this._options.enableImprovedSignalingErrorPrecision,offerSdp:e.sdp,reconnectToken:e.reconnect,voiceEventSidGenerator:this._options.voiceEventSidGenerator})]):(this._log.debug("#error",e),this.emit(t.EventName.Error,new h.ClientErrors.BadRequest("Malformed invite from gateway")),[2]);case 1:return r=a.sent(),this._calls.push(r),r.once("accept",(function(){u._soundcache.get(t.SoundName.Incoming).stop(),u._publishNetworkChange()})),s=(null===(c=this._audio)||void 0===c?void 0:c.incoming())&&!n?function(){return u._soundcache.get(t.SoundName.Incoming).play()}:function(){return Promise.resolve()},this._showIncomingCall(r,s),[2]}}))}))},s._onSignalingOffline=function(){s._log.info("Stream is offline"),s._edge=null,s._region=null,s._shouldReRegister=s.state!==t.State.Unregistered,s._setState(t.State.Unregistered)},s._onSignalingReady=function(){s._log.info("Stream is ready"),s._setState(t.State.Registered)},s._publishNetworkChange=function(){s._activeCall&&s._networkInformation&&s._publisher.info("network-information","network-change",{connection_type:s._networkInformation.type,downlink:s._networkInformation.downlink,downlinkMax:s._networkInformation.downlinkMax,effective_type:s._networkInformation.effectiveType,rtt:s._networkInformation.rtt},s._activeCall)},s._updateInputStream=function(e){var t=s._activeCall;return t&&!e?Promise.reject(new h.InvalidStateError("Cannot unset input device while a call is in progress.")):(s._callInputStream=e,t?t._setInputTracksFromStream(e):Promise.resolve())},s._updateSinkIds=function(e,t){return("ringtone"===e?s._updateRingtoneSinkIds(t):s._updateSpeakerSinkIds(t)).then((function(){s._publisher.info("audio",e+"-devices-set",{audio_device_ids:t},s._activeCall)}),(function(n){throw s._publisher.error("audio",e+"-devices-set-failed",{audio_device_ids:t,message:n.message},s._activeCall),n}))},s._setupLoglevel(o.logLevel),s._logOptions("constructor",o),s.updateToken(n),C.isLegacyEdge())throw new h.NotSupportedError("Microsoft Edge Legacy (https://support.microsoft.com/en-us/help/4533505/what-is-microsoft-edge-legacy) is deprecated and will not be able to connect to Twilio to make or receive calls after September 1st, 2020. Please see this documentation for a list of supported browsers https://www.twilio.com/docs/voice/client/javascript#supported-browsers");if(!t.isSupported&&o.ignoreBrowserSupport){if(window&&window.location&&"http:"===window.location.protocol)throw new h.NotSupportedError("twilio.js wasn't able to find WebRTC browser support.           This is most likely because this page is served over http rather than https,           which does not support WebRTC in many browsers. Please load this page over https and           try again.");throw new h.NotSupportedError("twilio.js 1.3+ SDKs require WebRTC browser support.         For more information, see <https://www.twilio.com/docs/api/client/twilio-js>.         If you have any questions about this announcement, please contact         Twilio Support at <<EMAIL>>.")}var u=globalThis,d=u.msBrowser||u.browser||u.chrome;if(s._isBrowserExtension=!!d&&!!d.runtime&&!!d.runtime.id||!!u.safari&&!!u.safari.extension,s._isBrowserExtension&&s._log.info("Running as browser extension."),navigator){var m=navigator;s._networkInformation=m.connection||m.mozConnection||m.webkitConnection}return s._networkInformation&&"function"===typeof s._networkInformation.addEventListener&&s._networkInformation.addEventListener("change",s._publishNetworkChange),t._getOrCreateAudioContext(),t._audioContext&&(t._dialtonePlayer||(t._dialtonePlayer=new f.default(t._audioContext))),"undefined"===typeof t._isUnifiedPlanDefault&&(t._isUnifiedPlanDefault="undefined"!==typeof window&&"undefined"!==typeof RTCPeerConnection&&"undefined"!==typeof RTCRtpTransceiver&&C.isUnifiedPlanDefault(window,window.navigator,RTCPeerConnection,RTCRtpTransceiver)),s._boundDestroy=s.destroy.bind(s),s._boundConfirmClose=s._confirmClose.bind(s),"undefined"!==typeof window&&window.addEventListener&&(window.addEventListener("unload",s._boundDestroy),window.addEventListener("pagehide",s._boundDestroy)),s.updateOptions(o),s}return o(t,e),Object.defineProperty(t,"audioContext",{get:function(){return t._audioContext},enumerable:!1,configurable:!0}),Object.defineProperty(t,"extension",{get:function(){var e,t,n="undefined"!==typeof document?document.createElement("audio"):{canPlayType:!1};try{e=n.canPlayType&&!!n.canPlayType("audio/mpeg").replace(/no/,"")}catch(o){e=!1}try{t=n.canPlayType&&!!n.canPlayType("audio/ogg;codecs='vorbis'").replace(/no/,"")}catch(o){t=!1}return t&&!e?"ogg":"mp3"},enumerable:!1,configurable:!0}),Object.defineProperty(t,"isSupported",{get:function(){return b.enabled()},enumerable:!1,configurable:!0}),Object.defineProperty(t,"packageName",{get:function(){return p.PACKAGE_NAME},enumerable:!1,configurable:!0}),t.runPreflight=function(e,n){return new g.PreflightTest(e,i({audioContext:t._getOrCreateAudioContext()},n))},t.toString=function(){return"[Twilio.Device class]"},Object.defineProperty(t,"version",{get:function(){return p.RELEASE_VERSION},enumerable:!1,configurable:!0}),t._getOrCreateAudioContext=function(){return t._audioContext||("undefined"!==typeof AudioContext?t._audioContext=new AudioContext:"undefined"!==typeof webkitAudioContext&&(t._audioContext=new webkitAudioContext)),t._audioContext},Object.defineProperty(t.prototype,"audio",{get:function(){return this._audio},enumerable:!1,configurable:!0}),t.prototype.connect=function(e){return void 0===e&&(e={}),r(this,void 0,void 0,(function(){var n,o,i,r,s,c,u,d,l;return a(this,(function(a){switch(a.label){case 0:if(this._log.debug(".connect",JSON.stringify(e)),this._throwIfDestroyed(),this._activeCall)throw new h.InvalidStateError("A Call is already active");if(e.connectToken){try{r=JSON.parse(decodeURIComponent(atob(e.connectToken))),n=r.customParameters,o=r.parameters,i=r.signalingReconnectToken}catch(p){throw new h.InvalidArgumentError("Cannot parse connectToken")}if(!o||!o.CallSid||!i)throw new h.InvalidArgumentError("Invalid connectToken")}return s=!1,c={},u={enableImprovedSignalingErrorPrecision:!!this._options.enableImprovedSignalingErrorPrecision,rtcConfiguration:e.rtcConfiguration,voiceEventSidGenerator:this._options.voiceEventSidGenerator},i&&o?(s=!0,u.callParameters=o,u.reconnectCallSid=o.CallSid,u.reconnectToken=i,c=n||c):c=e.params||c,l=this,[4,this._makeCall(c,u,s)];case 1:return d=l._activeCall=a.sent(),this._calls.splice(0).forEach((function(e){return e.ignore()})),this._soundcache.get(t.SoundName.Incoming).stop(),d.accept({rtcConstraints:e.rtcConstraints}),this._publishNetworkChange(),[2,d]}}))}))},Object.defineProperty(t.prototype,"calls",{get:function(){return this._calls},enumerable:!1,configurable:!0}),t.prototype.destroy=function(){var e;this._log.debug(".destroy"),this._log.debug("Rejecting any incoming calls"),this._calls.slice(0).forEach((function(e){return e.reject()})),this.disconnectAll(),this._stopRegistrationTimer(),this._destroyStream(),this._destroyPublisher(),this._destroyAudioHelper(),null===(e=this._audioProcessorEventObserver)||void 0===e||e.destroy(),this._networkInformation&&"function"===typeof this._networkInformation.removeEventListener&&this._networkInformation.removeEventListener("change",this._publishNetworkChange),"undefined"!==typeof window&&window.removeEventListener&&(window.removeEventListener("beforeunload",this._boundConfirmClose),window.removeEventListener("unload",this._boundDestroy),window.removeEventListener("pagehide",this._boundDestroy)),this._setState(t.State.Destroyed),s.EventEmitter.prototype.removeAllListeners.call(this)},t.prototype.disconnectAll=function(){this._log.debug(".disconnectAll"),this._calls.splice(0).forEach((function(e){return e.disconnect()})),this._activeCall&&this._activeCall.disconnect()},Object.defineProperty(t.prototype,"edge",{get:function(){return this._edge},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"home",{get:function(){return this._home},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"identity",{get:function(){return this._identity},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isBusy",{get:function(){return!!this._activeCall},enumerable:!1,configurable:!0}),t.prototype.register=function(){return r(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:if(this._log.debug(".register"),this.state!==t.State.Unregistered)throw new h.InvalidStateError('Attempt to register when device is in state "'+this.state+'". Must be "'+t.State.Unregistered+'".');return this._shouldReRegister=!1,this._setState(t.State.Registering),[4,this._streamConnectedPromise||this._setupStream()];case 1:return e.sent(),[4,this._sendPresence(!0)];case 2:return e.sent(),[4,C.promisifyEvents(this,t.State.Registered,t.State.Unregistered)];case 3:return e.sent(),[2]}}))}))},Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"token",{get:function(){return this._token},enumerable:!1,configurable:!0}),t.prototype.toString=function(){return"[Twilio.Device instance]"},t.prototype.unregister=function(){return r(this,void 0,void 0,(function(){var e,n;return a(this,(function(o){switch(o.label){case 0:if(this._log.debug(".unregister"),this.state!==t.State.Registered)throw new h.InvalidStateError('Attempt to unregister when device is in state "'+this.state+'". Must be "'+t.State.Registered+'".');return this._shouldReRegister=!1,[4,this._streamConnectedPromise];case 1:return e=o.sent(),n=new Promise((function(t){e.on("offline",t)})),[4,this._sendPresence(!1)];case 2:return o.sent(),[4,n];case 3:return o.sent(),[2]}}))}))},t.prototype.updateOptions=function(e){if(void 0===e&&(e={}),this._logOptions("updateOptions",e),this.state===t.State.Destroyed)throw new h.InvalidStateError('Attempt to "updateOptions" when device is in state "'+this.state+'".');this._options=i(i(i({},this._defaultOptions),this._options),e);var n=new Set(this._chunderURIs),o="string"===typeof this._options.chunderw?[this._options.chunderw]:Array.isArray(this._options.chunderw)&&this._options.chunderw,r=this._chunderURIs=(o||y.getChunderURIs(this._options.edge)).map(y.createSignalingEndpointURL),a=n.size!==r.length;if(!a)for(var s=0,c=r;s<c.length;s++){var u=c[s];if(!n.has(u)){a=!0;break}}if(this.isBusy&&a)throw new h.InvalidStateError("Cannot change Edge while on an active Call");this._setupLoglevel(this._options.logLevel);for(var d=0,l=Object.keys(t._defaultSounds);d<l.length;d++){var f=l[d],m=t._defaultSounds[f],_=p.SOUNDS_BASE_URL+"/"+m.filename+"."+t.extension+"?cache="+p.RELEASE_VERSION,g=this._options.sounds&&this._options.sounds[f]||_,v=new(this._options.Sound||w.default)(f,g,{audioContext:this._options.disableAudioContextSounds?null:t.audioContext,maxDuration:m.maxDuration,shouldLoop:m.shouldLoop});this._soundcache.set(f,v)}this._setupAudioHelper(),this._setupPublisher(),a&&this._streamConnectedPromise&&this._setupStream(),"undefined"!==typeof window&&"function"===typeof window.addEventListener&&this._options.closeProtection&&(window.removeEventListener("beforeunload",this._boundConfirmClose),window.addEventListener("beforeunload",this._boundConfirmClose))},t.prototype.updateToken=function(e){if(this._log.debug(".updateToken"),this.state===t.State.Destroyed)throw new h.InvalidStateError('Attempt to "updateToken" when device is in state "'+this.state+'".');if("string"!==typeof e)throw new h.InvalidArgumentError('Parameter "token" must be of type "string".');this._token=e,this._stream&&this._stream.setToken(this._token),this._publisher&&this._publisher.setToken(this._token)},t.prototype._confirmClose=function(e){if(!this._activeCall)return"";var t=this._options.closeProtection||!1,n="string"!==typeof t?"A call is currently in-progress. Leaving or reloading this page will end the call.":t;return(e||window.event).returnValue=n,n},t.prototype._destroyAudioHelper=function(){this._audio&&(this._audio._destroy(),this._audio=null)},t.prototype._destroyPublisher=function(){this._publisher&&(this._publisher=null)},t.prototype._destroyStream=function(){this._stream&&(this._stream.removeListener("close",this._onSignalingClose),this._stream.removeListener("connected",this._onSignalingConnected),this._stream.removeListener("error",this._onSignalingError),this._stream.removeListener("invite",this._onSignalingInvite),this._stream.removeListener("offline",this._onSignalingOffline),this._stream.removeListener("ready",this._onSignalingReady),this._stream.destroy(),this._stream=null),this._onSignalingOffline(),this._streamConnectedPromise=null},t.prototype._findCall=function(e){return this._calls.find((function(t){return t.parameters.CallSid===e||t.outboundConnectionId===e}))||null},t.prototype._logOptions=function(e,t){void 0===t&&(t={});var n=["allowIncomingWhileBusy","appName","appVersion","closeProtection","codecPreferences","disableAudioContextSounds","dscp","edge","enableImprovedSignalingErrorPrecision","forceAggressiveIceNomination","logLevel","maxAverageBitrate","maxCallSignalingTimeoutMs","sounds","tokenRefreshMs"],o=["RTCPeerConnection","enumerateDevices","getUserMedia"];if("object"===typeof t){var r=i({},t);Object.keys(r).forEach((function(e){n.includes(e)||o.includes(e)||delete r[e],o.includes(e)&&(r[e]=!0)})),this._log.debug("."+e,JSON.stringify(r))}},t.prototype._makeCall=function(e,n,o){return void 0===o&&(o=!1),r(this,void 0,void 0,(function(){var i,r,s,c,u=this;return a(this,(function(a){switch(a.label){case 0:if("undefined"===typeof t._isUnifiedPlanDefault)throw new h.InvalidStateError("Device has not been initialized.");return c={audioHelper:this._audio,isUnifiedPlanDefault:t._isUnifiedPlanDefault,onIgnore:function(){u._soundcache.get(t.SoundName.Incoming).stop()}},[4,this._streamConnectedPromise||this._setupStream()];case 1:return c.pstream=a.sent(),c.publisher=this._publisher,c.soundcache=this._soundcache,i=c,n=Object.assign({MediaStream:this._options.MediaStream||b.PeerConnection,RTCPeerConnection:this._options.RTCPeerConnection,beforeAccept:function(e){u._activeCall&&u._activeCall!==e&&(u._activeCall.disconnect(),u._removeCall(u._activeCall))},codecPreferences:this._options.codecPreferences,customSounds:this._options.sounds,dialtonePlayer:t._dialtonePlayer,dscp:this._options.dscp,forceAggressiveIceNomination:this._options.forceAggressiveIceNomination,getInputStream:function(){return u._options.fileInputStream||u._callInputStream},getSinkIds:function(){return u._callSinkIds},maxAverageBitrate:this._options.maxAverageBitrate,preflight:this._options.preflight,rtcConstraints:this._options.rtcConstraints,shouldPlayDisconnect:function(){var e;return null===(e=u._audio)||void 0===e?void 0:e.disconnect()},twimlParams:e,voiceEventSidGenerator:this._options.voiceEventSidGenerator},n),r=function(){u._stream?null===u._activeCall&&0===u._calls.length&&u._stream.updatePreferredURI(null):u._log.warn("UnsetPreferredUri called without a stream")},s=new(this._options.Call||l.default)(i,n),this._publisher.info("settings","init",{RTCPeerConnection:!!this._options.RTCPeerConnection,enumerateDevices:!!this._options.enumerateDevices,getUserMedia:!!this._options.getUserMedia},s),s.once("accept",(function(){var e,n,i;u._stream.updatePreferredURI(u._preferredURI),u._removeCall(s),u._activeCall=s,u._audio&&u._audio._maybeStartPollingVolume(),s.direction===l.default.CallDirection.Outgoing&&(null===(e=u._audio)||void 0===e?void 0:e.outgoing())&&!o&&u._soundcache.get(t.SoundName.Outgoing).play();var r={edge:u._edge||u._region};u._options.edge&&(r.selected_edge=Array.isArray(u._options.edge)?u._options.edge:[u._options.edge]),u._publisher.info("settings","edge",r,s),(null===(n=u._audio)||void 0===n?void 0:n.processedStream)&&(null===(i=u._audioProcessorEventObserver)||void 0===i||i.emit("enabled"))})),s.addListener("error",(function(e){"closed"===s.status()&&(u._removeCall(s),r()),u._audio&&u._audio._maybeStopPollingVolume(),u._maybeStopIncomingSound()})),s.once("cancel",(function(){u._log.info("Canceled: "+s.parameters.CallSid),u._removeCall(s),r(),u._audio&&u._audio._maybeStopPollingVolume(),u._maybeStopIncomingSound()})),s.once("disconnect",(function(){u._audio&&u._audio._maybeStopPollingVolume(),u._removeCall(s),r(),u._maybeStopIncomingSound()})),s.once("reject",(function(){u._log.info("Rejected: "+s.parameters.CallSid),u._audio&&u._audio._maybeStopPollingVolume(),u._removeCall(s),r(),u._maybeStopIncomingSound()})),s.on("transportClose",(function(){s.status()===l.default.State.Pending&&(u._audio&&u._audio._maybeStopPollingVolume(),u._removeCall(s),u._maybeStopIncomingSound())})),[2,s]}}))}))},t.prototype._maybeStopIncomingSound=function(){this._calls.length||this._soundcache.get(t.SoundName.Incoming).stop()},t.prototype._removeCall=function(e){this._activeCall===e&&(this._activeCall=null);for(var t=this._calls.length-1;t>=0;t--)e===this._calls[t]&&this._calls.splice(t,1)},t.prototype._sendPresence=function(e){return r(this,void 0,void 0,(function(){var t;return a(this,(function(n){switch(n.label){case 0:return[4,this._streamConnectedPromise];case 1:return(t=n.sent())?(t.register({audio:e}),e?this._startRegistrationTimer():this._stopRegistrationTimer(),[2]):[2]}}))}))},t.prototype._setState=function(e){if(e!==this.state){this._state=e;var t=this._stateEventMapping[e];this._log.debug("#"+t),this.emit(t)}},t.prototype._setupAudioHelper=function(){var e=this;this._audioProcessorEventObserver||(this._audioProcessorEventObserver=new d.AudioProcessorEventObserver,this._audioProcessorEventObserver.on("event",(function(t){var n=t.name,o=t.group;e._publisher.info(o,n,{},e._activeCall)})));var n={audioContext:t.audioContext,audioProcessorEventObserver:this._audioProcessorEventObserver,enumerateDevices:this._options.enumerateDevices,getUserMedia:this._options.getUserMedia||S.default};if(this._audio)return this._log.info("Found existing audio helper; updating options..."),void this._audio._updateUserOptions(n);this._audio=new(this._options.AudioHelper||u.default)(this._updateSinkIds,this._updateInputStream,n),this._audio.on("deviceChange",(function(t){var n=e._activeCall,o=t.map((function(e){return e.deviceId}));e._publisher.info("audio","device-change",{lost_active_device_ids:o},n),n&&n._mediaHandler._onInputDevicesChanged()}))},t.prototype._setupLoglevel=function(e){var t="number"===typeof e||"string"===typeof e?e:c.levels.ERROR;this._log.setDefaultLevel(t),this._log.info("Set logger default level to",t)},t.prototype._setupPublisher=function(){var e=this;this._publisher&&(this._log.info("Found existing publisher; destroying..."),this._destroyPublisher());var t={defaultPayload:this._createDefaultPayload,metadata:{app_name:this._options.appName,app_version:this._options.appVersion}};return this._options.eventgw&&(t.host=this._options.eventgw),this._home&&(t.host=y.createEventGatewayURI(this._home)),this._publisher=new(this._options.Publisher||m.default)("twilio-js-sdk",this.token,t),!1===this._options.publishEvents?this._publisher.disable():this._publisher.on("error",(function(t){e._log.warn("Cannot connect to insights.",t)})),this._publisher},t.prototype._setupStream=function(){var e=this;return this._stream&&(this._log.info("Found existing stream; destroying..."),this._destroyStream()),this._log.info("Setting up VSP"),this._stream=new(this._options.PStream||v.default)(this.token,this._chunderURIs,{backoffMaxMs:this._options.backoffMaxMs,maxPreferredDurationMs:this._options.maxCallSignalingTimeoutMs}),this._stream.addListener("close",this._onSignalingClose),this._stream.addListener("connected",this._onSignalingConnected),this._stream.addListener("error",this._onSignalingError),this._stream.addListener("invite",this._onSignalingInvite),this._stream.addListener("offline",this._onSignalingOffline),this._stream.addListener("ready",this._onSignalingReady),this._streamConnectedPromise=C.promisifyEvents(this._stream,"connected","close").then((function(){return e._stream}))},t.prototype._showIncomingCall=function(e,n){var o,i=this;return Promise.race([n(),new Promise((function(e,t){o=setTimeout((function(){t(new Error("Playing incoming ringtone took too long; it might not play. Continuing execution..."))}),2e3)}))]).catch((function(e){i._log.warn(e.message)})).then((function(){clearTimeout(o),i._log.debug("#incoming",JSON.stringify({customParameters:e.customParameters,parameters:e.parameters})),i.emit(t.EventName.Incoming,e)}))},t.prototype._startRegistrationTimer=function(){var e=this;this._stopRegistrationTimer(),this._regTimer=setTimeout((function(){e._sendPresence(!0)}),3e4)},t.prototype._stopRegistrationTimer=function(){this._regTimer&&clearTimeout(this._regTimer)},t.prototype._throwIfDestroyed=function(){if(this.state===t.State.Destroyed)throw new h.InvalidStateError("Device has been destroyed.")},t.prototype._updateRingtoneSinkIds=function(e){return Promise.resolve(this._soundcache.get(t.SoundName.Incoming).setSinkIds(e))},t.prototype._updateSpeakerSinkIds=function(e){Array.from(this._soundcache.entries()).filter((function(e){return e[0]!==t.SoundName.Incoming})).forEach((function(t){return t[1].setSinkIds(e)})),this._callSinkIds=e;var n=this._activeCall;return n?n._setSinkIds(e):Promise.resolve()},t._defaultSounds={disconnect:{filename:"disconnect",maxDuration:3e3},dtmf0:{filename:"dtmf-0",maxDuration:1e3},dtmf1:{filename:"dtmf-1",maxDuration:1e3},dtmf2:{filename:"dtmf-2",maxDuration:1e3},dtmf3:{filename:"dtmf-3",maxDuration:1e3},dtmf4:{filename:"dtmf-4",maxDuration:1e3},dtmf5:{filename:"dtmf-5",maxDuration:1e3},dtmf6:{filename:"dtmf-6",maxDuration:1e3},dtmf7:{filename:"dtmf-7",maxDuration:1e3},dtmf8:{filename:"dtmf-8",maxDuration:1e3},dtmf9:{filename:"dtmf-9",maxDuration:1e3},dtmfh:{filename:"dtmf-hash",maxDuration:1e3},dtmfs:{filename:"dtmf-star",maxDuration:1e3},incoming:{filename:"incoming",shouldLoop:!0},outgoing:{filename:"outgoing",maxDuration:3e3}},t}(s.EventEmitter);!function(e){!function(e){e.Error="error",e.Incoming="incoming",e.Destroyed="destroyed",e.Unregistered="unregistered",e.Registering="registering",e.Registered="registered",e.TokenWillExpire="tokenWillExpire"}(e.EventName||(e.EventName={})),function(e){e.Destroyed="destroyed",e.Unregistered="unregistered",e.Registering="registering",e.Registered="registered"}(e.State||(e.State={})),function(e){e.Incoming="incoming",e.Outgoing="outgoing",e.Disconnect="disconnect",e.Dtmf0="dtmf0",e.Dtmf1="dtmf1",e.Dtmf2="dtmf2",e.Dtmf3="dtmf3",e.Dtmf4="dtmf4",e.Dtmf5="dtmf5",e.Dtmf6="dtmf6",e.Dtmf7="dtmf7",e.Dtmf8="dtmf8",e.Dtmf9="dtmf9",e.DtmfS="dtmfs",e.DtmfH="dtmfh"}(e.SoundName||(e.SoundName={}))}(P||(P={})),t.default=P},19326:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(18987),i={dtmf0:[1360,960],dtmf1:[1230,720],dtmf2:[1360,720],dtmf3:[1480,720],dtmf4:[1230,790],dtmf5:[1360,790],dtmf6:[1480,790],dtmf7:[1230,870],dtmf8:[1360,870],dtmf9:[1480,870],dtmfh:[1480,960],dtmfs:[1230,960]},r=function(){function e(e){var t=this;this._context=e,this._gainNodes=[],this._gainNodes=[this._context.createGain(),this._context.createGain()],this._gainNodes.forEach((function(e){e.connect(t._context.destination),e.gain.value=.1,t._gainNodes.push(e)}))}return e.prototype.cleanup=function(){this._gainNodes.forEach((function(e){e.disconnect()}))},e.prototype.play=function(e){var t=this,n=i[e];if(!n)throw new o.InvalidArgumentError("Invalid DTMF sound name");[this._context.createOscillator(),this._context.createOscillator()].forEach((function(e,o){e.type="sine",e.frequency.value=n[o],e.connect(t._gainNodes[o]),e.start(),e.stop(t._context.currentTime+.1),e.addEventListener("ended",(function(){return e.disconnect()}))}))},e}();t.default=r},80305:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.errorsByCode=t.MediaErrors=t.SignalingErrors=t.UserMediaErrors=t.MalformedRequestErrors=t.GeneralErrors=t.SIPServerErrors=t.ClientErrors=t.SignatureValidationErrors=t.AuthorizationErrors=t.TwilioError=void 0;var i,r,a,s,c,u,d,l,p,f=n(83528);t.TwilioError=f.default,function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=20101,i.description="Invalid access token",i.explanation="Twilio was unable to validate your Access Token",i.name="AccessTokenInvalid",i.solutions=[],Object.setPrototypeOf(i,e.AccessTokenInvalid.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AccessTokenInvalid=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=20104,i.description="Access token expired or expiration date invalid",i.explanation="The Access Token provided to the Twilio API has expired, the expiration time specified in the token was invalid, or the expiration time specified was too far in the future",i.name="AccessTokenExpired",i.solutions=[],Object.setPrototypeOf(i,e.AccessTokenExpired.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AccessTokenExpired=n;var i=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=20151,i.description="Authentication Failed",i.explanation="The Authentication with the provided JWT failed",i.name="AuthenticationFailed",i.solutions=[],Object.setPrototypeOf(i,e.AuthenticationFailed.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AuthenticationFailed=i}(t.AuthorizationErrors||(t.AuthorizationErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The access token has an invalid Account SID, API Key, or API Key Secret."],i.code=31202,i.description="Signature validation failed.",i.explanation="The provided access token failed signature validation.",i.name="AccessTokenSignatureValidationFailed",i.solutions=["Ensure the Account SID, API Key, and API Key Secret are valid when generating your access token."],Object.setPrototypeOf(i,e.AccessTokenSignatureValidationFailed.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AccessTokenSignatureValidationFailed=t}(r=t.SignatureValidationErrors||(t.SignatureValidationErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31400,i.description="Bad Request (HTTP/SIP)",i.explanation="The request could not be understood due to malformed syntax.",i.name="BadRequest",i.solutions=[],Object.setPrototypeOf(i,e.BadRequest.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.BadRequest=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The outbound call was made to an invalid phone number.","The TwiML application sid is missing a Voice URL."],i.code=31404,i.description="Not Found (HTTP/SIP)",i.explanation="The server has not found anything matching the request.",i.name="NotFound",i.solutions=["Ensure the phone number dialed is valid.","Ensure the TwiML application is configured correctly with a Voice URL link."],Object.setPrototypeOf(i,e.NotFound.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.NotFound=n;var i=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31480,i.description="Temporarily Unavailable (SIP)",i.explanation="The callee is currently unavailable.",i.name="TemporarilyUnavailable",i.solutions=[],Object.setPrototypeOf(i,e.TemporarilyUnavailable.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.TemporarilyUnavailable=i;var r=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31486,i.description="Busy Here (SIP)",i.explanation="The callee is busy.",i.name="BusyHere",i.solutions=[],Object.setPrototypeOf(i,e.BusyHere.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.BusyHere=r}(a=t.ClientErrors||(t.ClientErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31603,i.description="Decline (SIP)",i.explanation="The callee does not wish to participate in the call.",i.name="Decline",i.solutions=[],Object.setPrototypeOf(i,e.Decline.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.Decline=t}(s=t.SIPServerErrors||(t.SIPServerErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31e3,i.description="Unknown Error",i.explanation="An unknown error has occurred. See error details for more information.",i.name="UnknownError",i.solutions=[],Object.setPrototypeOf(i,e.UnknownError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.UnknownError=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31001,i.description="Application Not Found",i.explanation="",i.name="ApplicationNotFoundError",i.solutions=[],Object.setPrototypeOf(i,e.ApplicationNotFoundError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ApplicationNotFoundError=n;var i=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31002,i.description="Connection Declined",i.explanation="",i.name="ConnectionDeclinedError",i.solutions=[],Object.setPrototypeOf(i,e.ConnectionDeclinedError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ConnectionDeclinedError=i;var r=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31003,i.description="Connection Timeout",i.explanation="The server could not produce a response within a suitable amount of time.",i.name="ConnectionTimeoutError",i.solutions=[],Object.setPrototypeOf(i,e.ConnectionTimeoutError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ConnectionTimeoutError=r;var a=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31005,i.description="Connection error",i.explanation="A connection error occurred during the call",i.name="ConnectionError",i.solutions=[],Object.setPrototypeOf(i,e.ConnectionError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ConnectionError=a;var s=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The incoming call was cancelled because it was not answered in time or it was accepted/rejected by another application instance registered with the same identity."],i.code=31008,i.description="Call cancelled",i.explanation="Unable to answer because the call has ended",i.name="CallCancelledError",i.solutions=[],Object.setPrototypeOf(i,e.CallCancelledError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.CallCancelledError=s;var c=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31009,i.description="Transport error",i.explanation="No transport available to send or receive messages",i.name="TransportError",i.solutions=[],Object.setPrototypeOf(i,e.TransportError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.TransportError=c}(c=t.GeneralErrors||(t.GeneralErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["Invalid content or MessageType passed to sendMessage method."],i.code=31100,i.description="The request had malformed syntax.",i.explanation="The request could not be understood due to malformed syntax.",i.name="MalformedRequestError",i.solutions=["Ensure content and MessageType passed to sendMessage method are valid."],Object.setPrototypeOf(i,e.MalformedRequestError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.MalformedRequestError=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31101,i.description="Missing parameter array in request",i.explanation="",i.name="MissingParameterArrayError",i.solutions=[],Object.setPrototypeOf(i,e.MissingParameterArrayError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.MissingParameterArrayError=n;var i=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31102,i.description="Authorization token missing in request.",i.explanation="",i.name="AuthorizationTokenMissingError",i.solutions=[],Object.setPrototypeOf(i,e.AuthorizationTokenMissingError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AuthorizationTokenMissingError=i;var r=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31103,i.description="Maximum parameter length has been exceeded.",i.explanation="Length of parameters cannot exceed MAX_PARAM_LENGTH.",i.name="MaxParameterLengthExceededError",i.solutions=[],Object.setPrototypeOf(i,e.MaxParameterLengthExceededError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.MaxParameterLengthExceededError=r;var a=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31104,i.description="Invalid bridge token",i.explanation="",i.name="InvalidBridgeTokenError",i.solutions=[],Object.setPrototypeOf(i,e.InvalidBridgeTokenError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.InvalidBridgeTokenError=a;var s=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["Client name contains invalid characters."],i.code=31105,i.description="Invalid client name",i.explanation="Client name should not contain control, space, delims, or unwise characters.",i.name="InvalidClientNameError",i.solutions=["Make sure that client name does not contain any of the invalid characters."],Object.setPrototypeOf(i,e.InvalidClientNameError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.InvalidClientNameError=s;var c=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31107,i.description="The reconnect parameter is invalid",i.explanation="",i.name="ReconnectParameterInvalidError",i.solutions=[],Object.setPrototypeOf(i,e.ReconnectParameterInvalidError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ReconnectParameterInvalidError=c}(u=t.MalformedRequestErrors||(t.MalformedRequestErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31201,i.description="Authorization error",i.explanation="The request requires user authentication. The server understood the request, but is refusing to fulfill it.",i.name="AuthorizationError",i.solutions=[],Object.setPrototypeOf(i,e.AuthorizationError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AuthorizationError=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31203,i.description="No valid account",i.explanation="",i.name="NoValidAccountError",i.solutions=[],Object.setPrototypeOf(i,e.NoValidAccountError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.NoValidAccountError=n;var i=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31204,i.description="Invalid JWT token",i.explanation="",i.name="InvalidJWTTokenError",i.solutions=[],Object.setPrototypeOf(i,e.InvalidJWTTokenError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.InvalidJWTTokenError=i;var r=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31205,i.description="JWT token expired",i.explanation="",i.name="JWTTokenExpiredError",i.solutions=[],Object.setPrototypeOf(i,e.JWTTokenExpiredError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.JWTTokenExpiredError=r;var a=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["Rate limit exceeded."],i.code=31206,i.description="Rate exceeded authorized limit.",i.explanation="The request performed exceeds the authorized limit.",i.name="RateExceededError",i.solutions=["Ensure message send rate does not exceed authorized limits."],Object.setPrototypeOf(i,e.RateExceededError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.RateExceededError=a;var s=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=31207,i.description="JWT token expiration too long",i.explanation="",i.name="JWTTokenExpirationTooLongError",i.solutions=[],Object.setPrototypeOf(i,e.JWTTokenExpirationTooLongError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.JWTTokenExpirationTooLongError=s;var c=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The payload size of Call Message Event exceeds the authorized limit."],i.code=31209,i.description="Call Message Event Payload size exceeded authorized limit.",i.explanation="The request performed to send a Call Message Event exceeds the payload size authorized limit",i.name="PayloadSizeExceededError",i.solutions=["Reduce payload size of Call Message Event to be within the authorized limit and try again."],Object.setPrototypeOf(i,e.PayloadSizeExceededError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.PayloadSizeExceededError=c}(i=t.AuthorizationErrors||(t.AuthorizationErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The user denied the getUserMedia request.","The browser denied the getUserMedia request."],i.code=31401,i.description="UserMedia Permission Denied Error",i.explanation="The browser or end-user denied permissions to user media. Therefore we were unable to acquire input audio.",i.name="PermissionDeniedError",i.solutions=["The user should accept the request next time prompted. If the browser saved the deny, the user should change that permission in their browser.","The user should to verify that the browser has permission to access the microphone at this address."],Object.setPrototypeOf(i,e.PermissionDeniedError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.PermissionDeniedError=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["NotFoundError - The deviceID specified was not found.","The getUserMedia constraints were overconstrained and no devices matched."],i.code=31402,i.description="UserMedia Acquisition Failed Error",i.explanation="The browser and end-user allowed permissions, however getting the media failed. Usually this is due to bad constraints, but can sometimes fail due to browser, OS or hardware issues.",i.name="AcquisitionFailedError",i.solutions=["Ensure the deviceID being specified exists.","Try acquiring media with fewer constraints."],Object.setPrototypeOf(i,e.AcquisitionFailedError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.AcquisitionFailedError=n}(d=t.UserMediaErrors||(t.UserMediaErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=[],i.code=53e3,i.description="Signaling connection error",i.explanation="Raised whenever a signaling connection error occurs that is not covered by a more specific error code.",i.name="ConnectionError",i.solutions=[],Object.setPrototypeOf(i,e.ConnectionError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ConnectionError=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The device running your application lost its Internet connection."],i.code=53001,i.description="Signaling connection disconnected",i.explanation="Raised whenever the signaling connection is unexpectedly disconnected.",i.name="ConnectionDisconnected",i.solutions=["Ensure the device running your application has access to a stable Internet connection."],Object.setPrototypeOf(i,e.ConnectionDisconnected.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ConnectionDisconnected=n}(l=t.SignalingErrors||(t.SignalingErrors={})),function(e){var t=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The Client may not be using a supported WebRTC implementation.","The Client may not have the necessary resources to create or apply a new media description."],i.code=53400,i.description="Client is unable to create or apply a local media description",i.explanation="Raised whenever a Client is unable to create or apply a local media description.",i.name="ClientLocalDescFailed",i.solutions=["If you are experiencing this error using the JavaScript SDK, ensure you are running it with a supported WebRTC implementation."],Object.setPrototypeOf(i,e.ClientLocalDescFailed.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ClientLocalDescFailed=t;var n=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The Client may not be using a supported WebRTC implementation.","The Client may be connecting peer-to-peer with another Participant that is not using a supported WebRTC implementation.","The Client may not have the necessary resources to apply a new media description."],i.code=53402,i.description="Client is unable to apply a remote media description",i.explanation="Raised whenever the Client receives a remote media description but is unable to apply it.",i.name="ClientRemoteDescFailed",i.solutions=["If you are experiencing this error using the JavaScript SDK, ensure you are running it with a supported WebRTC implementation."],Object.setPrototypeOf(i,e.ClientRemoteDescFailed.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ClientRemoteDescFailed=n;var i=function(t){function n(n,o){var i=t.call(this,n,o)||this;i.causes=["The Client was unable to establish a media connection.","A media connection which was active failed liveliness checks."],i.code=53405,i.description="Media connection failed",i.explanation="Raised by the Client or Server whenever a media connection fails.",i.name="ConnectionError",i.solutions=["If the problem persists, try connecting to another region.","Check your Client's network connectivity.","If you've provided custom ICE Servers then ensure that the URLs and credentials are valid."],Object.setPrototypeOf(i,e.ConnectionError.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return o(n,t),n}(f.default);e.ConnectionError=i}(p=t.MediaErrors||(t.MediaErrors={})),t.errorsByCode=new Map([[20101,i.AccessTokenInvalid],[20104,i.AccessTokenExpired],[20151,i.AuthenticationFailed],[31202,r.AccessTokenSignatureValidationFailed],[31400,a.BadRequest],[31404,a.NotFound],[31480,a.TemporarilyUnavailable],[31486,a.BusyHere],[31603,s.Decline],[31e3,c.UnknownError],[31001,c.ApplicationNotFoundError],[31002,c.ConnectionDeclinedError],[31003,c.ConnectionTimeoutError],[31005,c.ConnectionError],[31008,c.CallCancelledError],[31009,c.TransportError],[31100,u.MalformedRequestError],[31101,u.MissingParameterArrayError],[31102,u.AuthorizationTokenMissingError],[31103,u.MaxParameterLengthExceededError],[31104,u.InvalidBridgeTokenError],[31105,u.InvalidClientNameError],[31107,u.ReconnectParameterInvalidError],[31201,i.AuthorizationError],[31203,i.NoValidAccountError],[31204,i.InvalidJWTTokenError],[31205,i.JWTTokenExpiredError],[31206,i.RateExceededError],[31207,i.JWTTokenExpirationTooLongError],[31209,i.PayloadSizeExceededError],[31401,d.PermissionDeniedError],[31402,d.AcquisitionFailedError],[53e3,l.ConnectionError],[53001,l.ConnectionDisconnected],[53400,p.ClientLocalDescFailed],[53402,p.ClientRemoteDescFailed],[53405,p.ConnectionError]]),Object.freeze(t.errorsByCode)},18987:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.UserMediaErrors=t.TwilioError=t.SIPServerErrors=t.SignatureValidationErrors=t.SignalingErrors=t.MediaErrors=t.MalformedRequestErrors=t.GeneralErrors=t.ClientErrors=t.AuthorizationErrors=t.hasErrorByCode=t.getErrorByCode=t.NotSupportedError=t.InvalidStateError=t.InvalidArgumentError=t.getPreciseSignalingErrorByCode=void 0;var i=n(80305);Object.defineProperty(t,"AuthorizationErrors",{enumerable:!0,get:function(){return i.AuthorizationErrors}}),Object.defineProperty(t,"ClientErrors",{enumerable:!0,get:function(){return i.ClientErrors}}),Object.defineProperty(t,"GeneralErrors",{enumerable:!0,get:function(){return i.GeneralErrors}}),Object.defineProperty(t,"MalformedRequestErrors",{enumerable:!0,get:function(){return i.MalformedRequestErrors}}),Object.defineProperty(t,"MediaErrors",{enumerable:!0,get:function(){return i.MediaErrors}}),Object.defineProperty(t,"SignalingErrors",{enumerable:!0,get:function(){return i.SignalingErrors}}),Object.defineProperty(t,"SignatureValidationErrors",{enumerable:!0,get:function(){return i.SignatureValidationErrors}}),Object.defineProperty(t,"SIPServerErrors",{enumerable:!0,get:function(){return i.SIPServerErrors}}),Object.defineProperty(t,"TwilioError",{enumerable:!0,get:function(){return i.TwilioError}}),Object.defineProperty(t,"UserMediaErrors",{enumerable:!0,get:function(){return i.UserMediaErrors}});var r=new Set([31001,31002,31003,31101,31102,31103,31104,31105,31107,31201,31202,31203,31204,31205,31207,31404,31480,31486,31603]);t.getPreciseSignalingErrorByCode=function(e,t){if("number"===typeof t&&d(t)&&(!!e||!r.has(t)))return u(t)};var a=function(e){function t(t){var n=e.call(this,t)||this;return n.name="InvalidArgumentError",n}return o(t,e),t}(Error);t.InvalidArgumentError=a;var s=function(e){function t(t){var n=e.call(this,t)||this;return n.name="InvalidStateError",n}return o(t,e),t}(Error);t.InvalidStateError=s;var c=function(e){function t(t){var n=e.call(this,t)||this;return n.name="NotSupportedError",n}return o(t,e),t}(Error);function u(e){var t=i.errorsByCode.get(e);if(!t)throw new a("Error code "+e+" not found");return t}function d(e){return i.errorsByCode.has(e)}t.NotSupportedError=c,t.getErrorByCode=u,t.hasErrorByCode=d},83528:function(e,t){var n=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){function t(n,o){var i=e.call(this)||this;Object.setPrototypeOf(i,t.prototype);var r="string"===typeof n?n:i.explanation,a="object"===typeof n?n:o;return i.message=i.name+" ("+i.code+"): "+r,i.originalError=a,i}return n(t,e),t}(Error);t.default=o},40202:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=n(5939),r=n(24468),a=n(96436),s=function(e){function t(n,o,i){var s=e.call(this)||this;if(!(s instanceof t))return new t(n,o,i);var c=(i=Object.assign({defaultPayload:function(){return{}}},i)).defaultPayload;"function"!==typeof c&&(c=function(){return Object.assign({},i.defaultPayload)});var u=!0,d=Object.assign({app_name:void 0,app_version:void 0},i.metadata);return Object.defineProperties(s,{_defaultPayload:{value:c},_host:{value:i.host,writable:!0},_isEnabled:{get:function(){return u},set:function(e){u=e}},_log:{value:new r.default("EventPublisher")},_request:{value:i.request||a.default,writable:!0},_token:{value:o,writable:!0},isEnabled:{enumerable:!0,get:function(){return u}},metadata:{enumerable:!0,get:function(){return d}},productName:{enumerable:!0,value:n},token:{enumerable:!0,get:function(){return this._token}}}),s}return o(t,e),t}(i.EventEmitter);function c(e){return{audio_codec:e.codecName,audio_level_in:e.audioInputLevel,audio_level_out:e.audioOutputLevel,bytes_received:e.bytesReceived,bytes_sent:e.bytesSent,call_volume_input:e.inputVolume,call_volume_output:e.outputVolume,jitter:e.jitter,mos:e.mos&&Math.round(100*e.mos)/100,packets_lost:e.packetsLost,packets_lost_fraction:e.packetsLostFraction&&Math.round(100*e.packetsLostFraction)/100,packets_received:e.packetsReceived,rtt:e.rtt,timestamp:new Date(e.timestamp).toISOString(),total_bytes_received:e.totals.bytesReceived,total_bytes_sent:e.totals.bytesSent,total_packets_lost:e.totals.packetsLost,total_packets_received:e.totals.packetsReceived,total_packets_sent:e.totals.packetsSent}}s.prototype._post=function(e,t,n,o,i,r,a){var s=this;if(!this.isEnabled&&!a||!this._host)return this._log.debug("Publishing cancelled",JSON.stringify({isEnabled:this.isEnabled,force:a,host:this._host})),Promise.resolve();if(!r||(!r.parameters||!r.parameters.CallSid)&&!r.outboundConnectionId)return r?this._log.debug("Publishing cancelled. Missing connection info",JSON.stringify({outboundConnectionId:r.outboundConnectionId,parameters:r.parameters})):this._log.debug("Publishing cancelled. Missing connection object"),Promise.resolve();var c={group:n,level:t.toUpperCase(),name:o,payload:i&&i.forEach?i.slice(0):Object.assign(this._defaultPayload(r),i),payload_type:"application/json",private:!1,publisher:this.productName,timestamp:(new Date).toISOString()};this.metadata&&(c.publisher_metadata=this.metadata),"EndpointEvents"===e&&this._log.debug("Publishing insights",JSON.stringify({endpointName:e,event:c,force:a,host:this._host}));var u={body:c,headers:{"Content-Type":"application/json","X-Twilio-Token":this.token},url:"https://"+this._host+"/v4/"+e};return new Promise((function(e,t){s._request.post(u,(function(n){n?(s.emit("error",n),t(n)):e()}))})).catch((function(e){s._log.error("Unable to post "+n+" "+o+" event to Insights. Received error: "+e)}))},s.prototype.post=function(e,t,n,o,i,r){return this._post("EndpointEvents",e,t,n,o,i,r)},s.prototype.debug=function(e,t,n,o){return this.post("debug",e,t,n,o)},s.prototype.info=function(e,t,n,o){return this.post("info",e,t,n,o)},s.prototype.warn=function(e,t,n,o){return this.post("warning",e,t,n,o)},s.prototype.error=function(e,t,n,o){return this.post("error",e,t,n,o)},s.prototype.postMetrics=function(e,t,n,o,i){var r=this;return new Promise((function(a){var s=n.map(c).map((function(e){return Object.assign(e,o)}));a(r._post("EndpointMetrics","info",e,t,s,i))}))},s.prototype.setHost=function(e){this._host=e},s.prototype.setToken=function(e){this._token=e},s.prototype.enable=function(){this._isEnabled=!0},s.prototype.disable=function(){this._isEnabled=!1},t.default=s},24468:function(e,t,n){var o=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o};Object.defineProperty(t,"__esModule",{value:!0}),t.Logger=void 0;var i=n(831),r=n(68752),a=function(){function e(t,n){this._log=e.getLogLevelInstance(n),this._prefix="[TwilioVoice]["+t+"]"}return e.getLogLevelInstance=function(t){if(!e.loglevelInstance)try{e.loglevelInstance=(t&&t.LogLevelModule?t.LogLevelModule:i).getLogger(r.PACKAGE_NAME)}catch(n){console.warn("Cannot create custom logger"),e.loglevelInstance=console}return e.loglevelInstance},e.prototype.debug=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];(e=this._log).debug.apply(e,o([this._prefix],t))},e.prototype.error=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];(e=this._log).error.apply(e,o([this._prefix],t))},e.prototype.info=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];(e=this._log).info.apply(e,o([this._prefix],t))},e.prototype.setDefaultLevel=function(e){this._log.setDefaultLevel?this._log.setDefaultLevel(e):console.warn("Logger cannot setDefaultLevel")},e.prototype.warn=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];(e=this._log).warn.apply(e,o([this._prefix],t))},e.levels=i.levels,e}();t.Logger=a.getLogLevelInstance(),t.default=a},56553:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(68752),i=n(18987),r=n(24468),a=o.SOUNDS_BASE_URL+"/outgoing.mp3",s=function(){function e(e,t,n,o){this._name=e,this._availableDevices=t,this._beforeChange=n,this._isSupported=o,this._activeDevices=new Set,this._log=new r.default("OutputDeviceCollection")}return e.prototype.delete=function(e){this._log.debug(".delete",e);var t=!!this._activeDevices.delete(e),n=this._availableDevices.get("default")||Array.from(this._availableDevices.values())[0];!this._activeDevices.size&&n&&this._activeDevices.add(n);var o=Array.from(this._activeDevices.values()).map((function(e){return e.deviceId}));return this._beforeChange(this._name,o),!!t},e.prototype.get=function(){return this._activeDevices},e.prototype.set=function(e){var t=this;if(this._log.debug(".set",e),!this._isSupported)return Promise.reject(new i.NotSupportedError("This browser does not support audio output selection"));var n=Array.isArray(e)?e:[e];if(!n.length)return Promise.reject(new i.InvalidArgumentError("Must specify at least one device to set"));var o=[],r=n.map((function(e){var n=t._availableDevices.get(e);return n||o.push(e),n}));return o.length?Promise.reject(new i.InvalidArgumentError("Devices not found: "+o.join(", "))):new Promise((function(e){e(t._beforeChange(t._name,n))})).then((function(){t._activeDevices.clear(),r.forEach(t._activeDevices.add,t._activeDevices)}))},e.prototype.test=function(e){return void 0===e&&(e=a),this._isSupported?this._activeDevices.size?Promise.all(Array.from(this._activeDevices).map((function(t){var n;return new Promise((function(t){(n=new Audio(e)).oncanplay=t})).then((function(){return n.setSinkId(t.deviceId).then((function(){return n.play()}))}))}))):Promise.reject(new i.InvalidStateError("No active output devices to test")):Promise.reject(new i.NotSupportedError("This browser does not support audio output selection"))},e}();t.default=s},37919:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)},r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.PreflightTest=void 0;var s=n(5939),c=n(55765),u=n(56546),d=n(18987),l=n(24468),p=n(90269),f=n(68752),h=function(e){function t(n,o){var r=e.call(this)||this;r._hasInsightsErrored=!1,r._log=new l.default("PreflightTest"),r._networkTiming={},r._options={codecPreferences:[c.default.Codec.PCMU,c.default.Codec.Opus],edge:"roaming",fakeMicInput:!1,logLevel:"error",signalingTimeoutMs:1e4},r._status=t.Status.Connecting,Object.assign(r._options,o),r._samples=[],r._warnings=[],r._startTime=Date.now(),r._initDevice(n,i(i({},r._options),{fileInputStream:r._options.fakeMicInput?r._getStreamFromFile():void 0}));var a=["codecPreferences","edge","fakeMicInput","logLevel","signalingTimeoutMs"],s=["audioContext","deviceFactory","fileInputStream","getRTCIceCandidateStatsReport","iceServers","rtcConfiguration"];if("object"===typeof o){var u=i({},o);Object.keys(u).forEach((function(e){a.includes(e)||s.includes(e)||delete u[e],s.includes(e)&&(u[e]=!0)})),r._log.debug(".constructor",JSON.stringify(u))}return r}return o(t,e),t.prototype.stop=function(){var e=this;this._log.debug(".stop");var t=new d.GeneralErrors.CallCancelledError;this._device?(this._device.once(u.default.EventName.Unregistered,(function(){return e._onFailed(t)})),this._device.destroy()):this._onFailed(t)},t.prototype._emitWarning=function(e,n,o){var i={name:e,description:n};o&&(i.rtcWarning=o),this._warnings.push(i),this._log.debug("#"+t.Events.Warning,JSON.stringify(i)),this.emit(t.Events.Warning,i)},t.prototype._getCallQuality=function(e){return e>4.2?t.CallQuality.Excellent:e>=4.1&&e<=4.2?t.CallQuality.Great:e>=3.7&&e<=4?t.CallQuality.Good:e>=3.1&&e<=3.6?t.CallQuality.Fair:t.CallQuality.Degraded},t.prototype._getReport=function(){var e=this._getRTCStats(),t={start:this._startTime};this._endTime&&(t.end=this._endTime,t.duration=this._endTime-this._startTime);var n={callSid:this._callSid,edge:this._edge,iceCandidateStats:this._rtcIceCandidateStatsReport.iceCandidateStats,networkTiming:this._networkTiming,samples:this._samples,selectedEdge:this._options.edge,stats:e,testTiming:t,totals:this._getRTCSampleTotals(),warnings:this._warnings},o=this._rtcIceCandidateStatsReport.selectedIceCandidatePairStats;return o&&(n.selectedIceCandidatePairStats=o,n.isTurnRequired="relay"===o.localCandidate.candidateType||"relay"===o.remoteCandidate.candidateType),e&&(n.callQuality=this._getCallQuality(e.mos.average)),n},t.prototype._getRTCSampleTotals=function(){if(this._latestSample)return i({},this._latestSample.totals)},t.prototype._getRTCStats=function(){var e=this._samples.findIndex((function(e){return"number"===typeof e.mos&&e.mos>0})),t=e>=0?this._samples.slice(e):[];if(t&&t.length)return["jitter","mos","rtt"].reduce((function(e,n){var o,r=t.map((function(e){return e[n]}));return i(i({},e),((o={})[n]={average:Number((r.reduce((function(e,t){return e+t}))/r.length).toPrecision(5)),max:Math.max.apply(Math,r),min:Math.min.apply(Math,r)},o))}),{})},t.prototype._getStreamFromFile=function(){var e=this._options.audioContext;if(!e)throw new d.NotSupportedError("Cannot fake input audio stream: AudioContext is not supported by this browser.");var t=new Audio(f.COWBELL_AUDIO_URL);t.addEventListener("canplaythrough",(function(){return t.play()})),"function"===typeof t.setAttribute&&t.setAttribute("crossorigin","anonymous");var n=e.createMediaElementSource(t),o=e.createMediaStreamDestination();return n.connect(o),o.stream},t.prototype._initDevice=function(e,t){var n=this;try{this._device=new(t.deviceFactory||u.default)(e,{codecPreferences:t.codecPreferences,edge:t.edge,fileInputStream:t.fileInputStream,logLevel:t.logLevel,preflight:!0}),this._device.once(u.default.EventName.Registered,(function(){n._onDeviceRegistered()})),this._device.once(u.default.EventName.Error,(function(e){n._onDeviceError(e)})),this._device.register()}catch(o){return void setTimeout((function(){n._onFailed(o)}))}this._signalingTimeoutTimer=setTimeout((function(){n._onDeviceError(new d.SignalingErrors.ConnectionError("WebSocket Connection Timeout"))}),t.signalingTimeoutMs)},t.prototype._onDeviceError=function(e){this._device.destroy(),this._onFailed(e)},t.prototype._onDeviceRegistered=function(){return r(this,void 0,void 0,(function(){var e,t,n=this;return a(this,(function(o){switch(o.label){case 0:return clearTimeout(this._echoTimer),clearTimeout(this._signalingTimeoutTimer),e=this,[4,this._device.connect({rtcConfiguration:this._options.rtcConfiguration})];case 1:return e._call=o.sent(),this._networkTiming.signaling={start:Date.now()},this._setupCallHandlers(this._call),this._edge=this._device.edge||void 0,this._options.fakeMicInput&&(this._echoTimer=setTimeout((function(){return n._device.disconnectAll()}),f.ECHO_TEST_DURATION),(t=this._device.audio)&&(t.disconnect(!1),t.outgoing(!1))),this._call.once("disconnect",(function(){n._device.once(u.default.EventName.Unregistered,(function(){return n._onUnregistered()})),n._device.destroy()})),this._call._publisher.on("error",(function(){n._hasInsightsErrored||n._emitWarning("insights-connection-error","Received an error when attempting to connect to Insights gateway"),n._hasInsightsErrored=!0})),[2]}}))}))},t.prototype._onFailed=function(e){clearTimeout(this._echoTimer),clearTimeout(this._signalingTimeoutTimer),this._releaseHandlers(),this._endTime=Date.now(),this._status=t.Status.Failed,this._log.debug("#"+t.Events.Failed,e),this.emit(t.Events.Failed,e)},t.prototype._onUnregistered=function(){var e=this;setTimeout((function(){e._status!==t.Status.Failed&&(clearTimeout(e._echoTimer),clearTimeout(e._signalingTimeoutTimer),e._releaseHandlers(),e._endTime=Date.now(),e._status=t.Status.Completed,e._report=e._getReport(),e._log.debug("#"+t.Events.Completed,JSON.stringify(e._report)),e.emit(t.Events.Completed,e._report))}),10)},t.prototype._releaseHandlers=function(){[this._device,this._call].forEach((function(e){e&&e.eventNames().forEach((function(t){return e.removeAllListeners(t)}))}))},t.prototype._setupCallHandlers=function(e){var n=this;this._options.fakeMicInput&&e.once("volume",(function(){e._mediaHandler.outputs.forEach((function(e){return e.audio.muted=!0}))})),e.on("warning",(function(e,t){n._emitWarning(e,"Received an RTCWarning. See .rtcWarning for the RTCWarning",t)})),e.once("accept",(function(){n._callSid=e._mediaHandler.callSid,n._status=t.Status.Connected,n._log.debug("#"+t.Events.Connected),n.emit(t.Events.Connected)})),e.on("sample",(function(o){return r(n,void 0,void 0,(function(){var n;return a(this,(function(i){switch(i.label){case 0:return this._latestSample?[3,2]:(n=this,[4,(this._options.getRTCIceCandidateStatsReport||p.getRTCIceCandidateStatsReport)(e._mediaHandler.version.pc)]);case 1:n._rtcIceCandidateStatsReport=i.sent(),i.label=2;case 2:return this._latestSample=o,this._samples.push(o),this._log.debug("#"+t.Events.Sample,JSON.stringify(o)),this.emit(t.Events.Sample,o),[2]}}))}))})),[{reportLabel:"peerConnection",type:"pcconnection"},{reportLabel:"ice",type:"iceconnection"},{reportLabel:"dtls",type:"dtlstransport"},{reportLabel:"signaling",type:"signaling"}].forEach((function(t){var o=t.type,i=t.reportLabel,r="on"+o+"statechange",a=e._mediaHandler[r];e._mediaHandler[r]=function(e){var t=n._networkTiming[i]=n._networkTiming[i]||{start:0};"connecting"===e||"checking"===e?t.start=Date.now():"connected"!==e&&"stable"!==e||t.duration||(t.end=Date.now(),t.duration=t.end-t.start),a(e)}}))},Object.defineProperty(t.prototype,"callSid",{get:function(){return this._callSid},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"endTime",{get:function(){return this._endTime},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"latestSample",{get:function(){return this._latestSample},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"report",{get:function(){return this._report},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"startTime",{get:function(){return this._startTime},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"status",{get:function(){return this._status},enumerable:!1,configurable:!0}),t}(s.EventEmitter);t.PreflightTest=h,function(e){!function(e){e.Excellent="excellent",e.Great="great",e.Good="good",e.Fair="fair",e.Degraded="degraded"}(e.CallQuality||(e.CallQuality={})),function(e){e.Completed="completed",e.Connected="connected",e.Failed="failed",e.Sample="sample",e.Warning="warning"}(e.Events||(e.Events={})),function(e){e.Connecting="connecting",e.Connected="connected",e.Completed="completed",e.Failed="failed"}(e.Status||(e.Status={}))}(h=t.PreflightTest||(t.PreflightTest={})),t.PreflightTest=h},11149:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=n(5939),r=n(68752),a=n(18987),s=n(24468),c=n(82195),u=function(e){function t(n,o,i){var r=e.call(this)||this;if(!(r instanceof t))return new t(n,o,i);var a={TransportFactory:c.default};for(var u in i=i||{},a)u in i||(i[u]=a[u]);r.options=i,r.token=n||"",r.status="disconnected",r.gateway=null,r.region=null,r._messageQueue=[],r._preferredUri=null,r._uris=o,r._handleTransportClose=r._handleTransportClose.bind(r),r._handleTransportError=r._handleTransportError.bind(r),r._handleTransportMessage=r._handleTransportMessage.bind(r),r._handleTransportOpen=r._handleTransportOpen.bind(r),r._log=new s.default("PStream"),r.on("error",(function(){r._log.warn("Unexpected error handled in pstream")}));var d=r;return r.addListener("ready",(function(){d.status="ready"})),r.addListener("offline",(function(){d.status="offline"})),r.addListener("close",(function(){d._log.info('Received "close" from server. Destroying PStream...'),d._destroy()})),r.transport=new r.options.TransportFactory(r._uris,{backoffMaxMs:r.options.backoffMaxMs,maxPreferredDurationMs:r.options.maxPreferredDurationMs}),Object.defineProperties(r,{uri:{enumerable:!0,get:function(){return this.transport.uri}}}),r.transport.on("close",r._handleTransportClose),r.transport.on("error",r._handleTransportError),r.transport.on("message",r._handleTransportMessage),r.transport.on("open",r._handleTransportOpen),r.transport.open(),r}return o(t,e),t}(i.EventEmitter);function d(){var e="undefined"!==typeof navigator?navigator:{};return{browser:{platform:e.platform||"unknown",userAgent:e.userAgent||"unknown"},p:"browser",plugin:"rtc",v:r.RELEASE_VERSION}}u.prototype._handleTransportClose=function(){this.emit("transportClose"),"disconnected"!==this.status&&("offline"!==this.status&&this.emit("offline",this),this.status="disconnected")},u.prototype._handleTransportError=function(e){e?this.emit("error","undefined"!==typeof e.code?{error:e}:e):this.emit("error",{error:{code:31e3,message:"Websocket closed without a provided reason",twilioError:new a.SignalingErrors.ConnectionDisconnected}})},u.prototype._handleTransportMessage=function(e){if(e&&e.data&&"string"===typeof e.data){var t=JSON.parse(e.data),n=t.type,o=t.payload,i=void 0===o?{}:o;this.gateway=i.gateway||this.gateway,this.region=i.region||this.region,"error"===n&&i.error&&(i.error.twilioError=new a.SignalingErrors.ConnectionError),this.emit(n,i)}},u.prototype._handleTransportOpen=function(){var e=this;this.status="connected",this.setToken(this.token),this.emit("transportOpen"),this._messageQueue.splice(0,this._messageQueue.length).forEach((function(t){return e._publish.apply(e,t)}))},u.toString=function(){return"[Twilio.PStream class]"},u.prototype.toString=function(){return"[Twilio.PStream instance]"},u.prototype.setToken=function(e){this._log.info("Setting token and publishing listen"),this.token=e;var t=0,n=this.options.maxPreferredDurationMs;this._log.info("maxPreferredDurationMs:"+n),"number"===typeof n&&n>=0&&(t=Math.min(Math.ceil(n/1e3),30)),this._log.info("reconnectTimeout:"+t);var o={browserinfo:d(),reconnectTimeout:t,token:e};this._publish("listen",o)},u.prototype.sendMessage=function(e,t,n,o,i){void 0===n&&(n="application/json");var r={callsid:e,content:t,contenttype:n,messagetype:o,voiceeventsid:i};this._publish("message",r,!0)},u.prototype.register=function(e){var t={media:e};this._publish("register",t,!0)},u.prototype.invite=function(e,t,n){var o={callsid:t,sdp:e,twilio:n?{params:n}:{}};this._publish("invite",o,!0)},u.prototype.reconnect=function(e,t,n){var o={callsid:t,reconnect:n,sdp:e,twilio:{}};this._publish("invite",o,!0)},u.prototype.answer=function(e,t){this._publish("answer",{sdp:e,callsid:t},!0)},u.prototype.dtmf=function(e,t){this._publish("dtmf",{callsid:e,dtmf:t},!0)},u.prototype.hangup=function(e,t){var n=t?{callsid:e,message:t}:{callsid:e};this._publish("hangup",n,!0)},u.prototype.reject=function(e){this._publish("reject",{callsid:e},!0)},u.prototype.reinvite=function(e,t){this._publish("reinvite",{sdp:e,callsid:t},!1)},u.prototype._destroy=function(){this.transport.removeListener("close",this._handleTransportClose),this.transport.removeListener("error",this._handleTransportError),this.transport.removeListener("message",this._handleTransportMessage),this.transport.removeListener("open",this._handleTransportOpen),this.transport.close(),this.emit("offline",this)},u.prototype.destroy=function(){return this._log.info("PStream.destroy() called..."),this._destroy(),this},u.prototype.updatePreferredURI=function(e){this._preferredUri=e,this.transport.updatePreferredURI(e)},u.prototype.updateURIs=function(e){this._uris=e,this.transport.updateURIs(this._uris)},u.prototype.publish=function(e,t){return this._publish(e,t,!0)},u.prototype._publish=function(e,t,n){var o=JSON.stringify({payload:t,type:e,version:"1.6"});!!this.transport.send(o)||(this.emit("error",{error:{code:31009,message:"No transport available to send or receive messages",twilioError:new a.GeneralErrors.TransportError}}),n&&this._messageQueue.push([e,t,!0]))},t.default=u},59057:function(e,t,n){var o;Object.defineProperty(t,"__esModule",{value:!0}),t.getRegionShortcode=t.getChunderURIs=t.createSignalingEndpointURL=t.createEventGatewayURI=t.defaultEdge=t.regionToEdge=t.regionShortcodes=t.Region=t.Edge=void 0;var i,r,a=n(18987);!function(e){e.Sydney="sydney",e.SaoPaulo="sao-paulo",e.Dublin="dublin",e.Frankfurt="frankfurt",e.Tokyo="tokyo",e.Singapore="singapore",e.Ashburn="ashburn",e.Umatilla="umatilla",e.Roaming="roaming",e.AshburnIx="ashburn-ix",e.SanJoseIx="san-jose-ix",e.LondonIx="london-ix",e.FrankfurtIx="frankfurt-ix",e.SingaporeIx="singapore-ix",e.SydneyIx="sydney-ix",e.TokyoIx="tokyo-ix"}(i=t.Edge||(t.Edge={})),function(e){e.Au1="au1",e.Au1Ix="au1-ix",e.Br1="br1",e.De1="de1",e.De1Ix="de1-ix",e.Gll="gll",e.Ie1="ie1",e.Ie1Ix="ie1-ix",e.Ie1Tnx="ie1-tnx",e.Jp1="jp1",e.Jp1Ix="jp1-ix",e.Sg1="sg1",e.Sg1Ix="sg1-ix",e.Sg1Tnx="sg1-tnx",e.Us1="us1",e.Us1Ix="us1-ix",e.Us1Tnx="us1-tnx",e.Us2="us2",e.Us2Ix="us2-ix",e.Us2Tnx="us2-tnx"}(r=t.Region||(t.Region={})),t.regionShortcodes={ASIAPAC_SINGAPORE:r.Sg1,ASIAPAC_SYDNEY:r.Au1,ASIAPAC_TOKYO:r.Jp1,EU_FRANKFURT:r.De1,EU_IRELAND:r.Ie1,SOUTH_AMERICA_SAO_PAULO:r.Br1,US_EAST_VIRGINIA:r.Us1,US_WEST_OREGON:r.Us2},t.regionToEdge=((o={})[r.Au1]=i.Sydney,o[r.Br1]=i.SaoPaulo,o[r.Ie1]=i.Dublin,o[r.De1]=i.Frankfurt,o[r.Jp1]=i.Tokyo,o[r.Sg1]=i.Singapore,o[r.Us1]=i.Ashburn,o[r.Us2]=i.Umatilla,o[r.Gll]=i.Roaming,o[r.Us1Ix]=i.AshburnIx,o[r.Us2Ix]=i.SanJoseIx,o[r.Ie1Ix]=i.LondonIx,o[r.De1Ix]=i.FrankfurtIx,o[r.Sg1Ix]=i.SingaporeIx,o[r.Au1Ix]=i.SydneyIx,o[r.Jp1Ix]=i.TokyoIx,o[r.Us1Tnx]=i.AshburnIx,o[r.Us2Tnx]=i.AshburnIx,o[r.Ie1Tnx]=i.LondonIx,o[r.Sg1Tnx]=i.SingaporeIx,o),t.defaultEdge=i.Roaming;function s(e){return"voice-js."+e+".twilio.com"}t.createEventGatewayURI=function(e){return e?"eventgw."+e+".twilio.com":"eventgw.twilio.com"},t.createSignalingEndpointURL=function(e){return"wss://"+e+"/signal"},t.getChunderURIs=function(e){if(e&&"string"!==typeof e&&!Array.isArray(e))throw new a.InvalidArgumentError("If `edge` is provided, it must be of type `string` or an array of strings.");return e?(Array.isArray(e)?e:[e]).map((function(e){return s(e)})):[s(t.defaultEdge)]},t.getRegionShortcode=function(e){return t.regionShortcodes[e]||null}},96436:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e,t,n){var o=JSON.stringify(t.body||{}),i=new Headers;t.headers=t.headers||[],Object.entries(t.headers).forEach((function(e){var t=e[0],n=e[1];return i.append(t,n)})),fetch(t.url,{body:o,headers:i,method:e}).then((function(e){return e.text()}),n).then((function(e){return n(null,e)}),n)};n.get=function(e,t){return new this("GET",e,t)},n.post=function(e,t){return new this("POST",e,t)},t.default=n},60658:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(18987),i=n(62997);t.default=function(e,t){return(t=t||{}).util=t.util||i,t.navigator=t.navigator||("undefined"!==typeof navigator?navigator:null),new Promise((function(n,i){if(!t.navigator)throw new o.NotSupportedError("getUserMedia is not supported");switch("function"){case typeof(t.navigator.mediaDevices&&t.navigator.mediaDevices.getUserMedia):return n(t.navigator.mediaDevices.getUserMedia(e));case typeof t.navigator.webkitGetUserMedia:return t.navigator.webkitGetUserMedia(e,n,i);case typeof t.navigator.mozGetUserMedia:return t.navigator.mozGetUserMedia(e,n,i);case typeof t.navigator.getUserMedia:return t.navigator.getUserMedia(e,n,i);default:throw new o.NotSupportedError("getUserMedia is not supported")}})).catch((function(e){throw t.util.isFirefox()&&"NotReadableError"===e.name?new o.NotSupportedError("Firefox does not currently support opening multiple audio input trackssimultaneously, even across different tabs.\nRelated Bugzilla thread: https://bugzilla.mozilla.org/show_bug.cgi?id=1299324"):e}))}},55848:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.IceCandidate=void 0;var n=function(){function e(e,t){var n;void 0===t&&(t=!1),this.deleted=!1;var o=e.candidate.split("network-cost ");o[1]&&(n=parseInt(o[1],10)),this.candidateType=e.type,this.ip=e.ip||e.address,this.isRemote=t,this.networkCost=n,this.port=e.port,this.priority=e.priority,this.protocol=e.protocol,this.relatedAddress=e.relatedAddress,this.relatedPort=e.relatedPort,this.tcpType=e.tcpType,this.transportId=e.sdpMid}return e.prototype.toPayload=function(){return{candidate_type:this.candidateType,deleted:this.deleted,ip:this.ip,is_remote:this.isRemote,"network-cost":this.networkCost,port:this.port,priority:this.priority,protocol:this.protocol,related_address:this.relatedAddress,related_port:this.relatedPort,tcp_type:this.tcpType,transport_id:this.transportId}},e}();t.IceCandidate=n},48785:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.PeerConnection=t.getMediaEngine=t.enabled=void 0;var o=n(13632);t.PeerConnection=o.default;var i=n(89261);t.enabled=function(){return i.default.test()},t.getMediaEngine=function(){return"undefined"!==typeof RTCIceGatherer?"ORTC":"WebRTC"}},9850:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=32767,o="undefined"!==typeof window?window.RTCStatsReport:void 0;function i(e){if(!(this instanceof i))return new i(e);var t=this;Object.defineProperties(this,{_map:{value:e},size:{enumerable:!0,get:function(){return t._map.size}}}),this[Symbol.iterator]=e[Symbol.iterator]}function r(e){return{bytesReceived:void 0,bytesSent:void 0,dtlsState:void 0,id:e.id,localCertificateId:e.stat("localCertificateId"),remoteCertificateId:e.stat("remoteCertificateId"),rtcpTransportStatsId:void 0,selectedCandidatePairId:e.stat("selectedCandidatePairId"),timestamp:Date.parse(e.timestamp),type:"transport"}}function a(e,t){return{associateStatsId:void 0,codecId:"codec-"+e.id,firCount:t?d(e,"googFirsSent"):void 0,id:e.id,isRemote:void 0,mediaType:e.stat("mediaType"),nackCount:d(e,t?"googNacksSent":"googNacksReceived"),pliCount:d(e,t?"googPlisSent":"googPlisReceived"),qpSum:d(e,"qpSum"),sliCount:void 0,ssrc:e.stat("ssrc"),timestamp:Date.parse(e.timestamp),trackId:"track-"+e.id,transportId:e.stat("transportId")}}function s(e,t){return{candidateType:u(e.stat("candidateType")),deleted:void 0,id:e.id,ip:e.stat("ipAddress"),isRemote:t,port:d(e,"portNumber"),priority:l(e,"priority"),protocol:e.stat("transport"),relayProtocol:void 0,timestamp:Date.parse(e.timestamp),transportId:void 0,type:t?"remote-candidate":"local-candidate",url:void 0}}function c(e){return isNaN(e)||""===e?void 0:parseInt(e,10)/1e3}function u(e){switch(e){case"peerreflexive":return"prflx";case"serverreflexive":return"srflx";default:return e}}function d(e,t){var n=e.stat(t);return f(e,t)?parseInt(n,10):void 0}function l(e,t){var n=e.stat(t);return f(e,t)?parseFloat(n):void 0}function p(e,t){var n=e.stat(t);return f(e,t)?"true"===n||!0===n:void 0}function f(e,t){var n=e.stat(t);return"undefined"!==typeof n&&""!==n}o&&(i.prototype=Object.create(o.prototype),i.prototype.constructor=i),["entries","forEach","get","has","keys","values"].forEach((function(e){i.prototype[e]=function(){for(var t,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return(t=this._map)[e].apply(t,n)}})),i.fromArray=function(e){return new i(e.reduce((function(e,t){return e.set(t.id,t),e}),new Map))},i.fromRTCStatsResponse=function(e){var t,o=new Map,u=e.result().reduce((function(e,i){var u=i.id;switch(i.type){case"googCertificate":e.set(u,function(e){return{base64Certificate:e.stat("googDerBase64"),fingerprint:e.stat("googFingerprint"),fingerprintAlgorithm:e.stat("googFingerprintAlgorithm"),id:e.id,issuerCertificateId:e.stat("googIssuerId"),timestamp:Date.parse(e.timestamp),type:"certificate"}}(i));break;case"datachannel":e.set(u,function(e){return{bytesReceived:void 0,bytesSent:void 0,datachannelid:e.stat("datachannelid"),id:e.id,label:e.stat("label"),messagesReceived:void 0,messagesSent:void 0,protocol:e.stat("protocol"),state:e.stat("state"),timestamp:Date.parse(e.timestamp),transportId:e.stat("transportId"),type:"data-channel"}}(i));break;case"googCandidatePair":p(i,"googActiveConnection")&&(t=u),e.set(u,function(e){return{availableIncomingBitrate:void 0,availableOutgoingBitrate:void 0,bytesReceived:d(e,"bytesReceived"),bytesSent:d(e,"bytesSent"),consentRequestsSent:d(e,"consentRequestsSent"),currentRoundTripTime:c(e.stat("googRtt")),id:e.id,lastPacketReceivedTimestamp:void 0,lastPacketSentTimestamp:void 0,localCandidateId:e.stat("localCandidateId"),nominated:void 0,priority:void 0,readable:void 0,remoteCandidateId:e.stat("remoteCandidateId"),requestsReceived:d(e,"requestsReceived"),requestsSent:d(e,"requestsSent"),responsesReceived:d(e,"responsesReceived"),responsesSent:d(e,"responsesSent"),retransmissionsReceived:void 0,retransmissionsSent:void 0,state:void 0,timestamp:Date.parse(e.timestamp),totalRoundTripTime:void 0,transportId:e.stat("googChannelId"),type:"candidate-pair",writable:p(e,"googWritable")}}(i));break;case"localcandidate":e.set(u,s(i,!1));break;case"remotecandidate":e.set(u,s(i,!0));break;case"ssrc":f(i,"packetsReceived")?e.set("rtp-"+u,function(e){var t=a(e,!0);return Object.assign(t,{burstDiscardCount:void 0,burstDiscardRate:void 0,burstLossCount:void 0,burstLossRate:void 0,burstPacketsDiscarded:void 0,burstPacketsLost:void 0,bytesReceived:d(e,"bytesReceived"),fractionLost:void 0,framesDecoded:d(e,"framesDecoded"),gapDiscardRate:void 0,gapLossRate:void 0,jitter:c(e.stat("googJitterReceived")),packetsDiscarded:void 0,packetsLost:d(e,"packetsLost"),packetsReceived:d(e,"packetsReceived"),packetsRepaired:void 0,roundTripTime:c(e.stat("googRtt")),type:"inbound-rtp"}),t}(i)):e.set("rtp-"+u,function(e){var t=a(e,!1);return Object.assign(t,{bytesSent:d(e,"bytesSent"),framesEncoded:d(e,"framesEncoded"),packetsSent:d(e,"packetsSent"),remoteTimestamp:void 0,targetBitrate:void 0,type:"outbound-rtp"}),t}(i)),e.set("track-"+u,function(e){return{audioLevel:f(e,"audioOutputLevel")?d(e,"audioOutputLevel")/n:(d(e,"audioInputLevel")||0)/n,detached:void 0,echoReturnLoss:l(e,"googEchoCancellationReturnLoss"),echoReturnLossEnhancement:l(e,"googEchoCancellationReturnLossEnhancement"),ended:void 0,frameHeight:f(e,"googFrameHeightReceived")?d(e,"googFrameHeightReceived"):d(e,"googFrameHeightSent"),frameWidth:f(e,"googFrameWidthReceived")?d(e,"googFrameWidthReceived"):d(e,"googFrameWidthSent"),framesCorrupted:void 0,framesDecoded:d(e,"framesDecoded"),framesDropped:void 0,framesPerSecond:void 0,framesReceived:void 0,framesSent:d(e,"framesEncoded"),fullFramesLost:void 0,id:e.id,kind:e.stat("mediaType"),partialFramesLost:void 0,remoteSource:void 0,ssrcIds:void 0,timestamp:Date.parse(e.timestamp),trackIdentifier:e.stat("googTrackId"),type:"track"}}(i)),e.set("codec-"+u,function(e){return{channels:void 0,clockRate:void 0,id:e.id,implementation:void 0,mimeType:e.stat("mediaType")+"/"+e.stat("googCodecName"),payloadType:void 0,sdpFmtpLine:void 0,timestamp:Date.parse(e.timestamp),type:"codec"}}(i));break;case"googComponent":var h=r(i);o.set(h.selectedCandidatePairId,u),e.set(u,r(i))}return e}),new Map);if(t){var h=o.get(t);h&&(u.get(h).dtlsState="connected")}return new i(u)},t.default=i},90497:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isNonNegativeNumber=t.calculate=void 0;var n=94.768;function o(e,t,o){if("number"!==typeof e||"number"!==typeof t||"number"!==typeof o||!i(e)||!i(t)||!i(o))return null;var r=e+2*t+10,a=0;switch(!0){case r<160:a=n-r/40;break;case r<1e3:a=n-(r-120)/10}if(!0===o<=a/2.5)a=Math.max(a-2.5*o,6.52);else a=0;return 1+.035*a+7e-6*a*(a-60)*(100-a)}function i(e){return"number"===typeof e&&!isNaN(e)&&isFinite(e)&&e>=0}t.calculate=o,t.isNonNegativeNumber=i,t.default={calculate:o,isNonNegativeNumber:i}},13632:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(18987),i=n(24468),r=n(62997),a=n(89261),s=n(40230);function c(e,t,n){if(!e||!t)throw new o.InvalidArgumentError("Audiohelper, and pstream are required arguments");if(!(this instanceof c))return new c(e,t,n);function a(){this._log.warn("Unexpected noop call in peerconnection")}this._log=new i.default("PeerConnection"),this.onaudio=a,this.onopen=a,this.onerror=a,this.onclose=a,this.ondisconnected=a,this.onfailed=a,this.onconnected=a,this.onreconnected=a,this.onsignalingstatechange=a,this.ondtlstransportstatechange=a,this.onicegatheringfailure=a,this.onicegatheringstatechange=a,this.oniceconnectionstatechange=a,this.onpcconnectionstatechange=a,this.onicecandidate=a,this.onselectedcandidatepairchange=a,this.onvolume=a,this.version=null,this.pstream=t,this.stream=null,this.sinkIds=new Set(["default"]),this.outputs=new Map,this.status="connecting",this.callSid=null,this.isMuted=!1;var s="undefined"!==typeof window&&(window.AudioContext||window.webkitAudioContext);return this._isSinkSupported=!!s&&"undefined"!==typeof HTMLAudioElement&&HTMLAudioElement.prototype.setSinkId,this._audioContext=s&&e._audioContext,this._audioHelper=e,this._hasIceCandidates=!1,this._hasIceGatheringFailures=!1,this._iceGatheringTimeoutId=null,this._masterAudio=null,this._masterAudioDeviceId=null,this._mediaStreamSource=null,this._dtmfSender=null,this._dtmfSenderUnsupported=!1,this._callEvents=[],this._nextTimeToPublish=Date.now(),this._onAnswerOrRinging=a,this._onHangup=a,this._remoteStream=null,this._shouldManageStream=!0,this._iceState="new",this._isUnifiedPlan=n.isUnifiedPlan,this.options=n=n||{},this.navigator=n.navigator||("undefined"!==typeof navigator?navigator:null),this.util=n.util||r,this.codecPreferences=n.codecPreferences,this}function u(e,t){"function"===typeof e.addTrack?t.getAudioTracks().forEach((function(n){e.addTrack(n,t)})):e.addStream(t)}function d(e){var t="undefined"!==typeof MediaStream?new MediaStream:new webkitMediaStream;return e.getAudioTracks().forEach(t.addTrack,t),t}function l(e,t){if("undefined"!==typeof e.srcObject)e.srcObject=t;else if("undefined"!==typeof e.mozSrcObject)e.mozSrcObject=t;else{if("undefined"===typeof e.src)return!1;var n=e.options.window||window;e.src=(n.URL||n.webkitURL).createObjectURL(t)}return!0}c.prototype.uri=function(){return this._uri},c.prototype.openDefaultDeviceWithConstraints=function(e){return this._audioHelper._openDefaultDeviceWithConstraints(e).then(this._setInputTracksFromStream.bind(this,!1))},c.prototype.setInputTracksFromStream=function(e){var t=this;return this._setInputTracksFromStream(!0,e).then((function(){t._shouldManageStream=!1}))},c.prototype._createAnalyser=function(e,t){t=Object.assign({fftSize:32,smoothingTimeConstant:.3},t);var n=e.createAnalyser();for(var o in t)n[o]=t[o];return n},c.prototype._setVolumeHandler=function(e){this.onvolume=e},c.prototype._startPollingVolume=function(){if(this._audioContext&&this.stream&&this._remoteStream){var e=this._audioContext,t=(this._inputAnalyser=this._createAnalyser(e)).frequencyBinCount,n=new Uint8Array(t);this._inputAnalyser2=this._createAnalyser(e,{maxDecibels:0,minDecibels:-127,smoothingTimeConstant:0});var o=(this._outputAnalyser=this._createAnalyser(e)).frequencyBinCount,i=new Uint8Array(o);this._outputAnalyser2=this._createAnalyser(e,{maxDecibels:0,minDecibels:-127,smoothingTimeConstant:0}),this._updateInputStreamSource(this.stream),this._updateOutputStreamSource(this._remoteStream);var r=this;setTimeout((function e(){if(r._audioContext){if("closed"===r.status)return r._inputAnalyser.disconnect(),r._outputAnalyser.disconnect(),r._inputAnalyser2.disconnect(),void r._outputAnalyser2.disconnect();r._inputAnalyser.getByteFrequencyData(n);var t=r.util.average(n);r._inputAnalyser2.getByteFrequencyData(n);var o=r.util.average(n);r._outputAnalyser.getByteFrequencyData(i);var a=r.util.average(i);r._outputAnalyser2.getByteFrequencyData(i);var s=r.util.average(i);r.onvolume(t/255,a/255,o,s),setTimeout(e,50)}}),50)}},c.prototype._stopStream=function(){this._shouldManageStream&&this._audioHelper._stopDefaultInputDeviceStream()},c.prototype._updateInputStreamSource=function(e){this._inputStreamSource&&this._inputStreamSource.disconnect();try{this._inputStreamSource=this._audioContext.createMediaStreamSource(e),this._inputStreamSource.connect(this._inputAnalyser),this._inputStreamSource.connect(this._inputAnalyser2)}catch(t){this._log.warn("Unable to update input MediaStreamSource",t),this._inputStreamSource=null}},c.prototype._updateOutputStreamSource=function(e){this._outputStreamSource&&this._outputStreamSource.disconnect();try{this._outputStreamSource=this._audioContext.createMediaStreamSource(e),this._outputStreamSource.connect(this._outputAnalyser),this._outputStreamSource.connect(this._outputAnalyser2)}catch(t){this._log.warn("Unable to update output MediaStreamSource",t),this._outputStreamSource=null}},c.prototype._setInputTracksFromStream=function(e,t){return this._isUnifiedPlan?this._setInputTracksForUnifiedPlan(e,t):this._setInputTracksForPlanB(e,t)},c.prototype._setInputTracksForPlanB=function(e,t){var n=this;if(!t)return Promise.reject(new o.InvalidArgumentError("Can not set input stream to null while in a call"));if(!t.getAudioTracks().length)return Promise.reject(new o.InvalidArgumentError("Supplied input stream has no audio tracks"));var i,r,a=this.stream;return a?(this._stopStream(),i=this.version.pc,r=a,"function"===typeof i.removeTrack?i.getSenders().forEach((function(e){i.removeTrack(e)})):i.removeStream(r),a.getAudioTracks().forEach(a.removeTrack,a),t.getAudioTracks().forEach(a.addTrack,a),u(this.version.pc,t),this._updateInputStreamSource(this.stream)):this.stream=e?d(t):t,this.mute(this.isMuted),this.version?new Promise((function(e,t){n.version.createOffer(n.options.maxAverageBitrate,n.codecPreferences,{audio:!0},(function(){n.version.processAnswer(n.codecPreferences,n._answerSdp,(function(){e(n.stream)}),t)}),t)})):Promise.resolve(this.stream)},c.prototype._setInputTracksForUnifiedPlan=function(e,t){var n=this;if(!t)return Promise.reject(new o.InvalidArgumentError("Can not set input stream to null while in a call"));if(!t.getAudioTracks().length)return Promise.reject(new o.InvalidArgumentError("Supplied input stream has no audio tracks"));var i=this.stream,r=function(){return n.mute(n.isMuted),Promise.resolve(n.stream)};return i?(this._shouldManageStream&&this._stopStream(),this._sender||(this._sender=this.version.pc.getSenders()[0]),this._sender.replaceTrack(t.getAudioTracks()[0]).then((function(){return n._updateInputStreamSource(t),n.stream=e?d(t):t,r()}))):(this.stream=e?d(t):t,r())},c.prototype._onInputDevicesChanged=function(){this.stream&&(this.stream.getAudioTracks().every((function(e){return"ended"===e.readyState}))&&this._shouldManageStream&&this.openDefaultDeviceWithConstraints({audio:!0}))},c.prototype._onIceGatheringFailure=function(e){this._hasIceGatheringFailures=!0,this.onicegatheringfailure(e)},c.prototype._onMediaConnectionStateChange=function(e){var t,n=this._iceState;if(n!==e&&("connected"===e||"disconnected"===e||"failed"===e))switch(this._iceState=e,e){case"connected":"disconnected"===n||"failed"===n?(t="ICE liveliness check succeeded. Connection with Twilio restored",this._log.info(t),this.onreconnected(t)):(t="Media connection established.",this._log.info(t),this.onconnected(t)),this._stopIceGatheringTimeout(),this._hasIceGatheringFailures=!1;break;case"disconnected":t="ICE liveliness check failed. May be having trouble connecting to Twilio",this._log.warn(t),this.ondisconnected(t);break;case"failed":t="Connection with Twilio was interrupted.",this._log.warn(t),this.onfailed(t)}},c.prototype._setSinkIds=function(e){return this._isSinkSupported?(this.sinkIds=new Set(e.forEach?e:[e]),this.version?this._updateAudioOutputs():Promise.resolve()):Promise.reject(new o.NotSupportedError("Audio output selection is not supported by this browser"))},c.prototype._startIceGatheringTimeout=function(){var e=this;this._stopIceGatheringTimeout(),this._iceGatheringTimeoutId=setTimeout((function(){e._onIceGatheringFailure("timeout")}),15e3)},c.prototype._stopIceGatheringTimeout=function(){clearInterval(this._iceGatheringTimeoutId)},c.prototype._updateAudioOutputs=function(){var e=Array.from(this.sinkIds).filter((function(e){return!this.outputs.has(e)}),this),t=Array.from(this.outputs.keys()).filter((function(e){return!this.sinkIds.has(e)}),this),n=this,o=e.map(this._createAudioOutput,this);return Promise.all(o).then((function(){return Promise.all(t.map(n._removeAudioOutput,n))}))},c.prototype._createAudio=function(e){var t=new Audio(e);return this.onaudio(t),t},c.prototype._createAudioOutput=function(e){var t=null;this._mediaStreamSource&&(t=this._audioContext.createMediaStreamDestination(),this._mediaStreamSource.connect(t));var n=this._createAudio();l(n,t&&t.stream?t.stream:this.pcStream);var o=this;return n.setSinkId(e).then((function(){return n.play()})).then((function(){o.outputs.set(e,{audio:n,dest:t})}))},c.prototype._removeAudioOutputs=function(){return this._masterAudio&&"undefined"!==typeof this._masterAudioDeviceId&&(this._disableOutput(this,this._masterAudioDeviceId),this.outputs.delete(this._masterAudioDeviceId),this._masterAudioDeviceId=null,this._masterAudio.paused||this._masterAudio.pause(),"undefined"!==typeof this._masterAudio.srcObject?this._masterAudio.srcObject=null:this._masterAudio.src="",this._masterAudio=null),Array.from(this.outputs.keys()).map(this._removeAudioOutput,this)},c.prototype._disableOutput=function(e,t){var n=e.outputs.get(t);n&&(n.audio&&(n.audio.pause(),n.audio.src=""),n.dest&&n.dest.disconnect())},c.prototype._reassignMasterOutput=function(e,t){var n=e.outputs.get(t);e.outputs.delete(t);var o=this,i=Array.from(e.outputs.keys())[0]||"default";return n.audio.setSinkId(i).then((function(){o._disableOutput(e,i),e.outputs.set(i,n),e._masterAudioDeviceId=i})).catch((function(){e.outputs.set(t,n),o._log.info("Could not reassign master output. Attempted to roll back.")}))},c.prototype._removeAudioOutput=function(e){return this._masterAudioDeviceId===e?this._reassignMasterOutput(this,e):(this._disableOutput(this,e),this.outputs.delete(e),Promise.resolve())},c.prototype._onAddTrack=function(e,t){var n=e._masterAudio=this._createAudio();l(n,t),n.play();var o=Array.from(e.outputs.keys())[0]||"default";e._masterAudioDeviceId=o,e.outputs.set(o,{audio:n});try{e._mediaStreamSource=e._audioContext.createMediaStreamSource(t)}catch(i){this._log.warn("Unable to create a MediaStreamSource from onAddTrack",i),this._mediaStreamSource=null}e.pcStream=t,e._updateAudioOutputs()},c.prototype._fallbackOnAddTrack=function(e,t){var n=document&&document.createElement("audio");n.autoplay=!0,l(n,t)||e._log.info("Error attaching stream to element."),e.outputs.set("default",{audio:n})},c.prototype._setEncodingParameters=function(e){if(e&&this._sender&&"function"===typeof this._sender.getParameters&&"function"===typeof this._sender.setParameters){var t=this._sender.getParameters();(t.priority||t.encodings&&t.encodings.length)&&(t.priority="high",t.encodings&&t.encodings.length&&t.encodings.forEach((function(e){e.priority="high",e.networkPriority="high"})),this._sender.setParameters(t))}},c.prototype._setupPeerConnection=function(e){var t=this,n=this,o=new(this.options.rtcpcFactory||a.default)({RTCPeerConnection:this.options.RTCPeerConnection});o.create(e),u(o.pc,this.stream);var i="ontrack"in o.pc?"ontrack":"onaddstream";return o.pc[i]=function(e){var i=n._remoteStream=e.stream||e.streams[0];"function"===typeof o.pc.getSenders&&(t._sender=o.pc.getSenders()[0]),n._isSinkSupported?n._onAddTrack(n,i):n._fallbackOnAddTrack(n,i),n._startPollingVolume()},o},c.prototype._maybeSetIceAggressiveNomination=function(e){return this.options.forceAggressiveIceNomination?s.setIceAggressiveNomination(e):e},c.prototype._setupChannel=function(){var e=this,t=this.version.pc;this.version.pc.onopen=function(){e.status="open",e.onopen()},this.version.pc.onstatechange=function(){e.version.pc&&"stable"===e.version.pc.readyState&&(e.status="open",e.onopen())},this.version.pc.onsignalingstatechange=function(){var n=t.signalingState;e._log.info('signalingState is "'+n+'"'),e.version.pc&&"stable"===e.version.pc.signalingState&&(e.status="open",e.onopen()),e.onsignalingstatechange(t.signalingState)},t.onconnectionstatechange=function(n){var o=t.connectionState;if(!o&&n&&n.target){var i=n.target;o=i.connectionState||i.connectionState_,e._log.info("pc.connectionState not detected. Using target PC. State="+o)}o?e._log.info('pc.connectionState is "'+o+'"'):e._log.warn('onconnectionstatechange detected but state is "'+o+'"'),e.onpcconnectionstatechange(o),e._onMediaConnectionStateChange(o)},t.onicecandidate=function(t){var n=t.candidate;n&&(e._hasIceCandidates=!0,e.onicecandidate(n),e._setupRTCIceTransportListener()),e._log.info("ICE Candidate: "+JSON.stringify(n))},t.onicegatheringstatechange=function(){var n=t.iceGatheringState;"gathering"===n?e._startIceGatheringTimeout():"complete"===n&&(e._stopIceGatheringTimeout(),e._hasIceCandidates||e._onIceGatheringFailure("none"),e._hasIceCandidates&&e._hasIceGatheringFailures&&e._startIceGatheringTimeout()),e._log.info('pc.iceGatheringState is "'+t.iceGatheringState+'"'),e.onicegatheringstatechange(n)},t.oniceconnectionstatechange=function(){e._log.info('pc.iceConnectionState is "'+t.iceConnectionState+'"'),e.oniceconnectionstatechange(t.iceConnectionState),e._onMediaConnectionStateChange(t.iceConnectionState)}},c.prototype._initializeMediaStream=function(e){return"open"!==this.status&&("disconnected"===this.pstream.status?(this.onerror({info:{code:31e3,message:"Cannot establish connection. Client is disconnected",twilioError:new o.SignalingErrors.ConnectionDisconnected}}),this.close(),!1):(this.version=this._setupPeerConnection(e),this._setupChannel(),!0))},c.prototype._removeReconnectionListeners=function(){this.pstream&&(this.pstream.removeListener("answer",this._onAnswerOrRinging),this.pstream.removeListener("hangup",this._onHangup))},c.prototype._setupRTCDtlsTransportListener=function(){var e=this,t=this.getRTCDtlsTransport();if(t&&!t.onstatechange){var n=function(){e._log.info('dtlsTransportState is "'+t.state+'"'),e.ondtlstransportstatechange(t.state)};n(),t.onstatechange=n}},c.prototype._setupRTCIceTransportListener=function(){var e=this,t=this._getRTCIceTransport();t&&!t.onselectedcandidatepairchange&&(t.onselectedcandidatepairchange=function(){return e.onselectedcandidatepairchange(t.getSelectedCandidatePair())})},c.prototype.iceRestart=function(){var e=this;this._log.info("Attempting to restart ICE..."),this._hasIceCandidates=!1,this.version.createOffer(this.options.maxAverageBitrate,this.codecPreferences,{iceRestart:!0}).then((function(){e._removeReconnectionListeners(),e._onAnswerOrRinging=function(t){if(e._removeReconnectionListeners(),t.sdp&&"have-local-offer"===e.version.pc.signalingState){var n=e._maybeSetIceAggressiveNomination(t.sdp);e._answerSdp=n,"closed"!==e.status&&e.version.processAnswer(e.codecPreferences,n,null,(function(t){var n=t&&t.message?t.message:t;e._log.error("Failed to process answer during ICE Restart. Error: "+n)}))}else{var o="Invalid state or param during ICE Restart:hasSdp:"+!!t.sdp+", signalingState:"+e.version.pc.signalingState;e._log.warn(o)}},e._onHangup=function(){e._log.info("Received hangup during ICE Restart"),e._removeReconnectionListeners()},e.pstream.on("answer",e._onAnswerOrRinging),e.pstream.on("hangup",e._onHangup),e.pstream.reinvite(e.version.getSDP(),e.callSid)})).catch((function(t){var n=t&&t.message?t.message:t;e._log.error("Failed to createOffer during ICE Restart. Error: "+n),e.onfailed(n)}))},c.prototype.makeOutgoingCall=function(e,t,n,i,r){var a=this;if(this._initializeMediaStream(i)){var s=this;this.callSid=n,this._onAnswerOrRinging=function(e){if(e.sdp){var t=a._maybeSetIceAggressiveNomination(e.sdp);s._answerSdp=t,"closed"!==s.status&&s.version.processAnswer(a.codecPreferences,t,c,u),s.pstream.removeListener("answer",s._onAnswerOrRinging),s.pstream.removeListener("ringing",s._onAnswerOrRinging)}},this.pstream.on("answer",this._onAnswerOrRinging),this.pstream.on("ringing",this._onAnswerOrRinging),this.version.createOffer(this.options.maxAverageBitrate,this.codecPreferences,{audio:!0},(function(){"closed"!==s.status&&(t?s.pstream.reconnect(s.version.getSDP(),s.callSid,t):s.pstream.invite(s.version.getSDP(),s.callSid,e),s._setupRTCDtlsTransportListener())}),(function(e){var t=e.message||e;s.onerror({info:{code:31e3,message:"Error creating the offer: "+t,twilioError:new o.MediaErrors.ClientLocalDescFailed}})}))}function c(){s.options&&s._setEncodingParameters(s.options.dscp),r(s.version.pc)}function u(e){var t=e.message||e;s.onerror({info:{code:31e3,message:"Error processing answer: "+t,twilioError:new o.MediaErrors.ClientRemoteDescFailed}})}},c.prototype.answerIncomingCall=function(e,t,n,i){if(this._initializeMediaStream(n)){t=this._maybeSetIceAggressiveNomination(t),this._answerSdp=t.replace(/^a=setup:actpass$/gm,"a=setup:passive"),this.callSid=e;var r=this;this.version.processSDP(this.options.maxAverageBitrate,this.codecPreferences,t,{audio:!0},(function(){"closed"!==r.status&&(r.pstream.answer(r.version.getSDP(),e),r.options&&r._setEncodingParameters(r.options.dscp),i(r.version.pc),r._setupRTCDtlsTransportListener())}),(function(e){var t=e.message||e;r.onerror({info:{code:31e3,message:"Error creating the answer: "+t,twilioError:new o.MediaErrors.ClientRemoteDescFailed}})}))}},c.prototype.close=function(){this.version&&this.version.pc&&("closed"!==this.version.pc.signalingState&&this.version.pc.close(),this.version.pc=null),this.stream&&(this.mute(!1),this._stopStream()),this.stream=null,this._removeReconnectionListeners(),this._stopIceGatheringTimeout(),Promise.all(this._removeAudioOutputs()).catch((function(){})),this._mediaStreamSource&&this._mediaStreamSource.disconnect(),this._inputAnalyser&&this._inputAnalyser.disconnect(),this._outputAnalyser&&this._outputAnalyser.disconnect(),this._inputAnalyser2&&this._inputAnalyser2.disconnect(),this._outputAnalyser2&&this._outputAnalyser2.disconnect(),this.status="closed",this.onclose()},c.prototype.reject=function(e){this.callSid=e},c.prototype.ignore=function(e){this.callSid=e},c.prototype.mute=function(e){(this.isMuted=e,this.stream)&&(this._sender&&this._sender.track?this._sender.track.enabled=!e:("function"===typeof this.stream.getAudioTracks?this.stream.getAudioTracks():this.stream.audioTracks).forEach((function(t){t.enabled=!e})))},c.prototype.getOrCreateDTMFSender=function(){if(this._dtmfSender||this._dtmfSenderUnsupported)return this._dtmfSender||null;var e=this,t=this.version.pc;if(!t)return this._log.warn("No RTCPeerConnection available to call createDTMFSender on"),null;if("function"===typeof t.getSenders&&("function"===typeof RTCDTMFSender||"function"===typeof RTCDtmfSender)){var n=t.getSenders().find((function(e){return e.dtmf}));if(n)return this._log.info("Using RTCRtpSender#dtmf"),this._dtmfSender=n.dtmf,this._dtmfSender}if("function"===typeof t.createDTMFSender&&"function"===typeof t.getLocalStreams){var o=t.getLocalStreams().map((function(t){var n=e._getAudioTracks(t);return n&&n[0]}))[0];return o?(this._log.info("Creating RTCDTMFSender"),this._dtmfSender=t.createDTMFSender(o),this._dtmfSender):(this._log.warn("No local audio MediaStreamTrack available on the RTCPeerConnection to pass to createDTMFSender"),null)}return this._log.info("RTCPeerConnection does not support RTCDTMFSender"),this._dtmfSenderUnsupported=!0,null},c.prototype.getRTCDtlsTransport=function(){var e=this.version&&this.version.pc&&"function"===typeof this.version.pc.getSenders&&this.version.pc.getSenders()[0];return e&&e.transport||null},c.prototype._canStopMediaStreamTrack=function(){return"function"===typeof MediaStreamTrack.prototype.stop},c.prototype._getAudioTracks=function(e){return"function"===typeof e.getAudioTracks?e.getAudioTracks():e.audioTracks},c.prototype._getRTCIceTransport=function(){var e=this.getRTCDtlsTransport();return e&&e.iceTransport||null},c.protocol=a.default.test()?new a.default:null,c.enabled=a.default.test(),t.default=c},89261:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(24468),i=n(62997),r=n(40230),a=n(32134);function s(e){"undefined"!==typeof window?e&&e.RTCPeerConnection?this.RTCPeerConnection=e.RTCPeerConnection:i.isLegacyEdge()?this.RTCPeerConnection=new a("undefined"!==typeof window?window:n.g):"function"===typeof window.RTCPeerConnection?this.RTCPeerConnection=window.RTCPeerConnection:"function"===typeof window.webkitRTCPeerConnection?this.RTCPeerConnection=webkitRTCPeerConnection:"function"===typeof window.mozRTCPeerConnection?(this.RTCPeerConnection=mozRTCPeerConnection,window.RTCSessionDescription=mozRTCSessionDescription,window.RTCIceCandidate=mozRTCIceCandidate):this.log.info("No RTCPeerConnection implementation available"):this.log.info("No RTCPeerConnection implementation available. The window object was not found.")}function c(e,t,n,o){return function(){var i=Array.prototype.slice.call(arguments);return new Promise((function(n){var r=e.apply(t,i);if(o){if("object"!==typeof r||"function"!==typeof r.then)throw new Error;n(r)}else n(r)})).catch((function(){return new Promise((function(o,r){e.apply(t,n?[o,r].concat(i):i.concat([o,r]))}))}))}}function u(e,t){return c(e,t,!0,!0)}function d(e,t){return c(e,t,!1,!1)}s.prototype.create=function(e){this.log=new o.default("RTCPC"),this.pc=new this.RTCPeerConnection(e)},s.prototype.createModernConstraints=function(e){if("undefined"===typeof e)return null;var t=Object.assign({},e);return"undefined"===typeof webkitRTCPeerConnection||i.isLegacyEdge()?("undefined"!==typeof e.audio&&(t.offerToReceiveAudio=e.audio),"undefined"!==typeof e.video&&(t.offerToReceiveVideo=e.video)):(t.mandatory={},"undefined"!==typeof e.audio&&(t.mandatory.OfferToReceiveAudio=e.audio),"undefined"!==typeof e.video&&(t.mandatory.OfferToReceiveVideo=e.video)),delete t.audio,delete t.video,t},s.prototype.createOffer=function(e,t,n,o,i){var a=this;return n=this.createModernConstraints(n),u(this.pc.createOffer,this.pc)(n).then((function(n){if(!a.pc)return Promise.resolve();var o=r.setMaxAverageBitrate(n.sdp,e);return d(a.pc.setLocalDescription,a.pc)(new RTCSessionDescription({sdp:r.setCodecPreferences(o,t),type:"offer"}))})).then(o,i)},s.prototype.createAnswer=function(e,t,n,o,i){var a=this;return n=this.createModernConstraints(n),u(this.pc.createAnswer,this.pc)(n).then((function(n){if(!a.pc)return Promise.resolve();var o=r.setMaxAverageBitrate(n.sdp,e);return d(a.pc.setLocalDescription,a.pc)(new RTCSessionDescription({sdp:r.setCodecPreferences(o,t),type:"answer"}))})).then(o,i)},s.prototype.processSDP=function(e,t,n,o,i,a){var s=this;n=r.setCodecPreferences(n,t);var c=new RTCSessionDescription({sdp:n,type:"offer"});return d(this.pc.setRemoteDescription,this.pc)(c).then((function(){s.createAnswer(e,t,o,i,a)}))},s.prototype.getSDP=function(){return this.pc.localDescription.sdp},s.prototype.processAnswer=function(e,t,n,o){return this.pc?(t=r.setCodecPreferences(t,e),d(this.pc.setRemoteDescription,this.pc)(new RTCSessionDescription({sdp:t,type:"answer"})).then(n,o)):Promise.resolve()},s.test=function(){if("object"===typeof navigator){var e=navigator.mediaDevices&&navigator.mediaDevices.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.getUserMedia;if(i.isLegacyEdge(navigator))return!1;if(e&&"function"===typeof window.RTCPeerConnection)return!0;if(e&&"function"===typeof window.webkitRTCPeerConnection)return!0;if(e&&"function"===typeof window.mozRTCPeerConnection){try{if("function"!==typeof(new window.mozRTCPeerConnection).getLocalStreams)return!1}catch(t){return!1}return!0}if("undefined"!==typeof RTCIceGatherer)return!0}return!1},t.default=s},40230:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.setMaxAverageBitrate=t.setIceAggressiveNomination=t.setCodecPreferences=t.getPreferredCodecInfo=void 0;var o=n(62997),i={0:"PCMU",8:"PCMA"};t.getPreferredCodecInfo=function(e){var t=/a=rtpmap:(\d+) (\S+)/m.exec(e)||[null,"",""],n=t[1];return{codecName:t[2],codecParams:(new RegExp("a=fmtp:"+n+" (\\S+)","m").exec(e)||[null,""])[1]}},t.setIceAggressiveNomination=function(e){return o.isChrome(window,window.navigator)?e.split("\n").filter((function(e){return-1===e.indexOf("a=ice-lite")})).join("\n"):e},t.setMaxAverageBitrate=function(e,t){if("number"!==typeof t||t<6e3||t>51e4)return e;var n=/a=rtpmap:(\d+) opus/m.exec(e),o=n&&n.length?n[1]:111,i=new RegExp("a=fmtp:"+o);return e.split("\n").map((function(e){return i.test(e)?e+";maxaveragebitrate="+t:e})).join("\n")},t.setCodecPreferences=function(e,t){var n=function(e,t,n){return e.replace(/\r\n\r\n$/,"\r\n").split("\r\nm=").slice(1).map((function(e){return"m="+e})).filter((function(e){var o=new RegExp("m="+(t||".*"),"gm"),i=new RegExp("a="+(n||".*"),"gm");return o.test(e)&&i.test(e)}))}(e);return[e.split("\r\nm=")[0]].concat(n.map((function(e){if(!/^m=(audio|video)/.test(e))return e;var n=e.match(/^m=(audio|video)/)[1],r=function(e){return Array.from((t=e,function(e){var t=e.split("\r\n")[0],n=t.match(/([0-9]+)/g);return n?n.slice(1).map((function(e){return parseInt(e,10)})):[]}(t).reduce((function(e,n){var o=new RegExp("a=rtpmap:"+n+" ([^/]+)"),r=t.match(o),a=r?r[1].toLowerCase():i[n]?i[n].toLowerCase():"";return e.set(n,a)}),new Map))).reduce((function(e,t){var n=t[0],o=t[1],i=e.get(o)||[];return e.set(o,i.concat(n))}),new Map);var t}(e),a=function(e,t){t=t.map((function(e){return e.toLowerCase()}));var n=o.flatMap(t,(function(t){return e.get(t)||[]})),i=o.difference(Array.from(e.keys()),t),r=o.flatMap(i,(function(t){return e.get(t)}));return n.concat(r)}(r,t),s=function(e,t){var n=t.split("\r\n"),o=n[0],i=n.slice(1);return o=o.replace(/([0-9]+\s?)+$/,e.join(" ")),[o].concat(i).join("\r\n")}(a,e),c=r.get("pcma")||[],u=r.get("pcmu")||[];return("audio"===n?new Set(c.concat(u)):new Set).has(a[0])?s.replace(/\r\nb=(AS|TIAS):([0-9]+)/g,""):s}))).join("\r\n")}},90269:function(e,t,n){var o=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o};Object.defineProperty(t,"__esModule",{value:!0}),t.getRTCIceCandidateStatsReport=t.getRTCStats=void 0;var i=n(18987),r=n(9850),a="PeerConnection is null",s="WebRTC statistics are unsupported";function c(e,t){return"function"===typeof e.get?e.get(t):e.find((function(e){return e.id===t}))}function u(e){if(!e)return Promise.reject(new i.InvalidArgumentError(a));if("function"!==typeof e.getStats)return Promise.reject(new i.NotSupportedError(s));var t;try{t=e.getStats()}catch(n){t=new Promise((function(t){return e.getStats(t)})).then(r.default.fromRTCStatsResponse)}return t}function d(){}function l(e){var t,n=null,o=new d;Array.from(e.values()).forEach((function(i){if(!i.isRemote){var r=i.type.replace("-","");if(t=t||i.timestamp,i.remoteId){var a=c(e,i.remoteId);a&&a.roundTripTime&&(o.rtt=1e3*a.roundTripTime)}switch(r){case"inboundrtp":o.timestamp=o.timestamp||i.timestamp,o.jitter=1e3*i.jitter,o.packetsLost=i.packetsLost,o.packetsReceived=i.packetsReceived,o.bytesReceived=i.bytesReceived;break;case"outboundrtp":if(o.timestamp=i.timestamp,o.packetsSent=i.packetsSent,o.bytesSent=i.bytesSent,i.codecId){var s=c(e,i.codecId);o.codecName=s?s.mimeType&&s.mimeType.match(/(.*\/)?(.*)/)[2]:i.codecId}break;case"transport":n=i.id}}})),o.timestamp||(o.timestamp=t);var i=c(e,n);if(!i)return o;var r=c(e,i.selectedCandidatePairId);if(!r)return o;var a=c(e,r.localCandidateId),s=c(e,r.remoteCandidateId);return o.rtt||(o.rtt=r&&1e3*r.currentRoundTripTime),Object.assign(o,{localAddress:a&&(a.address||a.ip),remoteAddress:s&&(s.address||s.ip)}),o}t.getRTCStats=function(e,t){return t=Object.assign({createRTCSample:l},t),u(e).then(t.createRTCSample)},t.getRTCIceCandidateStatsReport=function(e){return u(e).then((function(e){var t,n=Array.from(e.values()).reduce((function(e,t){switch(["candidatePairs","localCandidates","remoteCandidates"].forEach((function(t){e[t]||(e[t]=[])})),t.type){case"candidate-pair":e.candidatePairs.push(t);break;case"local-candidate":e.localCandidates.push(t);break;case"remote-candidate":e.remoteCandidates.push(t);break;case"transport":t.selectedCandidatePairId&&(e.transport=t)}return e}),{}),i=n.candidatePairs,r=n.localCandidates,a=n.remoteCandidates,s=n.transport,c=i.find((function(e){return e.selected||s&&e.id===s.selectedCandidatePairId}));return c&&(t={localCandidate:r.find((function(e){return e.id===c.localCandidateId})),remoteCandidate:a.find((function(e){return e.id===c.remoteCandidateId}))}),{iceCandidateStats:o(r,a),selectedIceCandidatePairStats:t}}))}},46624:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){Object.defineProperties(this,{deviceId:{get:function(){return e.deviceId}},groupId:{get:function(){return e.groupId}},kind:{get:function(){return e.kind}},label:{get:function(){return e.label}}})};t.default=n},32769:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(68884),i=n(745),r=n(18987);function a(e,t,n){if(!(this instanceof a))return new a(e,t,n);if(!e||!t)throw new r.InvalidArgumentError("name and url are required arguments");(n=Object.assign({AudioFactory:"undefined"!==typeof Audio?Audio:null,maxDuration:0,shouldLoop:!1},n)).AudioPlayer=n.audioContext?i.default.bind(i.default,n.audioContext):n.AudioFactory,Object.defineProperties(this,{_Audio:{value:n.AudioPlayer},_activeEls:{value:new Map},_isSinkSupported:{value:null!==n.AudioFactory&&"function"===typeof n.AudioFactory.prototype.setSinkId},_maxDuration:{value:n.maxDuration},_maxDurationTimeout:{value:null,writable:!0},_operations:{value:new o.AsyncQueue},_playPromise:{value:null,writable:!0},_shouldLoop:{value:n.shouldLoop},_sinkIds:{value:["default"]},isPlaying:{enumerable:!0,get:function(){return!!this._playPromise}},name:{enumerable:!0,value:e},url:{enumerable:!0,value:t}}),this._Audio&&this._play(!0,!1)}function s(e){e&&(e.pause(),e.src="",e.srcObject=null,e.load())}a.prototype._playAudioElement=function(e,t,n){var o=this,i=this._activeEls.get(e);if(!i)throw new r.InvalidArgumentError('sinkId: "'+e+"\" doesn't have an audio element");return i.muted=!!t,i.loop=!!n,i.play().then((function(){return i})).catch((function(t){throw s(i),o._activeEls.delete(e),t}))},a.prototype._play=function(e,t){this.isPlaying&&this._stop(),this._maxDuration>0&&(this._maxDurationTimeout=setTimeout(this._stop.bind(this),this._maxDuration)),t="boolean"===typeof t?t:this._shouldLoop;var n=this;return this._playPromise=Promise.all(this._sinkIds.map((function(o){if(!n._Audio)return Promise.resolve();var i=n._activeEls.get(o);return i?n._playAudioElement(o,e,t):("function"===typeof(i=new n._Audio(n.url)).setAttribute&&i.setAttribute("crossorigin","anonymous"),new Promise((function(e){i.addEventListener("canplaythrough",e)})).then((function(){return(n._isSinkSupported?i.setSinkId(o):Promise.resolve()).then((function(){return n._activeEls.set(o,i),n._playPromise?n._playAudioElement(o,e,t):Promise.resolve()}))})))})))},a.prototype._stop=function(){var e=this;this._activeEls.forEach((function(t,n){e._sinkIds.includes(n)?(t.pause(),t.currentTime=0):(s(t),e._activeEls.delete(n))})),clearTimeout(this._maxDurationTimeout),this._playPromise=null,this._maxDurationTimeout=null},a.prototype.setSinkIds=function(e){this._isSinkSupported&&(e=e.forEach?e:[e],[].splice.apply(this._sinkIds,[0,this._sinkIds.length].concat(e)))},a.prototype.stop=function(){var e=this;this._operations.enqueue((function(){return e._stop(),Promise.resolve()}))},a.prototype.play=function(){var e=this;return this._operations.enqueue((function(){return e._play()}))},t.default=a},49017:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)},r=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o};Object.defineProperty(t,"__esModule",{value:!0});var a=n(5939),s=n(18987),c=n(90497),u=n(90269),d=n(62997),l={audioInputLevel:{minStandardDeviation:327.67,sampleCount:10},audioOutputLevel:{minStandardDeviation:327.67,sampleCount:10},bytesReceived:{clearCount:2,min:1,raiseCount:3,sampleCount:3},bytesSent:{clearCount:2,min:1,raiseCount:3,sampleCount:3},jitter:{max:30},mos:{min:3},packetsLostFraction:[{max:1},{clearValue:1,maxAverage:3,sampleCount:7}],rtt:{max:400}};var p=function(e){function t(t){var n=e.call(this)||this;n._activeWarnings=new Map,n._currentStreaks=new Map,n._inputVolumes=[],n._outputVolumes=[],n._sampleBuffer=[],n._supplementalSampleBuffers={audioInputLevel:[],audioOutputLevel:[]},n._warningsEnabled=!0,t=t||{},n._getRTCStats=t.getRTCStats||u.getRTCStats,n._mos=t.Mos||c.default,n._peerConnection=t.peerConnection,n._thresholds=i(i({},l),t.thresholds);var o=Object.values(n._thresholds).map((function(e){return e.sampleCount})).filter((function(e){return!!e}));return n._maxSampleCount=Math.max.apply(Math,r([5],o)),n._peerConnection&&n.enable(n._peerConnection),n}return o(t,e),t.prototype.addVolumes=function(e,t){this._inputVolumes.push(e),this._outputVolumes.push(t)},t.prototype.disable=function(){return this._sampleInterval&&(clearInterval(this._sampleInterval),delete this._sampleInterval),this},t.prototype.disableWarnings=function(){return this._warningsEnabled&&this._activeWarnings.clear(),this._warningsEnabled=!1,this},t.prototype.enable=function(e){if(e){if(this._peerConnection&&e!==this._peerConnection)throw new s.InvalidArgumentError("Attempted to replace an existing PeerConnection in StatsMonitor.enable");this._peerConnection=e}if(!this._peerConnection)throw new s.InvalidArgumentError("Can not enable StatsMonitor without a PeerConnection");return this._sampleInterval=this._sampleInterval||setInterval(this._fetchSample.bind(this),1e3),this},t.prototype.enableWarnings=function(){return this._warningsEnabled=!0,this},t.prototype.hasActiveWarning=function(e,t){var n=e+":"+t;return!!this._activeWarnings.get(n)},t.prototype._addSample=function(e){var t=this._sampleBuffer;t.push(e),t.length>this._maxSampleCount&&t.splice(0,t.length-this._maxSampleCount)},t.prototype._clearWarning=function(e,t,n){var o=e+":"+t,r=this._activeWarnings.get(o);!r||Date.now()-r.timeRaised<5e3||(this._activeWarnings.delete(o),this.emit("warning-cleared",i(i({},n),{name:e,threshold:{name:t,value:this._thresholds[e][t]}})))},t.prototype._createSample=function(e,t){var n=t&&t.totals.bytesSent||0,o=t&&t.totals.bytesReceived||0,i=t&&t.totals.packetsSent||0,r=t&&t.totals.packetsReceived||0,a=t&&t.totals.packetsLost||0,s=e.bytesSent-n,c=e.bytesReceived-o,u=e.packetsSent-i,l=e.packetsReceived-r,p=e.packetsLost-a,f=l+p,h=f>0?p/f*100:0,m=e.packetsReceived+e.packetsLost,_=m>0?e.packetsLost/m*100:100,g="number"!==typeof e.rtt&&t?t.rtt:e.rtt,v=this._inputVolumes.splice(0);this._supplementalSampleBuffers.audioInputLevel.push(v);var y=this._outputVolumes.splice(0);return this._supplementalSampleBuffers.audioOutputLevel.push(y),{audioInputLevel:Math.round(d.average(v)),audioOutputLevel:Math.round(d.average(y)),bytesReceived:c,bytesSent:s,codecName:e.codecName,jitter:e.jitter,mos:this._mos.calculate(g,e.jitter,t&&h),packetsLost:p,packetsLostFraction:h,packetsReceived:l,packetsSent:u,rtt:g,timestamp:e.timestamp,totals:{bytesReceived:e.bytesReceived,bytesSent:e.bytesSent,packetsLost:e.packetsLost,packetsLostFraction:_,packetsReceived:e.packetsReceived,packetsSent:e.packetsSent}}},t.prototype._fetchSample=function(){var e=this;this._getSample().then((function(t){e._addSample(t),e._raiseWarnings(),e.emit("sample",t)})).catch((function(t){e.disable(),e.emit("error",t)}))},t.prototype._getSample=function(){var e=this;return this._getRTCStats(this._peerConnection).then((function(t){var n=null;return e._sampleBuffer.length&&(n=e._sampleBuffer[e._sampleBuffer.length-1]),e._createSample(t,n)}))},t.prototype._raiseWarning=function(e,t,n){var o=e+":"+t;if(!this._activeWarnings.has(o)){this._activeWarnings.set(o,{timeRaised:Date.now()});var r,a=this._thresholds[e];if(Array.isArray(a)){var s=a.find((function(e){return t in e}));s&&(r=s[t])}else r=this._thresholds[e][t];this.emit("warning",i(i({},n),{name:e,threshold:{name:t,value:r}}))}},t.prototype._raiseWarnings=function(){var e=this;this._warningsEnabled&&Object.keys(this._thresholds).forEach((function(t){return e._raiseWarningsForStat(t)}))},t.prototype._raiseWarningsForStat=function(e){var t=this;(Array.isArray(this._thresholds[e])?this._thresholds[e]:[this._thresholds[e]]).forEach((function(n){var o=t._sampleBuffer,i=n.clearCount||0,a=n.raiseCount||3,s=n.sampleCount||t._maxSampleCount,c=o.slice(-s),u=c.map((function(t){return t[e]}));if(!u.some((function(e){return"undefined"===typeof e||null===e}))){var l;if("number"===typeof n.max&&(l=function(e,t){return t.reduce((function(t,n){return t+(n>e?1:0)}),0)}(n.max,u),l>=a?t._raiseWarning(e,"max",{values:u,samples:c}):l<=i&&t._clearWarning(e,"max",{values:u,samples:c})),"number"===typeof n.min&&(l=function(e,t){return t.reduce((function(t,n){return t+(n<e?1:0)}),0)}(n.min,u),l>=a?t._raiseWarning(e,"min",{values:u,samples:c}):l<=i&&t._clearWarning(e,"min",{values:u,samples:c})),"number"===typeof n.maxDuration&&o.length>1){var p=(c=o.slice(-2))[0][e],f=c[1][e],h=t._currentStreaks.get(e)||0,m=p===f?h+1:0;t._currentStreaks.set(e,m),m>=n.maxDuration?t._raiseWarning(e,"maxDuration",{value:m}):0===m&&t._clearWarning(e,"maxDuration",{value:h})}if("number"===typeof n.minStandardDeviation){var _=t._supplementalSampleBuffers[e];if(!_||_.length<n.sampleCount)return;_.length>n.sampleCount&&_.splice(0,_.length-n.sampleCount);var g=function(e){if(e.length<=0)return null;var t=e.reduce((function(e,t){return e+t}),0)/e.length,n=e.map((function(e){return Math.pow(e-t,2)}));return Math.sqrt(n.reduce((function(e,t){return e+t}),0)/n.length)}(function(e){return e.reduce((function(e,t){return r(e,t)}),[])}(_.slice(-s)));if("number"!==typeof g)return;g<n.minStandardDeviation?t._raiseWarning(e,"minStandardDeviation",{value:g}):t._clearWarning(e,"minStandardDeviation",{value:g})}[["maxAverage",function(e,t){return e>t}],["minAverage",function(e,t){return e<t}]].forEach((function(o){var i=o[0],r=o[1];if("number"===typeof n[i]&&u.length>=s){var a=d.average(u);r(a,n[i])?t._raiseWarning(e,i,{values:u,samples:c}):r(a,n.clearValue||n[i])||t._clearWarning(e,i,{values:u,samples:c})}}))}}))},t}(a.EventEmitter);t.default=p},62997:function(e,t,n){function o(e){if(!(this instanceof o))return new o(e);this.message=e}function i(e){return!!e.userAgent.match("Electron")}function r(e,t){var n=!!t.userAgent.match("CriOS"),o=!!t.userAgent.match("HeadlessChrome"),r="undefined"!==typeof e.chrome&&"Google Inc."===t.vendor&&-1===t.userAgent.indexOf("OPR")&&-1===t.userAgent.indexOf("Edge");return n||i(t)||r||o}function a(e){return!!(e=e||("undefined"===typeof window?n.g.navigator:window.navigator))&&"string"===typeof e.userAgent&&/firefox|fxios/i.test(e.userAgent)}function s(e){return!!e.vendor&&-1!==e.vendor.indexOf("Apple")&&e.userAgent&&-1===e.userAgent.indexOf("CriOS")&&-1===e.userAgent.indexOf("FxiOS")}Object.defineProperty(t,"__esModule",{value:!0}),t.promisifyEvents=t.flatMap=t.queryToJson=t.isUnifiedPlanDefault=t.isSafari=t.isLegacyEdge=t.isFirefox=t.isChrome=t.isElectron=t.difference=t.average=t.Exception=void 0,o.prototype.toString=function(){return"Twilio.Exception: "+this.message},t.average=function(e){return e&&e.length?e.reduce((function(e,t){return e+t}))/e.length:0},t.difference=function(e,t,n){n=n||function(e){return e};var o=new Set(t.map(n));return e.filter((function(e){return!o.has(n(e))}))},t.isElectron=i,t.isChrome=r,t.isFirefox=a,t.isLegacyEdge=function(e){return!!(e=e||("undefined"===typeof window?n.g.navigator:window.navigator))&&"string"===typeof e.userAgent&&/edge\/\d+/i.test(e.userAgent)},t.isSafari=s,t.isUnifiedPlanDefault=function(e,t,n,o){if("undefined"===typeof e||"undefined"===typeof t||"undefined"===typeof n||"undefined"===typeof o||"undefined"===typeof n.prototype||"undefined"===typeof o.prototype)return!1;if(r(e,t)&&n.prototype.addTransceiver){var i=new n,c=!0;try{i.addTransceiver("audio")}catch(u){c=!1}return i.close(),c}return!!a(t)||!!s(t)&&"currentDirection"in o.prototype},t.queryToJson=function(e){return e?e.split("&").reduce((function(e,t){var n=t.split("="),o=n[0],i=decodeURIComponent((n[1]||"").replace(/\+/g,"%20"));return o&&(e[o]=i),e}),{}):""},t.flatMap=function(e,t){var n=e instanceof Map||e instanceof Set?Array.from(e.values()):e;return t=t||function(e){return e},n.reduce((function(e,n){var o=t(n);return e.concat(o)}),[])},t.promisifyEvents=function(e,t,n){return new Promise((function(o,i){function r(){e.removeListener(n,a),o()}function a(){e.removeListener(t,r),i()}e.once(t,r),e.once(n,a)}))};var c=o;t.Exception=c},88342:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.generateVoiceEventSid=void 0;var o=n(59614),i=n(18987),r="function"===typeof o?o:o.default;t.generateVoiceEventSid=function(){return"KX"+function(){if("object"!==typeof window)throw new i.NotSupportedError("This platform is not supported.");var e=window.crypto;if("object"!==typeof e)throw new i.NotSupportedError("The `crypto` module is not available on this platform.");if("undefined"===typeof(e.randomUUID||e.getRandomValues))throw new i.NotSupportedError("Neither `crypto.randomUUID` or `crypto.getRandomValues` are available on this platform.");if("undefined"===typeof window.Uint32Array)throw new i.NotSupportedError("The `Uint32Array` module is not available on this platform.");var t="function"===typeof e.randomUUID?function(){return e.randomUUID()}:function(){return e.getRandomValues(new Uint32Array(32)).toString()};return r(t())}()}},82195:function(e,t,n){var o=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.WSTransportState=void 0;var r,a=n(5939),s=n(77343),c=n(18987),u=n(24468),d=globalThis.WebSocket,l=1/0;!function(e){e.Connecting="connecting",e.Closed="closed",e.Open="open"}(r=t.WSTransportState||(t.WSTransportState={}));var p=function(e){function t(n,o){void 0===o&&(o={});var a=e.call(this)||this;return a.state=r.Closed,a._backoffStartTime={preferred:null,primary:null},a._connectedUri=null,a._log=new u.default("WSTransport"),a._shouldFallback=!1,a._uriIndex=0,a._moveUriIndex=function(){a._uriIndex++,a._uriIndex>=a._uris.length&&(a._uriIndex=0)},a._onSocketClose=function(e){if(a._log.error("Received websocket close event code: "+e.code+". Reason: "+e.reason),1006===e.code||1015===e.code){a.emit("error",{code:31005,message:e.reason||"Websocket connection to Twilio's signaling servers were unexpectedly ended. If this is happening consistently, there may be an issue resolving the hostname provided. If a region or an edge is being specified in Device setup, ensure it is valid.",twilioError:new c.SignalingErrors.ConnectionError});var t=a.state===r.Open||a._previousState===r.Open;!a._shouldFallback&&t||a._moveUriIndex(),a._shouldFallback=!0}a._closeSocket()},a._onSocketError=function(e){a._log.error("WebSocket received error: "+e.message),a.emit("error",{code:31e3,message:e.message||"WSTransport socket error",twilioError:new c.SignalingErrors.ConnectionDisconnected})},a._onSocketMessage=function(e){if(a._setHeartbeatTimeout(),a._socket&&"\n"===e.data)return a._socket.send("\n"),void a._log.debug("heartbeat");e&&"string"===typeof e.data&&a._log.debug("Received: "+e.data),a.emit("message",e)},a._onSocketOpen=function(){a._log.info("WebSocket opened successfully."),a._timeOpened=Date.now(),a._shouldFallback=!1,a._setState(r.Open),clearTimeout(a._connectTimeout),a._resetBackoffs(),a._setHeartbeatTimeout(),a.emit("open")},a._options=i(i({},t.defaultConstructorOptions),o),a._uris=n,a._backoff=a._setupBackoffs(),a}return o(t,e),t.prototype.close=function(){this._log.info("WSTransport.close() called..."),this._close()},t.prototype.open=function(){this._log.info("WSTransport.open() called..."),!this._socket||this._socket.readyState!==d.CONNECTING&&this._socket.readyState!==d.OPEN?this._preferredUri?this._connect(this._preferredUri):this._connect(this._uris[this._uriIndex]):this._log.info("WebSocket already open.")},t.prototype.send=function(e){if(this._log.debug("Sending: "+e),!this._socket||this._socket.readyState!==d.OPEN)return this._log.debug("Cannot send message. WebSocket is not open."),!1;try{this._socket.send(e)}catch(t){return this._log.error("Error while sending message:",t.message),this._closeSocket(),!1}return!0},t.prototype.updatePreferredURI=function(e){this._preferredUri=e},t.prototype.updateURIs=function(e){"string"===typeof e&&(e=[e]),this._uris=e,this._uriIndex=0},t.prototype._close=function(){this._setState(r.Closed),this._closeSocket()},t.prototype._closeSocket=function(){clearTimeout(this._connectTimeout),clearTimeout(this._heartbeatTimeout),this._log.info("Closing and cleaning up WebSocket..."),this._socket?(this._socket.removeEventListener("close",this._onSocketClose),this._socket.removeEventListener("error",this._onSocketError),this._socket.removeEventListener("message",this._onSocketMessage),this._socket.removeEventListener("open",this._onSocketOpen),this._socket.readyState!==d.CONNECTING&&this._socket.readyState!==d.OPEN||this._socket.close(),this._timeOpened&&Date.now()-this._timeOpened>1e4&&this._resetBackoffs(),this.state!==r.Closed&&this._performBackoff(),delete this._socket,this.emit("close")):this._log.info("No WebSocket to clean up.")},t.prototype._connect=function(e,t){var n=this;this._log.info("number"===typeof t?"Attempting to reconnect (retry #"+t+")...":"Attempting to connect..."),this._closeSocket(),this._setState(r.Connecting),this._connectedUri=e;try{this._socket=new this._options.WebSocket(this._connectedUri)}catch(o){return this._log.error("Could not connect to endpoint:",o.message),this._close(),void this.emit("error",{code:31e3,message:o.message||"Could not connect to "+this._connectedUri,twilioError:new c.SignalingErrors.ConnectionDisconnected})}this._socket.addEventListener("close",this._onSocketClose),this._socket.addEventListener("error",this._onSocketError),this._socket.addEventListener("message",this._onSocketMessage),this._socket.addEventListener("open",this._onSocketOpen),delete this._timeOpened,this._connectTimeout=setTimeout((function(){n._log.info("WebSocket connection attempt timed out."),n._moveUriIndex(),n._closeSocket()}),this._options.connectTimeoutMs)},t.prototype._performBackoff=function(){this._preferredUri?(this._log.info("Preferred URI set; backing off."),this._backoff.preferred.backoff()):(this._log.info("Preferred URI not set; backing off."),this._backoff.primary.backoff())},t.prototype._resetBackoffs=function(){this._backoff.preferred.reset(),this._backoff.primary.reset(),this._backoffStartTime.preferred=null,this._backoffStartTime.primary=null},t.prototype._setHeartbeatTimeout=function(){var e=this;clearTimeout(this._heartbeatTimeout),this._heartbeatTimeout=setTimeout((function(){e._log.info("No messages received in 15 seconds. Reconnecting..."),e._shouldFallback=!0,e._closeSocket()}),15e3)},t.prototype._setState=function(e){this._previousState=this.state,this.state=e},t.prototype._setupBackoffs=function(){var e=this,t={factor:2,jitter:.4,max:this._options.maxPreferredDelayMs,min:100};this._log.info("Initializing preferred transport backoff using config: ",t);var n=new s.default(t);n.on("backoff",(function(t,n){e.state!==r.Closed?(e._log.info("Will attempt to reconnect Websocket to preferred URI in "+n+"ms"),0===t&&(e._backoffStartTime.preferred=Date.now(),e._log.info("Preferred backoff start; "+e._backoffStartTime.preferred))):e._log.info("Preferred backoff initiated but transport state is closed; not attempting a connection.")})),n.on("ready",(function(t,n){if(e.state!==r.Closed){if(null!==e._backoffStartTime.preferred)return Date.now()-e._backoffStartTime.preferred>e._options.maxPreferredDurationMs?(e._log.info("Max preferred backoff attempt time exceeded; falling back to primary backoff."),e._preferredUri=null,void e._backoff.primary.backoff()):"string"!==typeof e._preferredUri?(e._log.info("Preferred URI cleared; falling back to primary backoff."),e._preferredUri=null,void e._backoff.primary.backoff()):void e._connect(e._preferredUri,t+1);e._log.info("Preferred backoff start time invalid; not attempting a connection.")}else e._log.info("Preferred backoff ready but transport state is closed; not attempting a connection.")}));var o={factor:2,jitter:.4,max:this._options.maxPrimaryDelayMs,min:this._uris&&this._uris.length>1?Math.floor(4001*Math.random())+1e3:100};this._log.info("Initializing primary transport backoff using config: ",o);var i=new s.default(o);return i.on("backoff",(function(t,n){e.state!==r.Closed?(e._log.info("Will attempt to reconnect WebSocket in "+n+"ms"),0===t&&(e._backoffStartTime.primary=Date.now(),e._log.info("Primary backoff start; "+e._backoffStartTime.primary))):e._log.info("Primary backoff initiated but transport state is closed; not attempting a connection.")})),i.on("ready",(function(t,n){e.state!==r.Closed?null!==e._backoffStartTime.primary?Date.now()-e._backoffStartTime.primary>e._options.maxPrimaryDurationMs?e._log.info("Max primary backoff attempt time exceeded; not attempting a connection."):e._connect(e._uris[e._uriIndex],t+1):e._log.info("Primary backoff start time invalid; not attempting a connection."):e._log.info("Primary backoff ready but transport state is closed; not attempting a connection.")})),{preferred:n,primary:i}},Object.defineProperty(t.prototype,"uri",{get:function(){return this._connectedUri},enumerable:!1,configurable:!0}),t.defaultConstructorOptions={WebSocket:d,connectTimeoutMs:5e3,maxPreferredDelayMs:1e3,maxPreferredDurationMs:15e3,maxPrimaryDelayMs:2e4,maxPrimaryDurationMs:l},t}(a.EventEmitter);t.default=p}}]);
//# sourceMappingURL=@twilio.7971cbde6c5e0b383e50b16d3f8298ad.js.map