import { ISRDropdownOption } from "@sr/design-component";
import { map as lo_map, forEach as lo_forEach } from "lodash"
import { Prospects, LogIn } from "@sr/shared-product-components";

export function getAllMembersOptions(allMembers: LogIn.ITeamMember[]) {
  let options: ISRDropdownOption[] = [];
  lo_map(allMembers, (member) => {
    let option: any = {};
    (option.displayText = member.first_name + ' ' + member.last_name),
      (option.value = member.user_id),
      options.push(option);
  });
  return options;
}

export function getTimeZonesOptions(
  timeZones: { name: string; value: string }[]
): ISRDropdownOption[] {
  let updatedOptions = lo_map(timeZones, (timeZone) => {
    return {
      value: timeZone.value,
      displayText: timeZone.name,
    };
  });
  updatedOptions.unshift({ value: '', displayText: '-- Select Timezone --' });
  return updatedOptions;
}

export function getListOptions(
  options: { id: React.ReactText; name: string }[]
) {
  let updatedOptions = lo_map(options, (obj) => {
    return { displayText: obj.name, value: obj.name };
  });
  updatedOptions.unshift({ value: '', displayText: '-- None --' });
  return updatedOptions;
}

export function getCountriesOptions(countries: string[]): ISRDropdownOption[] {
  let updatedOptions = lo_map(countries, (country) => {
    return { value: country, displayText: country };
  });
  updatedOptions.unshift({ value: '', displayText: '-- None --' });
  return updatedOptions;
}

export function getValue(colName: string, prospectInfo: Prospects.IProspect) {
  let intialValue = '';
  lo_forEach(prospectInfo, (value, prospectKey: any) => {
    if (prospectKey === 'custom_fields') {
      lo_forEach(value, (val, customkey: any) => {
        if (colName === customkey) {
          intialValue = val;
        }
      });
    } else {
      if (colName === prospectKey) {
        intialValue = value;
      }
    }
  });
  return intialValue;
}