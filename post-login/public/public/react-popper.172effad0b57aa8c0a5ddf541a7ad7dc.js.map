{"version": 3, "file": "react-popper.xxxxxxxxxxxxxxxxxxxx.js", "mappings": "iJAAe,SAASA,IAYtB,OAXAA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAC1D,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAASF,UAAUD,GACvB,IAAK,IAAII,KAAOD,EACVP,OAAOS,UAAUC,eAAeC,KAAKJ,EAAQC,KAC/CL,EAAOK,GAAOD,EAAOC,GAG3B,CACA,OAAOL,CACT,EACOJ,EAASa,MAAMC,KAAMR,UAC9B,CCbe,SAASS,EAAuBC,GAC7C,QAAa,IAATA,EACF,MAAM,IAAIC,eAAe,6DAE3B,OAAOD,CACT,CCLe,SAASE,EAAgBC,EAAGC,GAKzC,OAJAF,EAAkBjB,OAAOoB,eAAiBpB,OAAOoB,eAAelB,OAAS,SAAyBgB,EAAGC,GAEnG,OADAD,EAAEG,UAAYF,EACPD,CACT,EACOD,EAAgBC,EAAGC,EAC5B,CCLe,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYT,OAAOyB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjC,EAAeA,EAAUC,EAC3B,CCLe,SAASG,EAAQT,GAG9B,OAAOS,EAAU,mBAAqBC,QAAU,iBAAmBA,OAAOC,SAAW,SAAUX,GAC7F,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAK,mBAAqBU,QAAUV,EAAEQ,cAAgBE,QAAUV,IAAMU,OAAOnB,UAAY,gBAAkBS,CACpH,EAAGS,EAAQT,EACb,CCNe,SAASY,EAAcC,GACpC,IAAI3B,ECFS,SAAqB2B,EAAGC,GACrC,GAAI,UAAYL,EAAQI,KAAOA,EAAG,OAAOA,EACzC,IAAIE,EAAIF,EAAEH,OAAOM,aACjB,QAAI,IAAWD,EAAG,CAChB,IAAI7B,EAAI6B,EAAEtB,KAAKoB,EAAGC,GAAK,WACvB,GAAI,UAAYL,EAAQvB,GAAI,OAAOA,EACnC,MAAM,IAAI+B,UAAU,+CACtB,CACA,OAAQ,WAAaH,EAAII,OAASC,QAAQN,EAC5C,CDPUG,CAAYH,EAAG,UACvB,MAAO,UAAYJ,EAAQvB,GAAKA,EAAIA,EAAI,EAC1C,CEJe,SAASkC,EAAgBC,EAAK/B,EAAKgC,GAYhD,OAXAhC,EAAMsB,EAActB,MACT+B,EACTvC,OAAOyC,eAAeF,EAAK/B,EAAK,CAC9BgC,MAAOA,EACPE,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZL,EAAI/B,GAAOgC,EAEND,CACT,C,0MCTWM,EAA8B,MAC9BC,EAAoC,MAE3CC,EAEJ,SAAUC,GAGR,SAASD,IAGP,IAFA,IAAIE,EAEKC,EAAO7C,UAAUC,OAAQ6C,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQhD,UAAUgD,GAezB,OAVAf,EAAgBxB,EAFhBmC,EAAQD,EAAiBrC,KAAKC,MAAMoC,EAAkB,CAACnC,MAAMyC,OAAOH,KAAUtC,MAE/B,qBAAiB,GAEhEyB,EAAgBxB,EAAuBmC,GAAQ,oBAAoB,SAAUM,GACvEA,GAAoBN,EAAMO,gBAAkBD,IAC9CN,EAAMO,cAAgBD,EAEtBN,EAAMQ,cAEV,IAEOR,CACT,CAtBA3B,EAAeyB,EAASC,GAwBxB,IAAIU,EAASX,EAAQtC,UAcrB,OAZAiD,EAAOC,qBAAuB,WAC5B9C,KAAK2C,cAAgB,IACvB,EAEAE,EAAOE,OAAS,WACd,OAAO,gBAAoBf,EAA4BgB,SAAU,CAC/DrB,MAAO3B,KAAK2C,eACX,gBAAoBV,EAAkCe,SAAU,CACjErB,MAAO3B,KAAKiD,kBACXjD,KAAKkD,MAAMC,UAChB,EAEOjB,CACT,CAxCA,CAwCE,aC9CSkB,EAAc,SAAqBC,GAC5C,OAAOd,MAAMe,QAAQD,GAAOA,EAAI,GAAKA,CACvC,EAMWE,EAAa,SAAoBC,GAC1C,GAAkB,oBAAPA,EAAmB,CAC5B,IAAK,IAAInB,EAAO7C,UAAUC,OAAQ6C,EAAO,IAAIC,MAAMF,EAAO,EAAIA,EAAO,EAAI,GAAIG,EAAO,EAAGA,EAAOH,EAAMG,IAClGF,EAAKE,EAAO,GAAKhD,UAAUgD,GAG7B,OAAOgB,EAAGzD,WAAM,EAAQuC,EAC1B,CACF,EA4BWmB,EAAS,SAAgBC,EAAKC,GAEvC,GAAmB,oBAARD,EACT,OAAOH,EAAWG,EAAKC,GAET,MAAPD,IACLA,EAAIE,QAAUD,EAEpB,EC9CIE,EAAe,CACjBC,SAAU,WACVC,IAAK,EACLC,KAAM,EACNC,QAAS,EACTC,cAAe,QAEbC,EAAoB,CAAC,EACdC,EAEX,SAAUjC,GAGR,SAASiC,IAGP,IAFA,IAAIhC,EAEKC,EAAO7C,UAAUC,OAAQ6C,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQhD,UAAUgD,GAwGzB,OAnGAf,EAAgBxB,EAFhBmC,EAAQD,EAAiBrC,KAAKC,MAAMoC,EAAkB,CAACnC,MAAMyC,OAAOH,KAAUtC,MAE/B,QAAS,CACtDqE,UAAMC,EACNC,eAAWD,IAGb7C,EAAgBxB,EAAuBmC,GAAQ,sBAAkB,GAEjEX,EAAgBxB,EAAuBmC,GAAQ,aAAc,MAE7DX,EAAgBxB,EAAuBmC,GAAQ,YAAa,MAE5DX,EAAgBxB,EAAuBmC,GAAQ,iBAAiB,SAAUoC,GACnEA,GAAcpC,EAAMoC,aAAeA,IACxCf,EAAOrB,EAAMc,MAAMuB,SAAUD,GAC7BpC,EAAMoC,WAAaA,EAEnBpC,EAAMsC,uBACR,IAEAjD,EAAgBxB,EAAuBmC,GAAQ,gBAAgB,SAAUuC,GACvEvC,EAAMuC,UAAYA,CACpB,IAEAlD,EAAgBxB,EAAuBmC,GAAQ,sBAAuB,CACpEwC,SAAS,EACTC,MAAO,IACPrB,GAAI,SAAYa,GACd,IAAIE,EAAYF,EAAKE,UAOrB,OALAnC,EAAM0C,SAAS,CACbT,KAAMA,EACNE,UAAWA,IAGNF,CACT,IAGF5C,EAAgBxB,EAAuBmC,GAAQ,cAAc,WAC3D,MAAO,CACLmC,UAAWnC,EAAMc,MAAMqB,UACvBQ,cAAe3C,EAAMc,MAAM6B,cAC3BC,cAAe5C,EAAMc,MAAM8B,cAC3BC,UAAW/F,EAAS,CAAC,EAAGkD,EAAMc,MAAM+B,UAAW,CAC7CC,MAAOhG,EAAS,CAAC,EAAGkD,EAAMc,MAAM+B,WAAa7C,EAAMc,MAAM+B,UAAUC,MAAO,CACxEN,UAAWxC,EAAMuC,UACjBQ,QAAS/C,EAAMuC,YAEjBS,WAAY,CACVR,SAAS,GAEXS,oBAAqBjD,EAAMiD,sBAGjC,IAEA5D,EAAgBxB,EAAuBmC,GAAQ,kBAAkB,WAC/D,OAAQA,EAAMoC,YAAepC,EAAMkD,MAAMjB,KAAsBnF,EAAS,CACtE4E,SAAU1B,EAAMkD,MAAMjB,KAAKkB,QAAQC,OAAO1B,UACzC1B,EAAMkD,MAAMjB,KAAKoB,QAF4B5B,CAGlD,IAEApC,EAAgBxB,EAAuBmC,GAAQ,sBAAsB,WACnE,OAAQA,EAAMkD,MAAMjB,KAAmBjC,EAAMkD,MAAMf,eAAxBD,CAC7B,IAEA7C,EAAgBxB,EAAuBmC,GAAQ,iBAAiB,WAC9D,OAAQA,EAAMuC,WAAcvC,EAAMkD,MAAMjB,KAA2BjC,EAAMkD,MAAMjB,KAAKqB,YAArCvB,CACjD,IAEA1C,EAAgBxB,EAAuBmC,GAAQ,2BAA2B,WACxE,OAAOA,EAAMkD,MAAMjB,KAAOjC,EAAMkD,MAAMjB,KAAKsB,UAAOrB,CACpD,IAEA7C,EAAgBxB,EAAuBmC,GAAQ,yBAAyB,WACjEA,EAAMwD,iBAEXxD,EAAMwD,eAAeC,UAErBzD,EAAMwD,eAAiB,KACzB,IAEAnE,EAAgBxB,EAAuBmC,GAAQ,wBAAwB,WACrEA,EAAM0D,wBAEN,IACItB,EADwBvE,EAAuBmC,GACZoC,WAEnCuB,EAAmB3D,EAAMc,MAAM6C,iBAC9BA,GAAqBvB,IAC1BpC,EAAMwD,eAAiB,IAAI,IAASG,EAAkBvB,EAAYpC,EAAM4D,cAC1E,IAEAvE,EAAgBxB,EAAuBmC,GAAQ,kBAAkB,WAC3DA,EAAMwD,gBACRxD,EAAMwD,eAAeK,gBAEzB,IAEO7D,CACT,CA/GA3B,EAAe2D,EAAajC,GAiH5B,IAAIU,EAASuB,EAAYxE,UA+CzB,OA7CAiD,EAAOqD,mBAAqB,SAA4BC,EAAWC,GAE7DpG,KAAKkD,MAAMqB,YAAc4B,EAAU5B,WAAavE,KAAKkD,MAAM6C,mBAAqBI,EAAUJ,kBAAoB/F,KAAKkD,MAAM8B,gBAAkBmB,EAAUnB,eAAkB,IAAUhF,KAAKkD,MAAM+B,UAAWkB,EAAUlB,UAAW,CAC9NoB,QAAQ,IAUCrG,KAAKkD,MAAM6B,gBAAkBoB,EAAUpB,eAAiB/E,KAAK4F,iBACtE5F,KAAKkD,MAAM6B,cAAgB/E,KAAK4F,eAAeU,uBAAyBtG,KAAK4F,eAAeW,yBAF5FvG,KAAK0E,uBASH0B,EAAU7B,YAAcvE,KAAKsF,MAAMf,WACrCvE,KAAKiG,gBAET,EAEApD,EAAOC,qBAAuB,WAC5BW,EAAOzD,KAAKkD,MAAMuB,SAAU,MAC5BzE,KAAK8F,uBACP,EAEAjD,EAAOE,OAAS,WACd,OAAOK,EAAYpD,KAAKkD,MAAMC,SAAvBC,CAAiC,CACtCM,IAAK1D,KAAKwG,cACVC,MAAOzG,KAAK0G,iBACZnC,UAAWvE,KAAK2G,qBAChBC,gBAAiB5G,KAAK6G,0BACtBZ,eAAgBjG,KAAKiG,eACrBa,WAAY,CACVpD,IAAK1D,KAAK+G,aACVN,MAAOzG,KAAKgH,kBAGlB,EAEO5C,CACT,CAlKA,CAkKE,aAEF3C,EAAgB2C,EAAa,eAAgB,CAC3CG,UAAW,SACXQ,eAAe,EACfgB,sBAAkBzB,EAClBU,eAAe,IAGjB,IAAIiC,EAAa,eAEF,SAASC,EAAOC,GAC7B,IAAIpB,EAAmBoB,EAAKpB,iBACxB7C,ECnMS,SAAuCxD,EAAQ0H,GAC5D,GAAc,MAAV1H,EAAgB,MAAO,CAAC,EAC5B,IAAIJ,EAAS,CAAC,EACd,IAAK,IAAIK,KAAOD,EACd,GAAIP,OAAOS,UAAUC,eAAeC,KAAKJ,EAAQC,GAAM,CACrD,GAAIyH,EAASC,QAAQ1H,IAAQ,EAAG,SAChCL,EAAOK,GAAOD,EAAOC,EACvB,CAEF,OAAOL,CACT,CDyLcgI,CAA8BH,EAAM,CAAC,qBAEjD,OAAO,gBAAoBnF,EAA4BuF,SAAU,MAAM,SAAU5E,GAC/E,OAAO,gBAAoByB,EAAalF,EAAS,CAC/C6G,sBAAuCzB,IAArByB,EAAiCA,EAAmBpD,GACrEO,GACL,GACF,C,wBEjMIsE,EAEJ,SAAUrF,GAGR,SAASqF,IAGP,IAFA,IAAIpF,EAEKC,EAAO7C,UAAUC,OAAQ6C,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQhD,UAAUgD,GAUzB,OALAf,EAAgBxB,EAFhBmC,EAAQD,EAAiBrC,KAAKC,MAAMoC,EAAkB,CAACnC,MAAMyC,OAAOH,KAAUtC,MAE/B,cAAc,SAAU2D,GACrEF,EAAOrB,EAAMc,MAAMuB,SAAUd,GAC7BJ,EAAWnB,EAAMc,MAAMD,iBAAkBU,EAC3C,IAEOvB,CACT,CAjBA3B,EAAe+G,EAAgBrF,GAmB/B,IAAIU,EAAS2E,EAAe5H,UAa5B,OAXAiD,EAAOC,qBAAuB,WAC5BW,EAAOzD,KAAKkD,MAAMuB,SAAU,KAC9B,EAEA5B,EAAOE,OAAS,WAEd,OADA,IAAQ0E,QAAQzH,KAAKkD,MAAMD,kBAAmB,oEACvCG,EAAYpD,KAAKkD,MAAMC,SAAvBC,CAAiC,CACtCM,IAAK1D,KAAK0H,YAEd,EAEOF,CACT,CAlCA,CAkCE,aAEa,SAASG,EAAUzE,GAChC,OAAO,gBAAoBjB,EAAkCsF,SAAU,MAAM,SAAUtE,GACrF,OAAO,gBAAoBuE,EAAgBtI,EAAS,CAClD+D,iBAAkBA,GACjBC,GACL,GACF,C,oBCrCA,IAEI0E,EAAU,WAAY,EA2C1BC,EAAOC,QAAUF,C", "sources": ["webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/extends.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/typeof.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/defineProperty.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/Manager.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/utils.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/Popper.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/lib/esm/Reference.js", "webpack://heaplabs-coldemail-app/./node_modules/react-popper/node_modules/warning/warning.js"], "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "_typeof", "Symbol", "iterator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t", "r", "e", "toPrimitive", "TypeError", "String", "Number", "_defineProperty", "obj", "value", "defineProperty", "enumerable", "configurable", "writable", "ManagerReferenceNodeContext", "ManagerReferenceNodeSetterContext", "Manager", "_React$Component", "_this", "_len", "args", "Array", "_key", "concat", "newReferenceNode", "referenceNode", "forceUpdate", "_proto", "componentWillUnmount", "render", "Provider", "setReferenceNode", "props", "children", "unwrapArray", "arg", "isArray", "safeInvoke", "fn", "setRef", "ref", "node", "current", "initialStyle", "position", "top", "left", "opacity", "pointerEvents", "initialArrowStyle", "InnerPopper", "data", "undefined", "placement", "popperNode", "innerRef", "updatePopperInstance", "arrowNode", "enabled", "order", "setState", "eventsEnabled", "positionFixed", "modifiers", "arrow", "element", "applyStyle", "updateStateModifier", "state", "offsets", "popper", "styles", "arrowStyles", "hide", "popperInstance", "destroy", "destroyPopperInstance", "referenceElement", "getOptions", "scheduleUpdate", "componentDidUpdate", "prevProps", "prevState", "strict", "enableEventListeners", "disableEventListeners", "setPopperNode", "style", "getPopperStyle", "getPopperPlacement", "outOfBoundaries", "getOutOfBoundariesState", "arrowProps", "setArrowNode", "getArrowStyle", "placements", "<PERSON><PERSON>", "_ref", "excluded", "indexOf", "_objectWithoutPropertiesLoose", "Consumer", "InnerReference", "Boolean", "ref<PERSON><PERSON><PERSON>", "Reference", "warning", "module", "exports"], "sourceRoot": ""}