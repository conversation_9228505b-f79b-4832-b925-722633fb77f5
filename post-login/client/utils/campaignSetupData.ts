/*
* getAllChannelSetupTabs(campaignStore):
    returns -

    3 initial tabs + accountSetting tabs (if any) + 2 last tabs

accountSetting tabs (distinctChannelSetupTypes() ->
  1. Looping over the no. of steps it has and appending it to my list.
  2. The distinction check is done before appending so list works as a set.
*/

import { Campaigns } from '@sr/shared-product-components';
import { fetchIcon } from "./sr-utils";

interface SubStepType {
  step_type: Campaigns.ICampaignStepType,
  description: string,
  tab_route: Campaigns.ITabRouteType
}

export const subStepObject: SubStepType[] = [
  {
    step_type: "general_task",
    description: "Ready for general task",
    tab_route: "channel_setup/general"
  },
  {
    step_type: "move_to_another_campaign",
    description: "Ready for move to another campaign",
    tab_route: "channel_setup/general"
  },
  {
    step_type: "send_email",
    description: "Connect your email account",
    tab_route: "channel_setup/email"
  },
  {
    step_type: "manual_send_email",
    description: "Connect your email account",
    tab_route: "channel_setup/email"
  },
  {
    step_type: "manual_email_magic_content",
    description: "Connect your email account",
    tab_route: "channel_setup/email"
  },
  {
    step_type: "auto_email_magic_content",
    description: "Connect your email account",
    tab_route: "channel_setup/email"
  },
  {
    step_type: "send_sms",
    description: "Add SMS account settings",
    tab_route: "channel_setup/sms"
  },
  {
    step_type: "send_whatsapp_message",
    description: "Add Whatsapp account settings",
    tab_route: "channel_setup/whatsapp"
  },
  {
    step_type: "send_linkedin_connection_request",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "send_linkedin_message",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "send_linkedin_inmail",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "linkedin_view_profile",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "auto_send_linkedin_connection_request",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "auto_send_linkedin_message",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "auto_send_linkedin_inmail",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: "auto_linkedin_view_profile",
    description: "Add Linkedin account settings",
    tab_route: "channel_setup/linkedin"
  },
  {
    step_type: 'call',
    description: "Add phone number",
    tab_route: "channel_setup/call"
  }
]


export const getTabRoute = (step_type: Campaigns.ICampaignStepType) => {
  const route = subStepObject.filter((s) => s.step_type == step_type)[0].tab_route
  return route
};

const getTabDescription = (step_type: Campaigns.ICampaignStepType) => {
  const description = subStepObject.filter((s) => s.step_type == step_type)[0].description
  return description
};

export const distinctChannelSetupTypes = (campaignStore: Campaigns.ICampaignStore) => {
  let channelSetupTypes: Campaigns.ICampaignStepType[] = [];
  campaignStore
    .currentCampaign
    .contentTabInfo
    .stepVariants
    .map((c) => {

      (channelSetupTypes.filter((eachSetupType) => getChannelType(eachSetupType) == getChannelType(c.step_type)).length > 0
        ? ""
        : channelSetupTypes.push(c.step_type));
    });


  // const initialTabLength = 3
  return channelSetupTypes.map((stepType, index) => {
    const tab_route: Campaigns.ITabRouteType = getTabRoute(stepType);
    const tab_description = getTabDescription(stepType);
    const channelSetupTypeObject: Campaigns.ISubTab = {
      // tabNumber: index + initialTabLength,
      title: tab_description,  // sub channel types don't have description as there title
      tabRoute: tab_route,
      step_type: stepType,
      // isDone: false,
      // errorMessage: '',
      // disabled: false,
      // tabDescription:'',
    };
    return channelSetupTypeObject;
  });
};

export const getNewCampaignTabs = (campaignStore: Campaigns.ICampaignStore) => {
  const campaignBasicInfo = campaignStore.getBasicInfo;
  const totalSteps = campaignBasicInfo.stats?.total_steps || 0;
  const totalProspects = campaignStore.currentCampaign?.prospectsNumber; //Convert to function-TODO
  const campaignEmailIdExists =
    campaignBasicInfo.settings?.campaign_email_settings.length > 0;
  const disableSettingsPreview =
    totalProspects !== 0 &&
      totalSteps !== 0 &&
      campaignEmailIdExists
      ? false
      : true;
  const channelSetupSubTabs = distinctChannelSetupTypes(campaignStore);
  const channelSetupTypesLength = channelSetupSubTabs.length;

  const steps: Campaigns.INewCampaignTabData[] = [
    {
      tabNumber: 1,
      title: "Prospects",
      tabRoute: "prospects",
      tabDescription: "Contact list",
      disabled: false,
      errorMessage: "At least 1 prospect needed",
      // isDone: totalProspects != 0
    },
    {
      tabNumber: 2,
      title: "Content",
      tabRoute: "content",
      tabDescription: "Create your campaign steps",
      disabled: false,
      errorMessage: "At least one step needed",
      // isDone: totalSteps > 0
    },
    {
      tabNumber: 3,
      title: "Channel Setup",
      tabRoute: "channel_setup",
      tabDescription: "Add your account details",
      errorMessage: "At least 1 account should be added",
      disabled: false,
      // isDone: false,
      subTabs: channelSetupTypesLength > 0 ? channelSetupSubTabs : undefined,
    },
    {
      tabNumber: 4,
      title: "Campaign Settings",
      tabRoute: "settings",
      errorMessage: "",
      tabDescription: "View, edit settings",
      disabled: false,
      // isDone: false,
    }
  ];

  if (campaignBasicInfo.settings.campaign_type !== "magic_content") {
    steps.push({
      tabNumber: 5,
      title: "Preview & Start",
      tabRoute: "preview",
      tabDescription: "Start campaign",
      disabled: disableSettingsPreview,
      errorMessage: ""
    })
  }

  const allChannelSettingTabs = steps  // Initial Setup Tabs +
    // .concat(channelSetupSubTabs)  // Channel Setup Sub Tabs +
    .concat([                   // Remaining Setup Tabs i.e campaign setting + preview and start

    ]);

  // // This is used for navigation, can be converted to interface
  const result = {
    tabs: allChannelSettingTabs,
    channelSteps: channelSetupTypesLength,
  };

  return result;
};


export const subStepStatus = (campaignStore: Campaigns.ICampaignStore) => {
  const steps = distinctChannelSetupTypes(campaignStore)

  const status = steps.map(step => (
    {
      tabRoute: step.tabRoute,
      tabStatus: isDone(step.tabRoute, campaignStore),
      step_type: step.step_type
    }))

  return status;
}

export const isDone = (tabRoute: Campaigns.ITabRouteType, campaignStore: Campaigns.ICampaignStore) => {
  const campaignBasicInfo = campaignStore.getBasicInfo;
  const campaignEmailIdExists =
    campaignBasicInfo.settings.campaign_email_settings.length > 0;

  const linkedin_setting_uuid = campaignBasicInfo.settings.linkedin_setting_uuid;
  const whatsapp_setting_uuid = campaignBasicInfo.settings.whatsapp_setting_uuid;
  const sms_setting_uuid = campaignBasicInfo.settings.sms_setting_uuid;
  const call_setting_uuid = campaignBasicInfo.settings.call_setting_uuid;

  if (tabRoute == 'channel_setup/email') {
    return campaignEmailIdExists
  } else if (tabRoute == 'channel_setup/linkedin') {
    return !!linkedin_setting_uuid ? true : false;
  } else if (tabRoute == 'channel_setup/sms') {
    return !!sms_setting_uuid ? true : false;
  } else if (tabRoute == 'channel_setup/whatsapp') {
    return !!whatsapp_setting_uuid ? true : false;
  } else if (tabRoute == 'channel_setup/call') {
    return !!call_setting_uuid ? true : false;
  } else {
    return true;
  }
}

export const getStepIcon = (stepType: Campaigns.ICampaignStepType) => {
  const step_type = stepType
  const icon = step_type == 'general_task' ? fetchIcon('sr_icon_mail') :
    step_type == 'send_email' ? fetchIcon('sr_icon_mail') :
      step_type == "manual_email_magic_content" ? fetchIcon('sr_icon_mail') :
        step_type == 'auto_email_magic_content' ? fetchIcon('sr_icon_mail') :
          step_type == 'linkedin_view_profile' ? fetchIcon('sr_icon_linkedin') :
            step_type == 'send_linkedin_connection_request' ? fetchIcon('sr_icon_linkedin') :
              step_type == 'send_linkedin_inmail' ? fetchIcon('sr_icon_linkedin') :
                step_type == 'send_linkedin_message' ? fetchIcon('sr_icon_linkedin') :
                  step_type == 'auto_linkedin_view_profile' ? fetchIcon('sr_icon_linkedin') :
                    step_type == 'auto_send_linkedin_connection_request' ? fetchIcon('sr_icon_linkedin') :
                      step_type == 'auto_send_linkedin_inmail' ? fetchIcon('sr_icon_linkedin') :
                        step_type == 'auto_send_linkedin_message' ? fetchIcon('sr_icon_linkedin') :
                          step_type == 'send_sms' ? fetchIcon('sr_icon_mail') :
                            step_type == 'send_whatsapp_message' ? fetchIcon('sr_icon_whatsapp') :
                              step_type == 'call' ? fetchIcon('sr_icon_phone') :
                                step_type == 'manual_send_email' ? fetchIcon('sr_icon_mail') : fetchIcon('sr_icon_mail')
  return icon;
}

export const getDisplayStepType = (step_type: Campaigns.ICampaignStepType) => {
  const displayStepType = step_type == 'general_task' ? 'General task' :
    step_type == 'send_email' ? 'Auto Email' :
      step_type == 'linkedin_view_profile' ? 'Linkedin View Profile' :
        step_type == 'send_linkedin_connection_request' ? 'Linkedin Connection' :
          step_type == "manual_email_magic_content" ? 'Manual Email ' :
            step_type == 'auto_email_magic_content' ? 'Auto Email' :
              step_type == 'send_linkedin_inmail' ? 'Linkedin InMail' :
                step_type == 'send_linkedin_message' ? 'Linkedin Message' :
                  step_type == 'auto_linkedin_view_profile' ? 'Auto Linkedin View Profile' :
                    step_type == 'auto_send_linkedin_connection_request' ? 'Auto Linkedin Connection' :
                      step_type == 'auto_send_linkedin_inmail' ? 'Auto Linkedin InMail' :
                        step_type == 'auto_send_linkedin_message' ? 'Auto Linkedin Message' :
                          step_type == 'send_sms' ? 'SMS Message' :
                            step_type == 'send_whatsapp_message' ? 'Whatsapp Message' :
                              step_type == 'call' ? 'Call' :
                                step_type == 'manual_send_email' ? 'Manual Email' : 'Unknown Step Type'

  return displayStepType
}

export const getChannelType = (step_type: Campaigns.ICampaignStepType) => {
  const displayStepType = step_type == 'general_task' ? 'General Task Type' :
    step_type == 'send_email' ? 'Email' :
      step_type == 'manual_send_email' ? 'Email' :
        step_type == "manual_email_magic_content" ? 'Email' :
          step_type == 'auto_email_magic_content' ? 'Email' :
            step_type == 'linkedin_view_profile' ? 'Linkedin' :
              step_type == 'send_linkedin_connection_request' ? 'Linkedin' :
                step_type == 'send_linkedin_inmail' ? 'Linkedin' :
                  step_type == 'send_linkedin_message' ? 'Linkedin' :
                    step_type == 'auto_linkedin_view_profile' ? 'Linkedin' :
                      step_type == 'auto_send_linkedin_connection_request' ? 'Linkedin' :
                        step_type == 'auto_send_linkedin_inmail' ? 'Linkedin' :
                          step_type == 'auto_send_linkedin_message' ? 'Linkedin' :
                            step_type == 'send_sms' ? 'Sms' :
                              step_type == 'call' ? 'Call' :
                                step_type == 'send_whatsapp_message' ? 'Whatsapp' : 'Unknown Step Type'

  return displayStepType
}

export const getChannelSetupIsDone = (campaignStore: Campaigns.ICampaignStore) => {
  const basicInfo = campaignStore.getBasicInfo
  const subSteps = distinctChannelSetupTypes(campaignStore)
  console.log('debug subSteps', subSteps);
  let isDone = true
  if (subSteps.length > 0) {
    subSteps.map((step) => {
      const channel = getChannelType(step.step_type)
      console.log('debug channel', channel);
      if (channel == "Email") {
        // console.log('debug email step', (basicInfo.settings.sender_email_settings_ids && basicInfo.settings.receiver_email_settings_id ? true : false) );
        isDone = isDone && (basicInfo.settings.campaign_email_settings.length > 0 ? true : false)
      } else if (channel == 'Linkedin') {
        isDone = isDone && (basicInfo.settings.linkedin_setting_uuid ? true : false)
      } else if (channel == 'Whatsapp') {
        isDone = isDone && (basicInfo.settings.whatsapp_setting_uuid ? true : false)
      } else if (channel == 'Sms') {
        isDone = isDone && (basicInfo.settings.sms_setting_uuid ? true : false)
      } else if (channel == 'Call') {
        isDone = isDone && (basicInfo.settings.call_setting_uuid ? true : false)
      }

    })

    console.log('debug isDone', isDone);

    return isDone
  } else {
    return false;
  }
}
