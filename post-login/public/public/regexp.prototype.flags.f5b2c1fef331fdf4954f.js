"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["regexp.prototype.flags"],{73994:function(t){var e=Object,r=TypeError;t.exports=function(){if(null!=this&&this!==e(this))throw new r("RegExp.prototype.flags getter called on non-object");var t="";return this.global&&(t+="g"),this.ignoreCase&&(t+="i"),this.multiline&&(t+="m"),this.dotAll&&(t+="s"),this.unicode&&(t+="u"),this.sticky&&(t+="y"),t}},80251:function(t,e,r){var o=r(19170),p=r(8958),s=r(73994),i=r(71856),n=r(47278),a=p(s);o(a,{getPolyfill:i,implementation:s,shim:n}),t.exports=a},71856:function(t,e,r){var o=r(73994),p=r(19170).supportsDescriptors,s=Object.getOwnPropertyDescriptor,i=TypeError;t.exports=function(){if(!p)throw new i("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");if("gim"===/a/gim.flags){var t=s(RegExp.prototype,"flags");if(t&&"function"===typeof t.get&&"boolean"===typeof/a/.dotAll)return t.get}return o}},47278:function(t,e,r){var o=r(19170).supportsDescriptors,p=r(71856),s=Object.getOwnPropertyDescriptor,i=Object.defineProperty,n=TypeError,a=Object.getPrototypeOf,l=/a/;t.exports=function(){if(!o||!a)throw new n("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var t=p(),e=a(l),r=s(e,"flags");return r&&r.get===t||i(e,"flags",{configurable:!0,enumerable:!1,get:t}),t}}}]);
//# sourceMappingURL=regexp.prototype.flags.7e0dc22f6c9151d2731b3bd97f30f2ff.js.map