"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@headlessui"],{38624:function(e,t,n){n.d(t,{h:function(){return _}});var r,o,l=n(89526),i=n(1480),a=n(35760),u=n(86517),s=n(95532),c=n(15673),d=n(9336),f=n(42670),p=n(49114),v=n(80786),m=n(32602),b=n(15815),g=n(78320),h=n(98943),y=n(64601),x=n(73830),R=n(92646),E=n(25328),S=n(76762),P=n(74388),T=n(8247),O=((o=O||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),I=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(I||{}),C=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(C||{}),w=((r=w||{})[r.OpenCombobox=0]="OpenCombobox",r[r.CloseCombobox=1]="CloseCombobox",r[r.GoToOption=2]="GoToOption",r[r.RegisterOption=3]="RegisterOption",r[r.UnregisterOption=4]="UnregisterOption",r);function D(e,t=e=>e){let n=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,r=(0,E.z2)(t(e.options.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{options:r,activeOptionIndex:o}}let k={1:e=>e.dataRef.current.disabled||1===e.comboboxState?e:{...e,activeOptionIndex:null,comboboxState:1},0(e){if(e.dataRef.current.disabled||0===e.comboboxState)return e;let t=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,r=e.options.findIndex((e=>n(e.dataRef.current.value)));return-1!==r&&(t=r),{...e,comboboxState:0,activeOptionIndex:t}},2(e,t){var n;if(e.dataRef.current.disabled||e.dataRef.current.optionsRef.current&&!e.dataRef.current.optionsPropsRef.current.static&&1===e.comboboxState)return e;let r=D(e);if(null===r.activeOptionIndex){let e=r.options.findIndex((e=>!e.dataRef.current.disabled));-1!==e&&(r.activeOptionIndex=e)}let o=(0,b.d)(t,{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,activeOptionIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n={id:t.id,dataRef:t.dataRef},r=D(e,(e=>[...e,n]));null===e.activeOptionIndex&&e.dataRef.current.isSelected(t.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(n));let o={...e,...r,activationTrigger:1};return e.dataRef.current.__demoMode&&void 0===e.dataRef.current.value&&(o.activeOptionIndex=0),o},4:(e,t)=>{let n=D(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}}},M=(0,l.createContext)(null);function F(e){let t=(0,l.useContext)(M);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,F),t}return t}M.displayName="ComboboxActionsContext";let A=(0,l.createContext)(null);function L(e){let t=(0,l.useContext)(A);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,L),t}return t}function N(e,t){return(0,x.E)(t.type,k,e,t)}A.displayName="ComboboxDataContext";let z=l.Fragment,V=(0,h.yV)((function(e,t){let{name:n,value:r,onChange:o,disabled:i=!1,__demoMode:a=!1,nullable:s=!1,multiple:d=!1,...p}=e,[v,m]=(0,l.useReducer)(N,{dataRef:(0,l.createRef)(),comboboxState:a?0:1,options:[],activeOptionIndex:null,activationTrigger:1}),g=(0,l.useRef)(!1),y=(0,l.useRef)({static:!1,hold:!1}),E=(0,l.useRef)({displayValue:void 0}),T=(0,l.useRef)(null),O=(0,l.useRef)(null),I=(0,l.useRef)(null),C=(0,l.useRef)(null),w=(0,u.z)(((e,t)=>e===t)),D=(0,l.useCallback)((e=>(0,x.E)(k.mode,{1:()=>r.some((t=>w(t,e))),0:()=>w(r,e)})),[r]),k=(0,l.useMemo)((()=>({...v,optionsPropsRef:y,inputPropsRef:E,labelRef:T,inputRef:O,buttonRef:I,optionsRef:C,value:r,disabled:i,mode:d?1:0,get activeOptionIndex(){if(g.current&&null===v.activeOptionIndex&&v.options.length>0){let e=v.options.findIndex((e=>!e.dataRef.current.disabled));if(-1!==e)return e}return v.activeOptionIndex},compare:w,isSelected:D,nullable:s,__demoMode:a})),[r,i,d,s,a,v]);(0,c.e)((()=>{v.dataRef.current=k}),[k]),(0,f.O)([k.buttonRef,k.inputRef,k.optionsRef],(()=>m({type:1})),0===k.comboboxState);let F=(0,l.useMemo)((()=>({open:0===k.comboboxState,disabled:i,activeIndex:k.activeOptionIndex,activeOption:null===k.activeOptionIndex?null:k.options[k.activeOptionIndex].dataRef.current.value})),[k,i]),L=(0,l.useCallback)((()=>{var e;if(!k.inputRef.current)return;let t=E.current.displayValue;k.inputRef.current.value="function"==typeof t?null!=(e=t(r))?e:"":"string"==typeof r?r:""}),[r,k.inputRef,E]),V=(0,u.z)((e=>{let t=k.options.find((t=>t.id===e));!t||(U(t.dataRef.current.value),L())})),B=(0,u.z)((()=>{if(null!==k.activeOptionIndex){let{dataRef:e,id:t}=k.options[k.activeOptionIndex];U(e.current.value),L(),m({type:2,focus:b.T.Specific,id:t})}})),Y=(0,u.z)((()=>{m({type:0}),g.current=!0})),$=(0,u.z)((()=>{m({type:1}),g.current=!1})),j=(0,u.z)(((e,t,n)=>(g.current=!1,e===b.T.Specific?m({type:2,focus:b.T.Specific,id:t,trigger:n}):m({type:2,focus:e,trigger:n})))),H=(0,u.z)(((e,t)=>(m({type:3,id:e,dataRef:t}),()=>m({type:4,id:e})))),U=(0,u.z)((e=>(0,x.E)(k.mode,{0:()=>o(e),1(){let t=k.value.slice(),n=t.indexOf(e);return-1===n?t.push(e):t.splice(n,1),o(t)}}))),_=(0,l.useMemo)((()=>({onChange:U,registerOption:H,goToOption:j,closeCombobox:$,openCombobox:Y,selectActiveOption:B,selectOption:V})),[]);(0,c.e)((()=>{1===k.comboboxState&&L()}),[L,k.comboboxState]),(0,c.e)(L,[L]);let G=null===t?{}:{ref:t};return l.createElement(M.Provider,{value:_},l.createElement(A.Provider,{value:k},l.createElement(P.up,{value:(0,x.E)(k.comboboxState,{0:P.ZM.Open,1:P.ZM.Closed})},null!=n&&null!=r&&(0,R.t)({[n]:r}).map((([e,t])=>l.createElement(S._,{features:S.A.Hidden,...(0,h.oA)({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:e,value:t})}))),(0,h.sY)({ourProps:G,theirProps:p,slot:F,defaultTag:z,name:"Combobox"}))))})),B=(0,h.yV)((function(e,t){var n,r;let{value:o,onChange:d,displayValue:f,type:p="text",...m}=e,g=L("Combobox.Input"),y=F("Combobox.Input"),R=(0,v.T)(g.inputRef,t),E=g.inputPropsRef,S=`headlessui-combobox-input-${(0,s.M)()}`,P=(0,a.G)();(0,c.e)((()=>{E.current.displayValue=f}),[f,E]);let O=(0,u.z)((e=>{switch(e.key){case T.R.Backspace:case T.R.Delete:if(0!==g.comboboxState||0!==g.mode||!g.nullable)return;let t=e.currentTarget;P.requestAnimationFrame((()=>{""===t.value&&(y.onChange(null),g.optionsRef.current&&(g.optionsRef.current.scrollTop=0),y.goToOption(b.T.Nothing))}));break;case T.R.Enter:if(0!==g.comboboxState)return;if(e.preventDefault(),e.stopPropagation(),null===g.activeOptionIndex)return void y.closeCombobox();y.selectActiveOption(),0===g.mode&&y.closeCombobox();break;case T.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),(0,x.E)(g.comboboxState,{0:()=>{y.goToOption(b.T.Next)},1:()=>{y.openCombobox()}});case T.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),(0,x.E)(g.comboboxState,{0:()=>{y.goToOption(b.T.Previous)},1:()=>{y.openCombobox(),P.nextFrame((()=>{g.value||y.goToOption(b.T.Last)}))}});case T.R.Home:case T.R.PageUp:return e.preventDefault(),e.stopPropagation(),y.goToOption(b.T.First);case T.R.End:case T.R.PageDown:return e.preventDefault(),e.stopPropagation(),y.goToOption(b.T.Last);case T.R.Escape:return 0!==g.comboboxState?void 0:(e.preventDefault(),g.optionsRef.current&&!g.optionsPropsRef.current.static&&e.stopPropagation(),y.closeCombobox());case T.R.Tab:if(0!==g.comboboxState)return;y.selectActiveOption(),y.closeCombobox()}})),I=(0,u.z)((e=>{y.openCombobox(),null==d||d(e)})),C=(0,i.v)((()=>{if(g.labelRef.current)return[g.labelRef.current.id].join(" ")}),[g.labelRef.current]),w=(0,l.useMemo)((()=>({open:0===g.comboboxState,disabled:g.disabled})),[g]),D={ref:R,id:S,role:"combobox",type:p,"aria-controls":null==(n=g.optionsRef.current)?void 0:n.id,"aria-expanded":g.disabled?void 0:0===g.comboboxState,"aria-activedescendant":null===g.activeOptionIndex||null==(r=g.options[g.activeOptionIndex])?void 0:r.id,"aria-multiselectable":1===g.mode||void 0,"aria-labelledby":C,disabled:g.disabled,onKeyDown:O,onChange:I};return(0,h.sY)({ourProps:D,theirProps:m,slot:w,defaultTag:"input",name:"Combobox.Input"})})),Y=(0,h.yV)((function(e,t){var n;let r=L("Combobox.Button"),o=F("Combobox.Button"),c=(0,v.T)(r.buttonRef,t),d=`headlessui-combobox-button-${(0,s.M)()}`,f=(0,a.G)(),m=(0,u.z)((e=>{switch(e.key){case T.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),1===r.comboboxState&&o.openCombobox(),f.nextFrame((()=>{var e;return null==(e=r.inputRef.current)?void 0:e.focus({preventScroll:!0})}));case T.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),1===r.comboboxState&&(o.openCombobox(),f.nextFrame((()=>{r.value||o.goToOption(b.T.Last)}))),f.nextFrame((()=>{var e;return null==(e=r.inputRef.current)?void 0:e.focus({preventScroll:!0})}));case T.R.Escape:return 0!==r.comboboxState?void 0:(e.preventDefault(),r.optionsRef.current&&!r.optionsPropsRef.current.static&&e.stopPropagation(),o.closeCombobox(),f.nextFrame((()=>{var e;return null==(e=r.inputRef.current)?void 0:e.focus({preventScroll:!0})})));default:return}})),g=(0,u.z)((e=>{if((0,y.P)(e.currentTarget))return e.preventDefault();0===r.comboboxState?o.closeCombobox():(e.preventDefault(),o.openCombobox()),f.nextFrame((()=>{var e;return null==(e=r.inputRef.current)?void 0:e.focus({preventScroll:!0})}))})),x=(0,i.v)((()=>{if(r.labelRef.current)return[r.labelRef.current.id,d].join(" ")}),[r.labelRef.current,d]),R=(0,l.useMemo)((()=>({open:0===r.comboboxState,disabled:r.disabled})),[r]),E=e,S={ref:c,id:d,type:(0,p.f)(e,r.buttonRef),tabIndex:-1,"aria-haspopup":!0,"aria-controls":null==(n=r.optionsRef.current)?void 0:n.id,"aria-expanded":r.disabled?void 0:0===r.comboboxState,"aria-labelledby":x,disabled:r.disabled,onClick:g,onKeyDown:m};return(0,h.sY)({ourProps:S,theirProps:E,slot:R,defaultTag:"button",name:"Combobox.Button"})})),$=(0,h.yV)((function(e,t){let n=L("Combobox.Label"),r=`headlessui-combobox-label-${(0,s.M)()}`,o=(0,v.T)(n.labelRef,t),i=(0,u.z)((()=>{var e;return null==(e=n.inputRef.current)?void 0:e.focus({preventScroll:!0})})),a=(0,l.useMemo)((()=>({open:0===n.comboboxState,disabled:n.disabled})),[n]);return(0,h.sY)({ourProps:{ref:o,id:r,onClick:i},theirProps:e,slot:a,defaultTag:"label",name:"Combobox.Label"})})),j=h.AN.RenderStrategy|h.AN.Static,H=(0,h.yV)((function(e,t){var n;let{hold:r=!1,...o}=e,a=L("Combobox.Options"),u=(0,v.T)(a.optionsRef,t),d=`headlessui-combobox-options-${(0,s.M)()}`,f=(0,P.oJ)(),p=null!==f?f===P.ZM.Open:0===a.comboboxState;(0,c.e)((()=>{var t;a.optionsPropsRef.current.static=null!=(t=e.static)&&t}),[a.optionsPropsRef,e.static]),(0,c.e)((()=>{a.optionsPropsRef.current.hold=r}),[a.optionsPropsRef,r]),(0,m.B)({container:a.optionsRef.current,enabled:0===a.comboboxState,accept:e=>"option"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let b=(0,i.v)((()=>{var e,t,n;return null!=(n=null==(e=a.labelRef.current)?void 0:e.id)?n:null==(t=a.buttonRef.current)?void 0:t.id}),[a.labelRef.current,a.buttonRef.current]),g=(0,l.useMemo)((()=>({open:0===a.comboboxState})),[a]),y={"aria-activedescendant":null===a.activeOptionIndex||null==(n=a.options[a.activeOptionIndex])?void 0:n.id,"aria-labelledby":b,role:"listbox",id:d,ref:u};return(0,h.sY)({ourProps:y,theirProps:o,slot:g,defaultTag:"ul",features:j,visible:p,name:"Combobox.Options"})})),U=(0,h.yV)((function(e,t){var n,r;let{disabled:o=!1,value:i,...a}=e,f=L("Combobox.Option"),p=F("Combobox.Option"),m=`headlessui-combobox-option-${(0,s.M)()}`,y=null!==f.activeOptionIndex&&f.options[f.activeOptionIndex].id===m,x=f.isSelected(i),R=(0,l.useRef)(null),E=(0,d.E)({disabled:o,value:i,domRef:R,textValue:null==(r=null==(n=R.current)?void 0:n.textContent)?void 0:r.toLowerCase()}),S=(0,v.T)(t,R),P=(0,u.z)((()=>p.selectOption(m)));(0,c.e)((()=>p.registerOption(m,E)),[E,m]);let T=(0,l.useRef)(!f.__demoMode);(0,c.e)((()=>{if(!f.__demoMode)return;let e=(0,g.k)();return e.requestAnimationFrame((()=>{T.current=!0})),e.dispose}),[]),(0,c.e)((()=>{if(0!==f.comboboxState||!y||!T.current||0===f.activationTrigger)return;let e=(0,g.k)();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=R.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[R,y,f.comboboxState,f.activationTrigger,f.activeOptionIndex]);let O=(0,u.z)((e=>{var t;if(o)return e.preventDefault();P(),0===f.mode&&(p.closeCombobox(),null==(t=f.inputRef.current)||t.focus({preventScroll:!0}))})),I=(0,u.z)((()=>{if(o)return p.goToOption(b.T.Nothing);p.goToOption(b.T.Specific,m)})),C=(0,u.z)((()=>{o||y||p.goToOption(b.T.Specific,m,0)})),w=(0,u.z)((()=>{o||!y||f.optionsPropsRef.current.hold||p.goToOption(b.T.Nothing)})),D=(0,l.useMemo)((()=>({active:y,selected:x,disabled:o})),[y,x,o]);return(0,h.sY)({ourProps:{id:m,ref:S,role:"option",tabIndex:!0===o?void 0:-1,"aria-disabled":!0===o||void 0,"aria-selected":!0===x||void 0,disabled:void 0,onClick:O,onFocus:I,onPointerMove:C,onMouseMove:C,onPointerLeave:w,onMouseLeave:w},theirProps:a,slot:D,defaultTag:"li",name:"Combobox.Option"})})),_=Object.assign(V,{Input:B,Button:Y,Label:$,Options:H,Option:U})},20266:function(e,t,n){n.d(t,{d:function(){return f},f:function(){return d}});var r=n(89526),o=n(95532),l=n(98943),i=n(15673),a=n(80786),u=n(86517);let s=(0,r.createContext)(null);function c(){let e=(0,r.useContext)(s);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,c),e}return e}function d(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)((()=>function(e){let n=(0,u.z)((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),o=(0,r.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props})),[n,e.slot,e.name,e.props]);return r.createElement(s.Provider,{value:o},e.children)}),[t])]}let f=(0,l.yV)((function(e,t){let n=c(),r=`headlessui-description-${(0,o.M)()}`,u=(0,a.T)(t);(0,i.e)((()=>n.register(r)),[r,n.register]);let s=e,d={ref:u,...n.props,id:r};return(0,l.sY)({ourProps:d,theirProps:s,slot:n.slot||{},defaultTag:"p",name:n.name||"Description"})}))},60724:function(e,t,n){n.d(t,{V:function(){return re}});var r=n(89526),o=n(73830),l=n(98943),i=n(80786),a=n(8247),u=n(64601),s=n(95532),c=n(73955),d=n(76762),f=n(25328),p=n(86517),v=n(48925),m=n(13014),b=n(43676),g=n(85806),h=n(12701);function y(e,t){let n=(0,r.useRef)([]),o=(0,p.z)(e);(0,r.useEffect)((()=>{for(let[e,r]of t.entries())if(n.current[e]!==r){let e=o(t);return n.current=t,e}}),[o,...t])}var x=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(x||{});let R=Object.assign((0,l.yV)((function(e,t){let n=(0,r.useRef)(null),a=(0,i.T)(n,t),{initialFocus:u,containers:s,features:x=30,...R}=e;(0,c.H)()||(x=1);let E=(0,b.i)(n);!function({ownerDocument:e},t){let n=(0,r.useRef)(null);(0,g.O)(null==e?void 0:e.defaultView,"focusout",(e=>{!t||n.current||(n.current=e.target)}),!0),y((()=>{t||((null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&(0,f.C5)(n.current),n.current=null)}),[t]);let o=(0,r.useRef)(!1);(0,r.useEffect)((()=>(o.current=!1,()=>{o.current=!0,(0,h.Y)((()=>{!o.current||((0,f.C5)(n.current),n.current=null)}))})),[])}({ownerDocument:E},Boolean(16&x));let S=function({ownerDocument:e,container:t,initialFocus:n},o){let l=(0,r.useRef)(null);return y((()=>{if(!o)return;let r=t.current;if(!r)return;let i=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===i)return void(l.current=i)}else if(r.contains(i))return void(l.current=i);null!=n&&n.current?(0,f.C5)(n.current):(0,f.jA)(r,f.TO.First)===f.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),l.current=null==e?void 0:e.activeElement}),[o]),l}({ownerDocument:E,container:n,initialFocus:u},Boolean(2&x));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let l=(0,m.t)();(0,g.O)(null==e?void 0:e.defaultView,"focus",(e=>{if(!o||!l.current)return;let i=new Set(null==n?void 0:n.current);i.add(t);let a=r.current;if(!a)return;let u=e.target;u&&u instanceof HTMLElement?function(e,t){var n;for(let r of e)if(null!=(n=r.current)&&n.contains(t))return!0;return!1}(i,u)?(r.current=u,(0,f.C5)(u)):(e.preventDefault(),e.stopPropagation(),(0,f.C5)(a)):(0,f.C5)(r.current)}),!0)}({ownerDocument:E,container:n,containers:s,previousActiveElement:S},Boolean(8&x));let P=(0,v.l)(),T=(0,p.z)((()=>{let e=n.current;!e||(0,o.E)(P.current,{[v.N.Forwards]:()=>(0,f.jA)(e,f.TO.First),[v.N.Backwards]:()=>(0,f.jA)(e,f.TO.Last)})})),O={ref:a};return r.createElement(r.Fragment,null,Boolean(4&x)&&r.createElement(d._,{as:"button",type:"button",onFocus:T,features:d.A.Focusable}),(0,l.sY)({ourProps:O,theirProps:R,defaultTag:"div",name:"FocusTrap"}),Boolean(4&x)&&r.createElement(d._,{as:"button",type:"button",onFocus:T,features:d.A.Focusable}))})),{features:x});var E=n(57725),S=n(15673);let P=new Set,T=new Map;function O(e){e.setAttribute("aria-hidden","true"),e.inert=!0}function I(e){let t=T.get(e);!t||(null===t["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",t["aria-hidden"]),e.inert=t.inert)}var C=n(73961);let w=(0,r.createContext)(!1);function D(){return(0,r.useContext)(w)}function k(e){return r.createElement(w.Provider,{value:e.force},e.children)}let M=r.Fragment,F=(0,l.yV)((function(e,t){let n=e,o=(0,r.useRef)(null),a=(0,i.T)((0,i.h)((e=>{o.current=e})),t),u=(0,b.i)(o),s=function(e){let t=D(),n=(0,r.useContext)(L),o=(0,b.i)(e),[l,i]=(0,r.useState)((()=>{if(!t&&null!==n||"undefined"==typeof window)return null;let e=null==o?void 0:o.getElementById("headlessui-portal-root");if(e)return e;if(null===o)return null;let r=o.createElement("div");return r.setAttribute("id","headlessui-portal-root"),o.body.appendChild(r)}));return(0,r.useEffect)((()=>{null!==l&&(null!=o&&o.body.contains(l)||null==o||o.body.appendChild(l))}),[l,o]),(0,r.useEffect)((()=>{t||null!==n&&i(n.current)}),[n,i,t]),l}(o),[d]=(0,r.useState)((()=>{var e;return"undefined"==typeof window?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null})),f=(0,c.H)(),p=(0,r.useRef)(!1);return(0,S.e)((()=>{if(p.current=!1,s&&d)return s.contains(d)||(d.setAttribute("data-headlessui-portal",""),s.appendChild(d)),()=>{p.current=!0,(0,h.Y)((()=>{var e;!p.current||!s||!d||(s.removeChild(d),s.childNodes.length<=0&&(null==(e=s.parentElement)||e.removeChild(s)))}))}}),[s,d]),f&&s&&d?(0,C.createPortal)((0,l.sY)({ourProps:{ref:a},theirProps:n,defaultTag:M,name:"Portal"}),d):null})),A=r.Fragment,L=(0,r.createContext)(null),N=(0,l.yV)((function(e,t){let{target:n,...o}=e,a={ref:(0,i.T)(t)};return r.createElement(L.Provider,{value:n},(0,l.sY)({ourProps:a,theirProps:o,defaultTag:A,name:"Popover.Group"}))})),z=Object.assign(F,{Group:N});var V=n(20266),B=n(74388);let Y=(0,r.createContext)((()=>{}));Y.displayName="StackContext";var $=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))($||{});function j({children:e,onUpdate:t,type:n,element:o}){let l=(0,r.useContext)(Y),i=(0,p.z)(((...e)=>{null==t||t(...e),l(...e)}));return(0,S.e)((()=>(i(0,n,o),()=>i(1,n,o))),[i,n,o]),r.createElement(Y.Provider,{value:i},e)}var H,U=n(42670),_=((H=_||{})[H.Open=0]="Open",H[H.Closed=1]="Closed",H),G=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(G||{});let Z={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},K=(0,r.createContext)(null);function Q(e){let t=(0,r.useContext)(K);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Q),t}return t}function q(e,t){return(0,o.E)(t.type,Z,e,t)}K.displayName="DialogContext";let J=l.AN.RenderStrategy|l.AN.Static,W=(0,l.yV)((function(e,t){let{open:n,onClose:u,initialFocus:f,__demoMode:v=!1,...m}=e,[h,y]=(0,r.useState)(0),x=(0,B.oJ)();void 0===n&&null!==x&&(n=(0,o.E)(x,{[B.ZM.Open]:!0,[B.ZM.Closed]:!1}));let C=(0,r.useRef)(new Set),w=(0,r.useRef)(null),D=(0,i.T)(w,t),M=(0,r.useRef)(null),F=(0,b.i)(w),A=e.hasOwnProperty("open")||null!==x,L=e.hasOwnProperty("onClose");if(!A&&!L)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!A)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!L)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof n)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${n}`);if("function"!=typeof u)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${u}`);let N=n?0:1,[Y,H]=(0,r.useReducer)(q,{titleId:null,descriptionId:null,panelRef:(0,r.createRef)()}),_=(0,p.z)((()=>u(!1))),G=(0,p.z)((e=>H({type:0,id:e}))),Z=!!(0,c.H)()&&(!v&&0===N),Q=h>1,W=null!==(0,r.useContext)(K),X=Q?"parent":"leaf";(function(e,t=!0){(0,S.e)((()=>{if(!t||!e.current)return;let n=e.current,r=(0,E.r)(n);if(r){P.add(n);for(let e of T.keys())e.contains(n)&&(I(e),T.delete(e));return r.querySelectorAll("body > *").forEach((e=>{if(e instanceof HTMLElement){for(let t of P)if(e.contains(t))return;1===P.size&&(T.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),O(e))}})),()=>{if(P.delete(n),P.size>0)r.querySelectorAll("body > *").forEach((e=>{if(e instanceof HTMLElement&&!T.has(e)){for(let t of P)if(e.contains(t))return;T.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),O(e)}}));else for(let e of T.keys())I(e),T.delete(e)}}}),[t])})(w,!!Q&&Z),(0,U.O)((()=>{var e,t;return[...Array.from(null!=(e=null==F?void 0:F.querySelectorAll("body > *, [data-headlessui-portal]"))?e:[]).filter((e=>!(!(e instanceof HTMLElement)||e.contains(M.current)||Y.panelRef.current&&e.contains(Y.panelRef.current)))),null!=(t=Y.panelRef.current)?t:w.current]}),_,Z&&!Q),(0,g.O)(null==F?void 0:F.defaultView,"keydown",(e=>{e.defaultPrevented||e.key===a.R.Escape&&0===N&&(Q||(e.preventDefault(),e.stopPropagation(),_()))})),(0,r.useEffect)((()=>{var e;if(0!==N||W)return;let t=(0,E.r)(w);if(!t)return;let n=t.documentElement,r=null!=(e=t.defaultView)?e:window,o=n.style.overflow,l=n.style.paddingRight,i=r.innerWidth-n.clientWidth;if(n.style.overflow="hidden",i>0){let e=i-(n.clientWidth-n.offsetWidth);n.style.paddingRight=`${e}px`}return()=>{n.style.overflow=o,n.style.paddingRight=l}}),[N,W]),(0,r.useEffect)((()=>{if(0!==N||!w.current)return;let e=new IntersectionObserver((e=>{for(let t of e)0===t.boundingClientRect.x&&0===t.boundingClientRect.y&&0===t.boundingClientRect.width&&0===t.boundingClientRect.height&&_()}));return e.observe(w.current),()=>e.disconnect()}),[N,w,_]);let[ee,te]=(0,V.f)(),ne=`headlessui-dialog-${(0,s.M)()}`,re=(0,r.useMemo)((()=>[{dialogState:N,close:_,setTitleId:G},Y]),[N,Y,_,G]),oe=(0,r.useMemo)((()=>({open:0===N})),[N]),le={ref:D,id:ne,role:"dialog","aria-modal":0===N||void 0,"aria-labelledby":Y.titleId,"aria-describedby":ee};return r.createElement(j,{type:"Dialog",element:w,onUpdate:(0,p.z)(((e,t,n)=>{"Dialog"===t&&(0,o.E)(e,{[$.Add](){C.current.add(n),y((e=>e+1))},[$.Remove](){C.current.add(n),y((e=>e-1))}})}))},r.createElement(k,{force:!0},r.createElement(z,null,r.createElement(K.Provider,{value:re},r.createElement(z.Group,{target:w},r.createElement(k,{force:!1},r.createElement(te,{slot:oe,name:"Dialog.Description"},r.createElement(R,{initialFocus:f,containers:C,features:Z?(0,o.E)(X,{parent:R.features.RestoreFocus,leaf:R.features.All&~R.features.FocusLock}):R.features.None},(0,l.sY)({ourProps:le,theirProps:m,slot:oe,defaultTag:"div",features:J,visible:0===N,name:"Dialog"})))))))),r.createElement(d._,{features:d.A.Hidden,ref:M}))})),X=(0,l.yV)((function(e,t){let[{dialogState:n,close:o}]=Q("Dialog.Overlay"),a=(0,i.T)(t),c=`headlessui-dialog-overlay-${(0,s.M)()}`,d=(0,p.z)((e=>{if(e.target===e.currentTarget){if((0,u.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),o()}})),f=(0,r.useMemo)((()=>({open:0===n})),[n]);return(0,l.sY)({ourProps:{ref:a,id:c,"aria-hidden":!0,onClick:d},theirProps:e,slot:f,defaultTag:"div",name:"Dialog.Overlay"})})),ee=(0,l.yV)((function(e,t){let[{dialogState:n},o]=Q("Dialog.Backdrop"),a=(0,i.T)(t),u=`headlessui-dialog-backdrop-${(0,s.M)()}`;(0,r.useEffect)((()=>{if(null===o.panelRef.current)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")}),[o.panelRef]);let c=(0,r.useMemo)((()=>({open:0===n})),[n]);return r.createElement(k,{force:!0},r.createElement(z,null,(0,l.sY)({ourProps:{ref:a,id:u,"aria-hidden":!0},theirProps:e,slot:c,defaultTag:"div",name:"Dialog.Backdrop"})))})),te=(0,l.yV)((function(e,t){let[{dialogState:n},o]=Q("Dialog.Panel"),a=(0,i.T)(t,o.panelRef),u=`headlessui-dialog-panel-${(0,s.M)()}`,c=(0,r.useMemo)((()=>({open:0===n})),[n]),d=(0,p.z)((e=>{e.stopPropagation()}));return(0,l.sY)({ourProps:{ref:a,id:u,onClick:d},theirProps:e,slot:c,defaultTag:"div",name:"Dialog.Panel"})})),ne=(0,l.yV)((function(e,t){let[{dialogState:n,setTitleId:o}]=Q("Dialog.Title"),a=`headlessui-dialog-title-${(0,s.M)()}`,u=(0,i.T)(t);(0,r.useEffect)((()=>(o(a),()=>o(null))),[a,o]);let c=(0,r.useMemo)((()=>({open:0===n})),[n]);return(0,l.sY)({ourProps:{ref:u,id:a},theirProps:e,slot:c,defaultTag:"h2",name:"Dialog.Title"})})),re=Object.assign(W,{Backdrop:ee,Panel:te,Overlay:X,Title:ne,Description:V.d})},90926:function(e,t,n){n.d(t,{p:function(){return D}});var r,o,l=n(89526),i=n(73830),a=n(98943),u=n(80786),s=n(95532),c=n(8247),d=n(64601),f=n(74388),p=n(49114),v=n(57725),m=n(86517),b=((o=b||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),g=((r=g||{})[r.ToggleDisclosure=0]="ToggleDisclosure",r[r.CloseDisclosure=1]="CloseDisclosure",r[r.SetButtonId=2]="SetButtonId",r[r.SetPanelId=3]="SetPanelId",r[r.LinkPanel=4]="LinkPanel",r[r.UnlinkPanel=5]="UnlinkPanel",r);let h={0:e=>({...e,disclosureState:(0,i.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},y=(0,l.createContext)(null);function x(e){let t=(0,l.useContext)(y);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,x),t}return t}y.displayName="DisclosureContext";let R=(0,l.createContext)(null);function E(e){let t=(0,l.useContext)(R);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,E),t}return t}R.displayName="DisclosureAPIContext";let S=(0,l.createContext)(null);function P(e,t){return(0,i.E)(t.type,h,e,t)}S.displayName="DisclosurePanelContext";let T=l.Fragment,O=(0,a.yV)((function(e,t){let{defaultOpen:n=!1,...r}=e,o=`headlessui-disclosure-button-${(0,s.M)()}`,c=`headlessui-disclosure-panel-${(0,s.M)()}`,d=(0,l.useRef)(null),p=(0,u.T)(t,(0,u.h)((e=>{d.current=e}),void 0===e.as||e.as===l.Fragment)),b=(0,l.useRef)(null),g=(0,l.useRef)(null),h=(0,l.useReducer)(P,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:g,panelRef:b,buttonId:o,panelId:c}),[{disclosureState:x},E]=h;(0,l.useEffect)((()=>E({type:2,buttonId:o})),[o,E]),(0,l.useEffect)((()=>E({type:3,panelId:c})),[c,E]);let S=(0,m.z)((e=>{E({type:1});let t=(0,v.r)(d);if(!t)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(o):t.getElementById(o);null==n||n.focus()})),O=(0,l.useMemo)((()=>({close:S})),[S]),I=(0,l.useMemo)((()=>({open:0===x,close:S})),[x,S]),C={ref:p};return l.createElement(y.Provider,{value:h},l.createElement(R.Provider,{value:O},l.createElement(f.up,{value:(0,i.E)(x,{0:f.ZM.Open,1:f.ZM.Closed})},(0,a.sY)({ourProps:C,theirProps:r,slot:I,defaultTag:T,name:"Disclosure"}))))})),I=(0,a.yV)((function(e,t){let[n,r]=x("Disclosure.Button"),o=(0,l.useContext)(S),i=null!==o&&o===n.panelId,s=(0,l.useRef)(null),f=(0,u.T)(s,t,i?null:n.buttonRef),v=(0,m.z)((e=>{var t;if(i){if(1===n.disclosureState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),r({type:0}),null==(t=n.buttonRef.current)||t.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),r({type:0})}})),b=(0,m.z)((e=>{if(e.key===c.R.Space)e.preventDefault()})),g=(0,m.z)((t=>{var o;(0,d.P)(t.currentTarget)||e.disabled||(i?(r({type:0}),null==(o=n.buttonRef.current)||o.focus()):r({type:0}))})),h=(0,l.useMemo)((()=>({open:0===n.disclosureState})),[n]),y=(0,p.f)(e,s),R=e,E=i?{ref:f,type:y,onKeyDown:v,onClick:g}:{ref:f,id:n.buttonId,type:y,"aria-expanded":e.disabled?void 0:0===n.disclosureState,"aria-controls":n.linkedPanel?n.panelId:void 0,onKeyDown:v,onKeyUp:b,onClick:g};return(0,a.sY)({ourProps:E,theirProps:R,slot:h,defaultTag:"button",name:"Disclosure.Button"})})),C=a.AN.RenderStrategy|a.AN.Static,w=(0,a.yV)((function(e,t){let[n,r]=x("Disclosure.Panel"),{close:o}=E("Disclosure.Panel"),i=(0,u.T)(t,n.panelRef,(()=>{n.linkedPanel||r({type:4})})),s=(0,f.oJ)(),c=null!==s?s===f.ZM.Open:0===n.disclosureState;(0,l.useEffect)((()=>()=>r({type:5})),[r]),(0,l.useEffect)((()=>{var t;1===n.disclosureState&&(null==(t=e.unmount)||t)&&r({type:5})}),[n.disclosureState,e.unmount,r]);let d=(0,l.useMemo)((()=>({open:0===n.disclosureState,close:o})),[n,o]),p=e,v={ref:i,id:n.panelId};return l.createElement(S.Provider,{value:n.panelId},(0,a.sY)({ourProps:v,theirProps:p,slot:d,defaultTag:"div",features:C,visible:c,name:"Disclosure.Panel"}))})),D=Object.assign(O,{Button:I,Panel:w})},8247:function(e,t,n){n.d(t,{R:function(){return o}});var r,o=((r=o||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r)},88698:function(e,t,n){n.d(t,{R:function(){return $}});var r,o,l=n(89526),i=n(35760),a=n(95532),u=n(15673),s=n(1480),c=n(80786),d=n(98943),f=n(73830),p=n(78320),v=n(8247),m=n(15815),b=n(64601),g=n(25328),h=n(74388),y=n(49114),x=n(42670),R=n(76762),E=n(92646),S=n(57725),P=n(86517),T=((o=T||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),O=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(O||{}),I=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(I||{}),C=((r=C||{})[r.OpenListbox=0]="OpenListbox",r[r.CloseListbox=1]="CloseListbox",r[r.SetDisabled=2]="SetDisabled",r[r.SetOrientation=3]="SetOrientation",r[r.GoToOption=4]="GoToOption",r[r.Search=5]="Search",r[r.ClearSearch=6]="ClearSearch",r[r.RegisterOption=7]="RegisterOption",r[r.UnregisterOption=8]="UnregisterOption",r);function w(e,t=e=>e){let n=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,r=(0,g.z2)(t(e.options.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{options:r,activeOptionIndex:o}}let D={1:e=>e.disabled||1===e.listboxState?e:{...e,activeOptionIndex:null,listboxState:1},0(e){if(e.disabled||0===e.listboxState)return e;let t=e.activeOptionIndex,{value:n,mode:r,compare:o}=e.propsRef.current,l=e.options.findIndex((e=>{let t=e.dataRef.current.value;return(0,f.E)(r,{1:()=>n.some((e=>o(e,t))),0:()=>o(n,t)})}));return-1!==l&&(t=l),{...e,listboxState:0,activeOptionIndex:t}},2:(e,t)=>e.disabled===t.disabled?e:{...e,disabled:t.disabled},3:(e,t)=>e.orientation===t.orientation?e:{...e,orientation:t.orientation},4(e,t){var n;if(e.disabled||1===e.listboxState)return e;let r=w(e),o=(0,m.d)(t,{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeOptionIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},5:(e,t)=>{if(e.disabled||1===e.listboxState)return e;let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeOptionIndex?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find((e=>{var t;return!e.dataRef.current.disabled&&(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))})),l=o?e.options.indexOf(o):-1;return-1===l||l===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:l,activationTrigger:1}},6:e=>e.disabled||1===e.listboxState||""===e.searchQuery?e:{...e,searchQuery:""},7:(e,t)=>{let n={id:t.id,dataRef:t.dataRef},r=w(e,(e=>[...e,n]));if(null===e.activeOptionIndex){let{value:o,mode:l,compare:i}=e.propsRef.current,a=t.dataRef.current.value;(0,f.E)(l,{1:()=>o.some((e=>i(e,a))),0:()=>i(o,a)})&&(r.activeOptionIndex=r.options.indexOf(n))}return{...e,...r}},8:(e,t)=>{let n=w(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}}},k=(0,l.createContext)(null);function M(e){let t=(0,l.useContext)(k);if(null===t){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}function F(e,t){return(0,f.E)(t.type,D,e,t)}k.displayName="ListboxContext";let A=l.Fragment,L=(0,d.yV)((function(e,t){let{value:n,name:r,onChange:o,disabled:i=!1,horizontal:a=!1,multiple:s=!1,...p}=e;const v=a?"horizontal":"vertical";let m=(0,c.T)(t),b=(0,l.useReducer)(F,{listboxState:1,propsRef:{current:{value:n,onChange:o,mode:s?1:0,compare:(0,P.z)(((e,t)=>e===t))}},labelRef:(0,l.createRef)(),buttonRef:(0,l.createRef)(),optionsRef:(0,l.createRef)(),disabled:i,orientation:v,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1}),[{listboxState:y,propsRef:S,optionsRef:T,buttonRef:O},I]=b;S.current.value=n,S.current.mode=s?1:0,(0,u.e)((()=>{S.current.onChange=e=>(0,f.E)(S.current.mode,{0:()=>o(e),1(){let t=S.current.value.slice(),n=t.indexOf(e);return-1===n?t.push(e):t.splice(n,1),o(t)}})}),[o,S]),(0,u.e)((()=>I({type:2,disabled:i})),[i]),(0,u.e)((()=>I({type:3,orientation:v})),[v]),(0,x.O)([O,T],((e,t)=>{var n;I({type:1}),(0,g.sP)(t,g.tJ.Loose)||(e.preventDefault(),null==(n=O.current)||n.focus())}),0===y);let C=(0,l.useMemo)((()=>({open:0===y,disabled:i})),[y,i]),w={ref:m};return l.createElement(k.Provider,{value:b},l.createElement(h.up,{value:(0,f.E)(y,{0:h.ZM.Open,1:h.ZM.Closed})},null!=r&&null!=n&&(0,E.t)({[r]:n}).map((([e,t])=>l.createElement(R._,{features:R.A.Hidden,...(0,d.oA)({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:e,value:t})}))),(0,d.sY)({ourProps:w,theirProps:p,slot:C,defaultTag:A,name:"Listbox"})))})),N=(0,d.yV)((function(e,t){var n;let[r,o]=M("Listbox.Button"),u=(0,c.T)(r.buttonRef,t),f=`headlessui-listbox-button-${(0,a.M)()}`,p=(0,i.G)(),g=(0,P.z)((e=>{switch(e.key){case v.R.Space:case v.R.Enter:case v.R.ArrowDown:e.preventDefault(),o({type:0}),p.nextFrame((()=>{r.propsRef.current.value||o({type:4,focus:m.T.First})}));break;case v.R.ArrowUp:e.preventDefault(),o({type:0}),p.nextFrame((()=>{r.propsRef.current.value||o({type:4,focus:m.T.Last})}))}})),h=(0,P.z)((e=>{if(e.key===v.R.Space)e.preventDefault()})),x=(0,P.z)((e=>{if((0,b.P)(e.currentTarget))return e.preventDefault();0===r.listboxState?(o({type:1}),p.nextFrame((()=>{var e;return null==(e=r.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(e.preventDefault(),o({type:0}))})),R=(0,s.v)((()=>{if(r.labelRef.current)return[r.labelRef.current.id,f].join(" ")}),[r.labelRef.current,f]),E=(0,l.useMemo)((()=>({open:0===r.listboxState,disabled:r.disabled})),[r]),S=e,T={ref:u,id:f,type:(0,y.f)(e,r.buttonRef),"aria-haspopup":!0,"aria-controls":null==(n=r.optionsRef.current)?void 0:n.id,"aria-expanded":r.disabled?void 0:0===r.listboxState,"aria-labelledby":R,disabled:r.disabled,onKeyDown:g,onKeyUp:h,onClick:x};return(0,d.sY)({ourProps:T,theirProps:S,slot:E,defaultTag:"button",name:"Listbox.Button"})})),z=(0,d.yV)((function(e,t){let[n]=M("Listbox.Label"),r=`headlessui-listbox-label-${(0,a.M)()}`,o=(0,c.T)(n.labelRef,t),i=(0,P.z)((()=>{var e;return null==(e=n.buttonRef.current)?void 0:e.focus({preventScroll:!0})})),u=(0,l.useMemo)((()=>({open:0===n.listboxState,disabled:n.disabled})),[n]);return(0,d.sY)({ourProps:{ref:o,id:r,onClick:i},theirProps:e,slot:u,defaultTag:"label",name:"Listbox.Label"})})),V=d.AN.RenderStrategy|d.AN.Static,B=(0,d.yV)((function(e,t){var n;let[r,o]=M("Listbox.Options"),u=(0,c.T)(r.optionsRef,t),b=`headlessui-listbox-options-${(0,a.M)()}`,g=(0,i.G)(),y=(0,i.G)(),x=(0,h.oJ)(),R=null!==x?x===h.ZM.Open:0===r.listboxState;(0,l.useEffect)((()=>{var e;let t=r.optionsRef.current;!t||0===r.listboxState&&t!==(null==(e=(0,S.r)(t))?void 0:e.activeElement)&&t.focus({preventScroll:!0})}),[r.listboxState,r.optionsRef]);let E=(0,P.z)((e=>{switch(y.dispose(),e.key){case v.R.Space:if(""!==r.searchQuery)return e.preventDefault(),e.stopPropagation(),o({type:5,value:e.key});case v.R.Enter:if(e.preventDefault(),e.stopPropagation(),null!==r.activeOptionIndex){let{dataRef:e}=r.options[r.activeOptionIndex];r.propsRef.current.onChange(e.current.value)}0===r.propsRef.current.mode&&(o({type:1}),(0,p.k)().nextFrame((()=>{var e;return null==(e=r.buttonRef.current)?void 0:e.focus({preventScroll:!0})})));break;case(0,f.E)(r.orientation,{vertical:v.R.ArrowDown,horizontal:v.R.ArrowRight}):return e.preventDefault(),e.stopPropagation(),o({type:4,focus:m.T.Next});case(0,f.E)(r.orientation,{vertical:v.R.ArrowUp,horizontal:v.R.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),o({type:4,focus:m.T.Previous});case v.R.Home:case v.R.PageUp:return e.preventDefault(),e.stopPropagation(),o({type:4,focus:m.T.First});case v.R.End:case v.R.PageDown:return e.preventDefault(),e.stopPropagation(),o({type:4,focus:m.T.Last});case v.R.Escape:return e.preventDefault(),e.stopPropagation(),o({type:1}),g.nextFrame((()=>{var e;return null==(e=r.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));case v.R.Tab:e.preventDefault(),e.stopPropagation();break;default:1===e.key.length&&(o({type:5,value:e.key}),y.setTimeout((()=>o({type:6})),350))}})),T=(0,s.v)((()=>{var e,t,n;return null!=(n=null==(e=r.labelRef.current)?void 0:e.id)?n:null==(t=r.buttonRef.current)?void 0:t.id}),[r.labelRef.current,r.buttonRef.current]),O=(0,l.useMemo)((()=>({open:0===r.listboxState})),[r]),I=e,C={"aria-activedescendant":null===r.activeOptionIndex||null==(n=r.options[r.activeOptionIndex])?void 0:n.id,"aria-multiselectable":1===r.propsRef.current.mode||void 0,"aria-labelledby":T,"aria-orientation":r.orientation,id:b,onKeyDown:E,role:"listbox",tabIndex:0,ref:u};return(0,d.sY)({ourProps:C,theirProps:I,slot:O,defaultTag:"ul",features:V,visible:R,name:"Listbox.Options"})})),Y=(0,d.yV)((function(e,t){let{disabled:n=!1,value:r,...o}=e,[i,s]=M("Listbox.Option"),v=`headlessui-listbox-option-${(0,a.M)()}`,b=null!==i.activeOptionIndex&&i.options[i.activeOptionIndex].id===v,{value:g,compare:h}=i.propsRef.current,y=(0,f.E)(i.propsRef.current.mode,{1:()=>g.some((e=>h(e,r))),0:()=>h(g,r)}),x=(0,l.useRef)(null),R=(0,c.T)(t,x);(0,u.e)((()=>{if(0!==i.listboxState||!b||0===i.activationTrigger)return;let e=(0,p.k)();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=x.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[x,b,i.listboxState,i.activationTrigger,i.activeOptionIndex]);let E=(0,l.useRef)({disabled:n,value:r,domRef:x});(0,u.e)((()=>{E.current.disabled=n}),[E,n]),(0,u.e)((()=>{E.current.value=r}),[E,r]),(0,u.e)((()=>{var e,t;E.current.textValue=null==(t=null==(e=x.current)?void 0:e.textContent)?void 0:t.toLowerCase()}),[E,x]);let S=(0,P.z)((()=>i.propsRef.current.onChange(r)));(0,u.e)((()=>(s({type:7,id:v,dataRef:E}),()=>s({type:8,id:v}))),[E,v]);let T=(0,P.z)((e=>{if(n)return e.preventDefault();S(),0===i.propsRef.current.mode&&(s({type:1}),(0,p.k)().nextFrame((()=>{var e;return null==(e=i.buttonRef.current)?void 0:e.focus({preventScroll:!0})})))})),O=(0,P.z)((()=>{if(n)return s({type:4,focus:m.T.Nothing});s({type:4,focus:m.T.Specific,id:v})})),I=(0,P.z)((()=>{n||b||s({type:4,focus:m.T.Specific,id:v,trigger:0})})),C=(0,P.z)((()=>{n||!b||s({type:4,focus:m.T.Nothing})})),w=(0,l.useMemo)((()=>({active:b,selected:y,disabled:n})),[b,y,n]);return(0,d.sY)({ourProps:{id:v,ref:R,role:"option",tabIndex:!0===n?void 0:-1,"aria-disabled":!0===n||void 0,"aria-selected":!0===y||void 0,disabled:void 0,onClick:T,onFocus:O,onPointerMove:I,onMouseMove:I,onPointerLeave:C,onMouseLeave:C},theirProps:o,slot:w,defaultTag:"li",name:"Listbox.Option"})})),$=Object.assign(L,{Button:N,Label:z,Options:B,Option:Y})},30422:function(e,t,n){n.d(t,{v:function(){return V}});var r,o,l=n(89526),i=n(73830),a=n(98943),u=n(78320),s=n(35760),c=n(15673),d=n(80786),f=n(95532),p=n(8247),v=n(15815),m=n(64601),b=n(25328),g=n(42670),h=n(32602),y=n(74388),x=n(49114),R=n(43676),E=n(86517),S=((o=S||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),P=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(P||{}),T=((r=T||{})[r.OpenMenu=0]="OpenMenu",r[r.CloseMenu=1]="CloseMenu",r[r.GoToItem=2]="GoToItem",r[r.Search=3]="Search",r[r.ClearSearch=4]="ClearSearch",r[r.RegisterItem=5]="RegisterItem",r[r.UnregisterItem=6]="UnregisterItem",r);function O(e,t=e=>e){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=(0,b.z2)(t(e.items.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{items:r,activeItemIndex:o}}let I={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,menuState:0},2:(e,t)=>{var n;let r=O(e),o=(0,v.d)(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find((e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled})),l=o?e.items.indexOf(o):-1;return-1===l||l===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:l,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let n=O(e,(e=>[...e,{id:t.id,dataRef:t.dataRef}]));return{...e,...n}},6:(e,t)=>{let n=O(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}}},C=(0,l.createContext)(null);function w(e){let t=(0,l.useContext)(C);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}function D(e,t){return(0,i.E)(t.type,I,e,t)}C.displayName="MenuContext";let k=l.Fragment,M=(0,a.yV)((function(e,t){let n=(0,l.useReducer)(D,{menuState:1,buttonRef:(0,l.createRef)(),itemsRef:(0,l.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:r,itemsRef:o,buttonRef:u},s]=n,c=(0,d.T)(t);(0,g.O)([u,o],((e,t)=>{var n;s({type:1}),(0,b.sP)(t,b.tJ.Loose)||(e.preventDefault(),null==(n=u.current)||n.focus())}),0===r);let f=(0,l.useMemo)((()=>({open:0===r})),[r]),p=e,v={ref:c};return l.createElement(C.Provider,{value:n},l.createElement(y.up,{value:(0,i.E)(r,{0:y.ZM.Open,1:y.ZM.Closed})},(0,a.sY)({ourProps:v,theirProps:p,slot:f,defaultTag:k,name:"Menu"})))})),F=(0,a.yV)((function(e,t){var n;let[r,o]=w("Menu.Button"),i=(0,d.T)(r.buttonRef,t),u=`headlessui-menu-button-${(0,f.M)()}`,c=(0,s.G)(),b=(0,E.z)((e=>{switch(e.key){case p.R.Space:case p.R.Enter:case p.R.ArrowDown:e.preventDefault(),e.stopPropagation(),o({type:0}),c.nextFrame((()=>o({type:2,focus:v.T.First})));break;case p.R.ArrowUp:e.preventDefault(),e.stopPropagation(),o({type:0}),c.nextFrame((()=>o({type:2,focus:v.T.Last})))}})),g=(0,E.z)((e=>{if(e.key===p.R.Space)e.preventDefault()})),h=(0,E.z)((t=>{if((0,m.P)(t.currentTarget))return t.preventDefault();e.disabled||(0===r.menuState?(o({type:1}),c.nextFrame((()=>{var e;return null==(e=r.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(t.preventDefault(),o({type:0})))})),y=(0,l.useMemo)((()=>({open:0===r.menuState})),[r]),R=e,S={ref:i,id:u,type:(0,x.f)(e,r.buttonRef),"aria-haspopup":!0,"aria-controls":null==(n=r.itemsRef.current)?void 0:n.id,"aria-expanded":e.disabled?void 0:0===r.menuState,onKeyDown:b,onKeyUp:g,onClick:h};return(0,a.sY)({ourProps:S,theirProps:R,slot:y,defaultTag:"button",name:"Menu.Button"})})),A=a.AN.RenderStrategy|a.AN.Static,L=(0,a.yV)((function(e,t){var n,r;let[o,i]=w("Menu.Items"),c=(0,d.T)(o.itemsRef,t),m=(0,R.i)(o.itemsRef),b=`headlessui-menu-items-${(0,f.M)()}`,g=(0,s.G)(),x=(0,y.oJ)(),S=null!==x?x===y.ZM.Open:0===o.menuState;(0,l.useEffect)((()=>{let e=o.itemsRef.current;!e||0===o.menuState&&e!==(null==m?void 0:m.activeElement)&&e.focus({preventScroll:!0})}),[o.menuState,o.itemsRef,m]),(0,h.B)({container:o.itemsRef.current,enabled:0===o.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let P=(0,E.z)((e=>{var t,n;switch(g.dispose(),e.key){case p.R.Space:if(""!==o.searchQuery)return e.preventDefault(),e.stopPropagation(),i({type:3,value:e.key});case p.R.Enter:if(e.preventDefault(),e.stopPropagation(),i({type:1}),null!==o.activeItemIndex){let{dataRef:e}=o.items[o.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}(0,u.k)().nextFrame((()=>{var e;return null==(e=o.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));break;case p.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),i({type:2,focus:v.T.Next});case p.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),i({type:2,focus:v.T.Previous});case p.R.Home:case p.R.PageUp:return e.preventDefault(),e.stopPropagation(),i({type:2,focus:v.T.First});case p.R.End:case p.R.PageDown:return e.preventDefault(),e.stopPropagation(),i({type:2,focus:v.T.Last});case p.R.Escape:e.preventDefault(),e.stopPropagation(),i({type:1}),(0,u.k)().nextFrame((()=>{var e;return null==(e=o.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));break;case p.R.Tab:e.preventDefault(),e.stopPropagation();break;default:1===e.key.length&&(i({type:3,value:e.key}),g.setTimeout((()=>i({type:4})),350))}})),T=(0,E.z)((e=>{if(e.key===p.R.Space)e.preventDefault()})),O=(0,l.useMemo)((()=>({open:0===o.menuState})),[o]),I=e,C={"aria-activedescendant":null===o.activeItemIndex||null==(n=o.items[o.activeItemIndex])?void 0:n.id,"aria-labelledby":null==(r=o.buttonRef.current)?void 0:r.id,id:b,onKeyDown:P,onKeyUp:T,role:"menu",tabIndex:0,ref:c};return(0,a.sY)({ourProps:C,theirProps:I,slot:O,defaultTag:"div",features:A,visible:S,name:"Menu.Items"})})),N=l.Fragment,z=(0,a.yV)((function(e,t){let{disabled:n=!1,...r}=e,[o,i]=w("Menu.Item"),s=`headlessui-menu-item-${(0,f.M)()}`,p=null!==o.activeItemIndex&&o.items[o.activeItemIndex].id===s,m=(0,l.useRef)(null),b=(0,d.T)(t,m);(0,c.e)((()=>{if(0!==o.menuState||!p||0===o.activationTrigger)return;let e=(0,u.k)();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=m.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[m,p,o.menuState,o.activationTrigger,o.activeItemIndex]);let g=(0,l.useRef)({disabled:n,domRef:m});(0,c.e)((()=>{g.current.disabled=n}),[g,n]),(0,c.e)((()=>{var e,t;g.current.textValue=null==(t=null==(e=m.current)?void 0:e.textContent)?void 0:t.toLowerCase()}),[g,m]),(0,c.e)((()=>(i({type:5,id:s,dataRef:g}),()=>i({type:6,id:s}))),[g,s]);let h=(0,E.z)((e=>{if(n)return e.preventDefault();i({type:1}),(0,u.k)().nextFrame((()=>{var e;return null==(e=o.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))})),y=(0,E.z)((()=>{if(n)return i({type:2,focus:v.T.Nothing});i({type:2,focus:v.T.Specific,id:s})})),x=(0,E.z)((()=>{n||p||i({type:2,focus:v.T.Specific,id:s,trigger:0})})),R=(0,E.z)((()=>{n||!p||i({type:2,focus:v.T.Nothing})})),S=(0,l.useMemo)((()=>({active:p,disabled:n})),[p,n]);return(0,a.sY)({ourProps:{id:s,ref:b,role:"menuitem",tabIndex:!0===n?void 0:-1,"aria-disabled":!0===n||void 0,disabled:void 0,onClick:h,onFocus:y,onPointerMove:x,onMouseMove:x,onPointerLeave:R,onMouseLeave:R},theirProps:r,slot:S,defaultTag:N,name:"Menu.Item"})})),V=Object.assign(M,{Button:F,Items:L,Item:z})},72517:function(e,t,n){n.d(t,{J:function(){return Y}});var r,o,l=n(89526),i=n(73830),a=n(98943),u=n(80786),s=n(95532),c=n(8247),d=n(64601),f=n(25328),p=n(74388),v=n(49114),m=n(42670),b=n(57725),g=n(43676),h=n(85806),y=n(76762),x=n(86517),R=n(48925),E=((o=E||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),S=((r=S||{})[r.TogglePopover=0]="TogglePopover",r[r.ClosePopover=1]="ClosePopover",r[r.SetButton=2]="SetButton",r[r.SetButtonId=3]="SetButtonId",r[r.SetPanel=4]="SetPanel",r[r.SetPanelId=5]="SetPanelId",r);let P={0:e=>({...e,popoverState:(0,i.E)(e.popoverState,{0:1,1:0})}),1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},T=(0,l.createContext)(null);function O(e){let t=(0,l.useContext)(T);if(null===t){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,O),t}return t}T.displayName="PopoverContext";let I=(0,l.createContext)(null);function C(e){let t=(0,l.useContext)(I);if(null===t){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,C),t}return t}I.displayName="PopoverAPIContext";let w=(0,l.createContext)(null);function D(){return(0,l.useContext)(w)}w.displayName="PopoverGroupContext";let k=(0,l.createContext)(null);function M(e,t){return(0,i.E)(t.type,P,e,t)}k.displayName="PopoverPanelContext";let F=(0,a.yV)((function(e,t){var n;let r=`headlessui-popover-button-${(0,s.M)()}`,o=`headlessui-popover-panel-${(0,s.M)()}`,c=(0,l.useRef)(null),d=(0,u.T)(t,(0,u.h)((e=>{c.current=e}))),v=(0,l.useReducer)(M,{popoverState:1,button:null,buttonId:r,panel:null,panelId:o,beforePanelSentinel:(0,l.createRef)(),afterPanelSentinel:(0,l.createRef)()}),[{popoverState:b,button:y,panel:R,beforePanelSentinel:E,afterPanelSentinel:S},P]=v,O=(0,g.i)(null!=(n=c.current)?n:y);(0,l.useEffect)((()=>P({type:3,buttonId:r})),[r,P]),(0,l.useEffect)((()=>P({type:5,panelId:o})),[o,P]);let C=(0,l.useMemo)((()=>{if(!y||!R)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(y))^Number(null==e?void 0:e.contains(R)))return!0;return!1}),[y,R]),w=(0,l.useMemo)((()=>({buttonId:r,panelId:o,close:()=>P({type:1})})),[r,o,P]),k=D(),F=null==k?void 0:k.registerPopover,A=(0,x.z)((()=>{var e;return null!=(e=null==k?void 0:k.isFocusWithinPopoverGroup())?e:(null==O?void 0:O.activeElement)&&((null==y?void 0:y.contains(O.activeElement))||(null==R?void 0:R.contains(O.activeElement)))}));(0,l.useEffect)((()=>null==F?void 0:F(w)),[F,w]),(0,h.O)(null==O?void 0:O.defaultView,"focus",(e=>{var t,n,r,o;0===b&&(A()||!y||!R||null!=(n=null==(t=E.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(o=null==(r=S.current)?void 0:r.contains)&&o.call(r,e.target)||P({type:1}))}),!0),(0,m.O)([y,R],((e,t)=>{P({type:1}),(0,f.sP)(t,f.tJ.Loose)||(e.preventDefault(),null==y||y.focus())}),0===b);let L=(0,x.z)((e=>{P({type:1});let t=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:y:y;null==t||t.focus()})),N=(0,l.useMemo)((()=>({close:L,isPortalled:C})),[L,C]),z=(0,l.useMemo)((()=>({open:0===b,close:L})),[b,L]),V=e,B={ref:d};return l.createElement(T.Provider,{value:v},l.createElement(I.Provider,{value:N},l.createElement(p.up,{value:(0,i.E)(b,{0:p.ZM.Open,1:p.ZM.Closed})},(0,a.sY)({ourProps:B,theirProps:V,slot:z,defaultTag:"div",name:"Popover"}))))})),A=(0,a.yV)((function(e,t){let[n,r]=O("Popover.Button"),{isPortalled:o}=C("Popover.Button"),p=(0,l.useRef)(null),m=`headlessui-focus-sentinel-${(0,s.M)()}`,b=D(),h=null==b?void 0:b.closeOthers,E=(0,l.useContext)(k),S=null!==E&&E===n.panelId,P=(0,u.T)(p,t,S?null:e=>r({type:2,button:e})),T=(0,u.T)(p,t),I=(0,g.i)(p),w=(0,x.z)((e=>{var t,o,l;if(S){if(1===n.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(o=(t=e.target).click)||o.call(t),r({type:1}),null==(l=n.button)||l.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===n.popoverState&&(null==h||h(n.buttonId)),r({type:0});break;case c.R.Escape:if(0!==n.popoverState)return null==h?void 0:h(n.buttonId);if(!p.current||(null==I?void 0:I.activeElement)&&!p.current.contains(I.activeElement))return;e.preventDefault(),e.stopPropagation(),r({type:1})}})),M=(0,x.z)((e=>{S||e.key===c.R.Space&&e.preventDefault()})),F=(0,x.z)((t=>{var o,l;(0,d.P)(t.currentTarget)||e.disabled||(S?(r({type:1}),null==(o=n.button)||o.focus()):(t.preventDefault(),t.stopPropagation(),1===n.popoverState&&(null==h||h(n.buttonId)),r({type:0}),null==(l=n.button)||l.focus()))})),A=(0,x.z)((e=>{e.preventDefault(),e.stopPropagation()})),L=0===n.popoverState,N=(0,l.useMemo)((()=>({open:L})),[L]),z=(0,v.f)(e,p),V=e,B=S?{ref:T,type:z,onKeyDown:w,onClick:F}:{ref:P,id:n.buttonId,type:z,"aria-expanded":e.disabled?void 0:0===n.popoverState,"aria-controls":n.panel?n.panelId:void 0,onKeyDown:w,onKeyUp:M,onClick:F,onMouseDown:A},Y=(0,R.l)(),$=(0,x.z)((()=>{let e=n.panel;e&&(0,i.E)(Y.current,{[R.N.Forwards]:()=>(0,f.jA)(e,f.TO.First),[R.N.Backwards]:()=>(0,f.jA)(e,f.TO.Last)})}));return l.createElement(l.Fragment,null,(0,a.sY)({ourProps:B,theirProps:V,slot:N,defaultTag:"button",name:"Popover.Button"}),L&&!S&&o&&l.createElement(y._,{id:m,features:y.A.Focusable,as:"button",type:"button",onFocus:$}))})),L=a.AN.RenderStrategy|a.AN.Static,N=(0,a.yV)((function(e,t){let[{popoverState:n},r]=O("Popover.Overlay"),o=(0,u.T)(t),i=`headlessui-popover-overlay-${(0,s.M)()}`,c=(0,p.oJ)(),f=null!==c?c===p.ZM.Open:0===n,v=(0,x.z)((e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();r({type:1})})),m=(0,l.useMemo)((()=>({open:0===n})),[n]);return(0,a.sY)({ourProps:{ref:o,id:i,"aria-hidden":!0,onClick:v},theirProps:e,slot:m,defaultTag:"div",features:L,visible:f,name:"Popover.Overlay"})})),z=a.AN.RenderStrategy|a.AN.Static,V=(0,a.yV)((function(e,t){let{focus:n=!1,...r}=e,[o,d]=O("Popover.Panel"),{close:v,isPortalled:m}=C("Popover.Panel"),b=`headlessui-focus-sentinel-before-${(0,s.M)()}`,h=`headlessui-focus-sentinel-after-${(0,s.M)()}`,E=(0,l.useRef)(null),S=(0,u.T)(E,t,(e=>{d({type:4,panel:e})})),P=(0,g.i)(E),T=(0,p.oJ)(),I=null!==T?T===p.ZM.Open:0===o.popoverState,w=(0,x.z)((e=>{var t;if(e.key===c.R.Escape){if(0!==o.popoverState||!E.current||(null==P?void 0:P.activeElement)&&!E.current.contains(P.activeElement))return;e.preventDefault(),e.stopPropagation(),d({type:1}),null==(t=o.button)||t.focus()}}));(0,l.useEffect)((()=>{var t;e.static||1===o.popoverState&&(null==(t=e.unmount)||t)&&d({type:4,panel:null})}),[o.popoverState,e.unmount,e.static,d]),(0,l.useEffect)((()=>{if(!n||0!==o.popoverState||!E.current)return;let e=null==P?void 0:P.activeElement;E.current.contains(e)||(0,f.jA)(E.current,f.TO.First)}),[n,E,o.popoverState]);let D=(0,l.useMemo)((()=>({open:0===o.popoverState,close:v})),[o,v]),M={ref:S,id:o.panelId,onKeyDown:w,onBlur:n&&0===o.popoverState?e=>{var t,n,r,l,i;let a=e.relatedTarget;!a||!E.current||null!=(t=E.current)&&t.contains(a)||(d({type:1}),((null==(r=null==(n=o.beforePanelSentinel.current)?void 0:n.contains)?void 0:r.call(n,a))||(null==(i=null==(l=o.afterPanelSentinel.current)?void 0:l.contains)?void 0:i.call(l,a)))&&a.focus({preventScroll:!0}))}:void 0,tabIndex:-1},F=(0,R.l)(),A=(0,x.z)((()=>{let e=E.current;e&&(0,i.E)(F.current,{[R.N.Forwards]:()=>{(0,f.jA)(e,f.TO.First)},[R.N.Backwards]:()=>{var e;null==(e=o.button)||e.focus({preventScroll:!0})}})})),L=(0,x.z)((()=>{let e=E.current;e&&(0,i.E)(F.current,{[R.N.Forwards]:()=>{var e,t,n;if(!o.button)return;let r=(0,f.GO)(),l=r.indexOf(o.button),i=r.slice(0,l+1),a=[...r.slice(l+1),...i];for(let u of a.slice())if((null==(t=null==(e=null==u?void 0:u.id)?void 0:e.startsWith)?void 0:t.call(e,"headlessui-focus-sentinel-"))||(null==(n=o.panel)?void 0:n.contains(u))){let e=a.indexOf(u);-1!==e&&a.splice(e,1)}(0,f.jA)(a,f.TO.First,!1)},[R.N.Backwards]:()=>(0,f.jA)(e,f.TO.Last)})}));return l.createElement(k.Provider,{value:o.panelId},I&&m&&l.createElement(y._,{id:b,ref:o.beforePanelSentinel,features:y.A.Focusable,as:"button",type:"button",onFocus:A}),(0,a.sY)({ourProps:M,theirProps:r,slot:D,defaultTag:"div",features:z,visible:I,name:"Popover.Panel"}),I&&m&&l.createElement(y._,{id:h,ref:o.afterPanelSentinel,features:y.A.Focusable,as:"button",type:"button",onFocus:L}))})),B=(0,a.yV)((function(e,t){let n=(0,l.useRef)(null),r=(0,u.T)(n,t),[o,i]=(0,l.useState)([]),s=(0,x.z)((e=>{i((t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t}))})),c=(0,x.z)((e=>(i((t=>[...t,e])),()=>s(e)))),d=(0,x.z)((()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let r=t.activeElement;return!(null==(e=n.current)||!e.contains(r))||o.some((e=>{var n,o;return(null==(n=t.getElementById(e.buttonId))?void 0:n.contains(r))||(null==(o=t.getElementById(e.panelId))?void 0:o.contains(r))}))})),f=(0,x.z)((e=>{for(let t of o)t.buttonId!==e&&t.close()})),p=(0,l.useMemo)((()=>({registerPopover:c,unregisterPopover:s,isFocusWithinPopoverGroup:d,closeOthers:f})),[c,s,d,f]),v=(0,l.useMemo)((()=>({})),[]),m=e,g={ref:r};return l.createElement(w.Provider,{value:p},(0,a.sY)({ourProps:g,theirProps:m,slot:v,defaultTag:"div",name:"Popover.Group"}))})),Y=Object.assign(F,{Button:A,Overlay:N,Panel:V,Group:B})},84231:function(e,t,n){n.d(t,{r:function(){return E}});var r=n(89526),o=n(98943),l=n(95532),i=n(8247),a=n(64601),u=n(15673),s=n(80786),c=n(86517);let d=(0,r.createContext)(null);function f(){let e=(0,r.useContext)(d);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return e}function p(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)((()=>function(e){let n=(0,c.z)((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),o=(0,r.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props})),[n,e.slot,e.name,e.props]);return r.createElement(d.Provider,{value:o},e.children)}),[t])]}let v=(0,o.yV)((function(e,t){let{passive:n=!1,...r}=e,i=f(),a=`headlessui-label-${(0,l.M)()}`,c=(0,s.T)(t);(0,u.e)((()=>i.register(a)),[a,i.register]);let d={ref:c,...i.props,id:a};return n&&("onClick"in d&&delete d.onClick,"onClick"in r&&delete r.onClick),(0,o.sY)({ourProps:d,theirProps:r,slot:i.slot||{},defaultTag:"label",name:i.name||"Label"})}));var m=n(20266),b=n(49114),g=n(76762),h=n(92646);let y=(0,r.createContext)(null);y.displayName="GroupContext";let x=r.Fragment;let R=(0,o.yV)((function(e,t){let{checked:n,onChange:u,name:d,value:f,...p}=e,v=`headlessui-switch-${(0,l.M)()}`,m=(0,r.useContext)(y),x=(0,r.useRef)(null),R=(0,s.T)(x,t,null===m?null:m.setSwitch),E=(0,c.z)((()=>u(!n))),S=(0,c.z)((e=>{if((0,a.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),E()})),P=(0,c.z)((e=>{e.key===i.R.Space?(e.preventDefault(),E()):e.key===i.R.Enter&&(0,h.g)(e.currentTarget)})),T=(0,c.z)((e=>e.preventDefault())),O=(0,r.useMemo)((()=>({checked:n})),[n]),I={id:v,ref:R,role:"switch",type:(0,b.f)(e,x),tabIndex:0,"aria-checked":n,"aria-labelledby":null==m?void 0:m.labelledby,"aria-describedby":null==m?void 0:m.describedby,onClick:S,onKeyUp:P,onKeyPress:T};return r.createElement(r.Fragment,null,null!=d&&n&&r.createElement(g._,{features:g.A.Hidden,...(0,o.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:n,name:d,value:f})}),(0,o.sY)({ourProps:I,theirProps:p,slot:O,defaultTag:"button",name:"Switch"}))})),E=Object.assign(R,{Group:function(e){let[t,n]=(0,r.useState)(null),[l,i]=p(),[a,u]=(0,m.f)(),s=(0,r.useMemo)((()=>({switch:t,setSwitch:n,labelledby:l,describedby:a})),[t,n,l,a]),c=e;return r.createElement(u,{name:"Switch.Description"},r.createElement(i,{name:"Switch.Label",props:{onClick(){!t||(t.click(),t.focus({preventScroll:!0}))}}},r.createElement(y.Provider,{value:s},(0,o.sY)({ourProps:{},theirProps:c,defaultTag:x,name:"Switch.Group"}))))},Label:v,Description:m.d})},97923:function(e,t,n){n.d(t,{u:function(){return z}});var r=n(89526),o=n(98943),l=n(74388),i=n(73830),a=n(12701),u=n(95532),s=n(13014),c=n(15673),d=n(9336),f=n(73955),p=n(80786);var v=n(78320);function m(e,...t){e&&t.length>0&&e.classList.add(...t)}function b(e,...t){e&&t.length>0&&e.classList.remove(...t)}var g,h=((g=h||{}).Ended="ended",g.Cancelled="cancelled",g);function y(e,t,n,r){let o=n?"enter":"leave",l=(0,v.k)(),a=void 0!==r?function(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}(r):()=>{},u=(0,i.E)(o,{enter:()=>t.enter,leave:()=>t.leave}),s=(0,i.E)(o,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),c=(0,i.E)(o,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return b(e,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),m(e,...u,...c),l.nextFrame((()=>{b(e,...c),m(e,...s),function(e,t){let n=(0,v.k)();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[l,i]=[r,o].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t}));if(l+i!==0){let r=[];r.push(n.addEventListener(e,"transitionrun",(o=>{o.target===o.currentTarget&&(r.splice(0).forEach((e=>e())),r.push(n.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(t("ended"),r.splice(0).forEach((e=>e())))})),n.addEventListener(e,"transitioncancel",(e=>{e.target===e.currentTarget&&(t("cancelled"),r.splice(0).forEach((e=>e())))}))))})))}else t("ended");n.add((()=>t("cancelled"))),n.dispose}(e,(n=>("ended"===n&&(b(e,...u),m(e,...t.entered)),a(n))))})),l.dispose}var x=n(35760),R=n(86517);function E({container:e,direction:t,classes:n,events:r,onStart:o,onStop:l}){let a=(0,s.t)(),u=(0,x.G)(),f=(0,d.E)(t),p=(0,R.z)((()=>(0,i.E)(f.current,{enter:()=>r.current.beforeEnter(),leave:()=>r.current.beforeLeave(),idle:()=>{}}))),m=(0,R.z)((()=>(0,i.E)(f.current,{enter:()=>r.current.afterEnter(),leave:()=>r.current.afterLeave(),idle:()=>{}})));(0,c.e)((()=>{let t=(0,v.k)();u.add(t.dispose);let r=e.current;if(r&&"idle"!==f.current&&a.current)return t.dispose(),p(),o.current(f.current),t.add(y(r,n.current,"enter"===f.current,(e=>{t.dispose(),(0,i.E)(e,{[h.Ended](){m(),l.current(f.current)},[h.Cancelled]:()=>{}})}))),t.dispose}),[t])}function S(e=""){return e.split(" ").filter((e=>e.trim().length>1))}let P=(0,r.createContext)(null);P.displayName="TransitionContext";var T,O=((T=O||{}).Visible="visible",T.Hidden="hidden",T);let I=(0,r.createContext)(null);function C(e){return"children"in e?C(e.children):e.current.filter((({state:e})=>"visible"===e)).length>0}function w(e){let t=(0,d.E)(e),n=(0,r.useRef)([]),l=(0,s.t)(),u=(0,R.z)(((e,r=o.l4.Hidden)=>{let u=n.current.findIndex((({id:t})=>t===e));-1!==u&&((0,i.E)(r,{[o.l4.Unmount](){n.current.splice(u,1)},[o.l4.Hidden](){n.current[u].state="hidden"}}),(0,a.Y)((()=>{var e;!C(n)&&l.current&&(null==(e=t.current)||e.call(t))})))})),c=(0,R.z)((e=>{let t=n.current.find((({id:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):n.current.push({id:e,state:"visible"}),()=>u(e,o.l4.Unmount)}));return(0,r.useMemo)((()=>({children:n,register:c,unregister:u})),[c,u,n])}function D(){}I.displayName="NestingContext";let k=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function M(e){var t;let n={};for(let r of k)n[r]=null!=(t=e[r])?t:D;return n}let F=o.AN.RenderStrategy,A=(0,o.yV)((function(e,t){let{beforeEnter:n,afterEnter:a,beforeLeave:s,afterLeave:c,enter:v,enterFrom:m,enterTo:b,entered:g,leave:h,leaveFrom:y,leaveTo:x,...R}=e,T=(0,r.useRef)(null),O=(0,p.T)(T,t),[D,k]=(0,r.useState)("visible"),A=R.unmount?o.l4.Unmount:o.l4.Hidden,{show:L,appear:N,initial:z}=function(){let e=(0,r.useContext)(P);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:V,unregister:B}=function(){let e=(0,r.useContext)(I);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),Y=(0,r.useRef)(null),$=(0,u.M)();(0,r.useEffect)((()=>{if($)return V($)}),[V,$]),(0,r.useEffect)((()=>{if(A===o.l4.Hidden&&$){if(L&&"visible"!==D)return void k("visible");(0,i.E)(D,{hidden:()=>B($),visible:()=>V($)})}}),[D,$,V,B,L,A]);let j=(0,d.E)({enter:S(v),enterFrom:S(m),enterTo:S(b),entered:S(g),leave:S(h),leaveFrom:S(y),leaveTo:S(x)}),H=function(e){let t=(0,r.useRef)(M(e));return(0,r.useEffect)((()=>{t.current=M(e)}),[e]),t}({beforeEnter:n,afterEnter:a,beforeLeave:s,afterLeave:c}),U=(0,f.H)();(0,r.useEffect)((()=>{if(U&&"visible"===D&&null===T.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[T,D,U]);let _=z&&!N,G=!U||_||Y.current===L?"idle":L?"enter":"leave",Z=(0,r.useRef)(!1),K=w((()=>{Z.current||(k("hidden"),B($))}));E({container:T,classes:j,events:H,direction:G,onStart:(0,d.E)((()=>{Z.current=!0})),onStop:(0,d.E)((e=>{Z.current=!1,"leave"===e&&!C(K)&&(k("hidden"),B($))}))}),(0,r.useEffect)((()=>{!_||(A===o.l4.Hidden?Y.current=null:Y.current=L)}),[L,_,D]);let Q=R,q={ref:O};return r.createElement(I.Provider,{value:K},r.createElement(l.up,{value:(0,i.E)(D,{visible:l.ZM.Open,hidden:l.ZM.Closed})},(0,o.sY)({ourProps:q,theirProps:Q,defaultTag:"div",features:F,visible:"visible"===D,name:"Transition.Child"})))})),L=(0,o.yV)((function(e,t){let{show:n,appear:a=!1,unmount:u,...s}=e,d=(0,r.useRef)(null),v=(0,p.T)(d,t);(0,f.H)();let m=(0,l.oJ)();if(void 0===n&&null!==m&&(n=(0,i.E)(m,{[l.ZM.Open]:!0,[l.ZM.Closed]:!1})),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[b,g]=(0,r.useState)(n?"visible":"hidden"),h=w((()=>{g("hidden")})),[y,x]=(0,r.useState)(!0),R=(0,r.useRef)([n]);(0,c.e)((()=>{!1!==y&&R.current[R.current.length-1]!==n&&(R.current.push(n),x(!1))}),[R,n]);let E=(0,r.useMemo)((()=>({show:n,appear:a,initial:y})),[n,a,y]);(0,r.useEffect)((()=>{if(n)g("visible");else if(C(h)){let e=d.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&g("hidden")}else g("hidden")}),[n,h]);let S={unmount:u};return r.createElement(I.Provider,{value:h},r.createElement(P.Provider,{value:E},(0,o.sY)({ourProps:{...S,as:r.Fragment,children:r.createElement(A,{ref:v,...S,...s})},theirProps:{},defaultTag:r.Fragment,features:F,visible:"visible"===b,name:"Transition"})))})),N=(0,o.yV)((function(e,t){let n=null!==(0,r.useContext)(P),o=null!==(0,l.oJ)();return r.createElement(r.Fragment,null,!n&&o?r.createElement(L,{ref:t,...e}):r.createElement(A,{ref:t,...e}))})),z=Object.assign(L,{Child:N,Root:L})},1480:function(e,t,n){n.d(t,{v:function(){return i}});var r=n(89526),o=n(15673),l=n(9336);function i(e,t){let[n,i]=(0,r.useState)(e),a=(0,l.E)(e);return(0,o.e)((()=>i(a.current)),[a,i,...t]),n}},35760:function(e,t,n){n.d(t,{G:function(){return l}});var r=n(89526),o=n(78320);function l(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)((()=>()=>e.dispose()),[e]),e}},85806:function(e,t,n){n.d(t,{O:function(){return l}});var r=n(89526),o=n(9336);function l(e,t,n,l){let i=(0,o.E)(n);(0,r.useEffect)((()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,l),()=>e.removeEventListener(t,n,l)}),[e,t,l])}},86517:function(e,t,n){n.d(t,{z:function(){return l}});var r=n(89526),o=n(9336);let l=function(e){let t=(0,o.E)(e);return r.useCallback(((...e)=>t.current(...e)),[t])}},95532:function(e,t,n){n.d(t,{M:function(){return s}});var r,o=n(89526),l=n(15673),i=n(73955);let a=0;function u(){return++a}let s=null!=(r=o.useId)?r:function(){let e=(0,i.H)(),[t,n]=o.useState(e?u:null);return(0,l.e)((()=>{null===t&&n(u())}),[t]),null!=t?""+t:void 0}},13014:function(e,t,n){n.d(t,{t:function(){return l}});var r=n(89526),o=n(15673);function l(){let e=(0,r.useRef)(!1);return(0,o.e)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},15673:function(e,t,n){n.d(t,{e:function(){return o}});var r=n(89526);let o="undefined"!=typeof window?r.useLayoutEffect:r.useEffect},9336:function(e,t,n){n.d(t,{E:function(){return l}});var r=n(89526),o=n(15673);function l(e){let t=(0,r.useRef)(e);return(0,o.e)((()=>{t.current=e}),[e]),t}},42670:function(e,t,n){n.d(t,{O:function(){return i}});var r=n(89526),o=n(25328),l=n(80842);function i(e,t,n=!0){let i=(0,r.useRef)(!1);function a(n,r){if(!i.current||n.defaultPrevented)return;let l=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e),a=r(n);if(null!==a&&a.ownerDocument.documentElement.contains(a)){for(let e of l){if(null===e)continue;let t=e instanceof HTMLElement?e:e.current;if(null!=t&&t.contains(a))return}return!(0,o.sP)(a,o.tJ.Loose)&&-1!==a.tabIndex&&n.preventDefault(),t(n,a)}}(0,r.useEffect)((()=>{requestAnimationFrame((()=>{i.current=n}))}),[n]),(0,l.s)("click",(e=>a(e,(e=>e.target))),!0),(0,l.s)("blur",(e=>a(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}},43676:function(e,t,n){n.d(t,{i:function(){return l}});var r=n(89526),o=n(57725);function l(...e){return(0,r.useMemo)((()=>(0,o.r)(...e)),[...e])}},49114:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(89526),o=n(15673);function l(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function i(e,t){let[n,i]=(0,r.useState)((()=>l(e)));return(0,o.e)((()=>{i(l(e))}),[e.type,e.as]),(0,o.e)((()=>{n||!t.current||t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&i("button")}),[n,t]),n}},73955:function(e,t,n){n.d(t,{H:function(){return l}});var r=n(89526);let o={serverHandoffComplete:!1};function l(){let[e,t]=(0,r.useState)(o.serverHandoffComplete);return(0,r.useEffect)((()=>{!0!==e&&t(!0)}),[e]),(0,r.useEffect)((()=>{!1===o.serverHandoffComplete&&(o.serverHandoffComplete=!0)}),[]),e}},80786:function(e,t,n){n.d(t,{h:function(){return i},T:function(){return a}});var r=n(89526),o=n(86517);let l=Symbol();function i(e,t=!0){return Object.assign(e,{[l]:t})}function a(...e){let t=(0,r.useRef)(e);(0,r.useEffect)((()=>{t.current=e}),[e]);let n=(0,o.z)((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}));return e.every((e=>null==e||(null==e?void 0:e[l])))?void 0:n}},48925:function(e,t,n){n.d(t,{N:function(){return i},l:function(){return a}});var r,o=n(89526),l=n(80842),i=((r=i||{})[r.Forwards=0]="Forwards",r[r.Backwards=1]="Backwards",r);function a(){let e=(0,o.useRef)(0);return(0,l.s)("keydown",(t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)}),!0),e}},32602:function(e,t,n){n.d(t,{B:function(){return i}});var r=n(89526),o=n(15673),l=n(57725);function i({container:e,accept:t,walk:n,enabled:i=!0}){let a=(0,r.useRef)(t),u=(0,r.useRef)(n);(0,r.useEffect)((()=>{a.current=t,u.current=n}),[t,n]),(0,o.e)((()=>{if(!e||!i)return;let t=(0,l.r)(e);if(!t)return;let n=a.current,r=u.current,o=Object.assign((e=>n(e)),{acceptNode:n}),s=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,o,!1);for(;s.nextNode();)r(s.currentNode)}),[e,i,a,u])}},80842:function(e,t,n){n.d(t,{s:function(){return l}});var r=n(89526),o=n(9336);function l(e,t,n){let l=(0,o.E)(t);(0,r.useEffect)((()=>{function t(e){l.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)}),[e,n])}},76762:function(e,t,n){n.d(t,{A:function(){return l},_:function(){return i}});var r=n(98943);var o,l=((o=l||{})[o.None=1]="None",o[o.Focusable=2]="Focusable",o[o.Hidden=4]="Hidden",o);let i=(0,r.yV)((function(e,t){let{features:n=1,...o}=e,l={ref:t,"aria-hidden":2===(2&n)||void 0,style:{position:"absolute",width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...4===(4&n)&&2!==(2&n)&&{display:"none"}}};return(0,r.sY)({ourProps:l,theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}))},74388:function(e,t,n){n.d(t,{up:function(){return u},ZM:function(){return i},oJ:function(){return a}});var r=n(89526);let o=(0,r.createContext)(null);o.displayName="OpenClosedContext";var l,i=((l=i||{})[l.Open=0]="Open",l[l.Closed=1]="Closed",l);function a(){return(0,r.useContext)(o)}function u({value:e,children:t}){return r.createElement(o.Provider,{value:e},t)}},64601:function(e,t,n){function r(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}n.d(t,{P:function(){return r}})},15815:function(e,t,n){n.d(t,{T:function(){return o},d:function(){return l}});var r,o=((r=o||{})[r.First=0]="First",r[r.Previous=1]="Previous",r[r.Next=2]="Next",r[r.Last=3]="Last",r[r.Specific=4]="Specific",r[r.Nothing=5]="Nothing",r);function l(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1,l=(()=>{switch(e.focus){case 0:return n.findIndex((e=>!t.resolveDisabled(e)));case 1:{let e=n.slice().reverse().findIndex(((e,n,r)=>!(-1!==o&&r.length-n-1>=o)&&!t.resolveDisabled(e)));return-1===e?e:n.length-1-e}case 2:return n.findIndex(((e,n)=>!(n<=o)&&!t.resolveDisabled(e)));case 3:{let e=n.slice().reverse().findIndex((e=>!t.resolveDisabled(e)));return-1===e?e:n.length-1-e}case 4:return n.findIndex((n=>t.resolveId(n)===e.id));case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}})();return-1===l?r:l}},78320:function(e,t,n){function r(){let e=[],t=[],n={enqueue(e){t.push(e)},addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add((()=>e.removeEventListener(t,r,o)))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add((()=>cancelAnimationFrame(t)))},nextFrame:(...e)=>n.requestAnimationFrame((()=>n.requestAnimationFrame(...e))),setTimeout(...e){let t=setTimeout(...e);return n.add((()=>clearTimeout(t)))},add:t=>(e.push(t),()=>{let n=e.indexOf(t);if(n>=0){let[t]=e.splice(n,1);t()}}),dispose(){for(let t of e.splice(0))t()},async workQueue(){for(let e of t.splice(0))await e()}};return n}n.d(t,{k:function(){return r}})},25328:function(e,t,n){n.d(t,{TO:function(){return s},fE:function(){return c},tJ:function(){return p},C5:function(){return m},jA:function(){return h},GO:function(){return f},sP:function(){return v},z2:function(){return g}});var r=n(73830),o=n(57725);let l=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var i,a,u,s=((u=s||{})[u.First=1]="First",u[u.Previous=2]="Previous",u[u.Next=4]="Next",u[u.Last=8]="Last",u[u.WrapAround=16]="WrapAround",u[u.NoScroll=32]="NoScroll",u),c=((a=c||{})[a.Error=0]="Error",a[a.Overflow=1]="Overflow",a[a.Success=2]="Success",a[a.Underflow=3]="Underflow",a),d=((i=d||{})[i.Previous=-1]="Previous",i[i.Next=1]="Next",i);function f(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(l))}var p=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(p||{});function v(e,t=0){var n;return e!==(null==(n=(0,o.r)(e))?void 0:n.body)&&(0,r.E)(t,{0:()=>e.matches(l),1(){let t=e;for(;null!==t;){if(t.matches(l))return!0;t=t.parentElement}return!1}})}function m(e){null==e||e.focus({preventScroll:!0})}let b=["textarea","input"].join(",");function g(e,t=e=>e){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}function h(e,t,n=!0){let r,o=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,l=Array.isArray(e)?n?g(e):e:f(e),i=o.activeElement,a=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,l.indexOf(i))-1;if(4&t)return Math.max(0,l.indexOf(i))+1;if(8&t)return l.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=32&t?{preventScroll:!0}:{},c=0,d=l.length;do{if(c>=d||c+d<=0)return 0;let e=u+c;if(16&t)e=(e+d)%d;else{if(e<0)return 3;if(e>=d)return 1}r=l[e],null==r||r.focus(s),c+=a}while(r!==o.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,b))&&n}(r)&&r.select(),r.hasAttribute("tabindex")||r.setAttribute("tabindex","0"),2}},92646:function(e,t,n){function r(e={},t=null,n=[]){for(let[r,i]of Object.entries(e))l(n,o(t,r),i);return n}function o(e,t){return e?e+"["+t+"]":t}function l(e,t,n){if(Array.isArray(n))for(let[r,i]of n.entries())l(e,o(t,r.toString()),i);else n instanceof Date?e.push([t,n.toISOString()]):"boolean"==typeof n?e.push([t,n?"1":"0"]):"string"==typeof n?e.push([t,n]):"number"==typeof n?e.push([t,`${n}`]):null==n?e.push([t,""]):r(n,t,e)}function i(e){var t;let n=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(n)for(let r of n.elements)if("INPUT"===r.tagName&&"submit"===r.type||"BUTTON"===r.tagName&&"submit"===r.type||"INPUT"===r.nodeName&&"image"===r.type)return void r.click()}n.d(t,{g:function(){return i},t:function(){return r}})},73830:function(e,t,n){function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}n.d(t,{E:function(){return r}})},12701:function(e,t,n){function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}n.d(t,{Y:function(){return r}})},57725:function(e,t,n){function r(e){return"undefined"==typeof window?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}n.d(t,{r:function(){return r}})},98943:function(e,t,n){n.d(t,{AN:function(){return a},l4:function(){return u},oA:function(){return p},yV:function(){return f},sY:function(){return s}});var r,o,l=n(89526),i=n(73830),a=((o=a||{})[o.None=0]="None",o[o.RenderStrategy=1]="RenderStrategy",o[o.Static=2]="Static",o),u=((r=u||{})[r.Unmount=0]="Unmount",r[r.Hidden=1]="Hidden",r);function s({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:a}){let u=d(t,e);if(l)return c(u,n,r,a);let s=null!=o?o:0;if(2&s){let{static:e=!1,...t}=u;if(e)return c(t,n,r,a)}if(1&s){let{unmount:e=!0,...t}=u;return(0,i.E)(e?0:1,{0:()=>null,1:()=>c({...t,hidden:!0,style:{display:"none"}},n,r,a)})}return c(u,n,r,a)}function c(e,t={},n,r){let{as:o=n,children:i,refName:a="ref",...u}=v(e,["unmount","static"]),s=void 0!==e.ref?{[a]:e.ref}:{},c="function"==typeof i?i(t):i;u.className&&"function"==typeof u.className&&(u.className=u.className(t));let f={};if(o===l.Fragment&&Object.keys(p(u)).length>0){if(!(0,l.isValidElement)(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(u).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"));return(0,l.cloneElement)(c,Object.assign({},d(c.props,p(v(u,["ref"]))),f,s))}return(0,l.createElement)(o,Object.assign({},v(u,["ref"]),o!==l.Fragment&&s,o!==l.Fragment&&f),c)}function d(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map((e=>[e,void 0]))));for(let r in n)Object.assign(t,{[r](e,...t){let o=n[r];for(let n of o){if(e.defaultPrevented)return;n(e,...t)}}});return t}function f(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function p(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function v(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}}}]);
//# sourceMappingURL=@headlessui.4d0608efe0c2343d3261e47ff41340e3.js.map