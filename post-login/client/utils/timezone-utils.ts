import * as moment from "moment"
import format from 'date-fns/format';


/*
getDate() -> for when the timestamp is given in number or string  --> number => utc format in seconds , string => timestamp
getTimeInUserTimezone() -> returns moment object that has time in given timezone
getTimeInUserTimeZone() -> returns time in given timezone in provided format
minuteLevelDateFormat() -> returns time in 'D MMM, h:mm a' format (or 'D MMM YYYY, h:mm a' if different year) in given timezone --> for acitivity timeline
formattedDate() -> to get date in a particular format
getTimestampForDatePicker() -> to get timestamp in a particular timezone when a date is selected in a datepicker.
  - when using a datepicker, a new date in created, in the timezone of the system.
  - When converting it into a different timezone, the date may increase or decrease by one.
  - To get the same date, adjustments are done and correct timestamp is fetched.
getTimestampFromDate() -> gets the exact timestamp for a given date
getDateForCampaigns() -> get the correct format for date in the campaigns table.
columnDateFormat() -> get the correct format for date in the prospects table.
*/


function getDate(time: number | Date | string): Date {
  if(typeof(time) == 'number'){
    return new Date(time * 1000)
  }
  else if(typeof(time) == 'string')
    return new Date(time)
  else
    return time
}

function getTimeInUserTimezone(time: number | Date | string, timezone: string): moment.Moment{

  const moment = require('moment-timezone')
  // console.log(time)

  const date = getDate(time) // converting to date format

  const x = moment(date).utc()

  const z: moment.Moment = moment.tz(x, timezone)
  // console.log("time in IST->" + date + "\ntime in " + timezone +"->" +z+" "+z.format('YYYY-MM-DD HH:mm:ss') +"\nisDST()->" +z.isDST() +"\n UTC->" + x)
  return z
}

//https://momentjs.com/timezone/docs/#/using-timezones/parsing-in-zone/ --> the moment library handles the DST

export function getTimeInUserTimeZone(time: number | Date | string, timezone: string, format?: string): string{
  return getTimeInUserTimezone(time , timezone).format(format ? format : 'YYYY-MM-DD HH:mm:ss')
}

export function minuteLevelDateFormat(timeStamp: number | Date | string, timezone: string) {
  const currentYear = moment().year();
  const timestampYear = moment(timeStamp).year();

  // If the timestamp is from a different year, include the year in the format
  if (currentYear !== timestampYear) {
    return getTimeInUserTimeZone(timeStamp, timezone, 'D MMM YYYY, h:mm a');
  } else {
    return getTimeInUserTimeZone(timeStamp, timezone, 'D MMM, h:mm a');
  }
}

export function dateYearFormatForInbox(timestamp: number | Date | string, timezone: string) {
    const formattedDate = moment().year() === moment(timestamp).year() ? getTimeInUserTimeZone(timestamp, timezone, 'ddd, MMM D') : getTimeInUserTimeZone(timestamp, timezone, 'ddd, MMM D, YYYY');
    return formattedDate;
}

export function timeFormatForInbox(timestamp: number | Date | string, timezone: string, showYear?: boolean) {
    return getTimeInUserTimeZone(timestamp, timezone, ' h:mm a');
}

export function dateTimeYearFormatForInbox(timestamp: number | Date | string, timezone: string) {
    const formattedDate = getTimeInUserTimeZone(timestamp, timezone, 'ddd, MMM D, YYYY, h:mm A');
    return formattedDate;
}

export function getTimeForInbox(timestamp: number| Date | string, timezone: string) {
  const today = moment();
  const timestampMoment = moment(timestamp);

  if (today.date() === timestampMoment.date() && today.month() === timestampMoment.month() && today.year() === timestampMoment.year()) {
    // Same day - show time
    return getTimeInUserTimeZone(timestamp, timezone, 'h:mm a');
  } else {
    return getTimeInUserTimeZone(timestamp, timezone, 'MMM D');
  }
}

export function formattedDate(date: number | Date | string, form: string): string {
  const formatDate: Date = getDate(date)
  return format(formatDate, form)
}

//for date picker specific purpose --> because we want timestamp for same date.
export function getTimestampForDatePicker(date: Date, timezone: string):number {
  const x = moment(date).unix() * 1000
  console.log(date.getTime())
  console.log(date.getDate(), moment.tz(x, timezone).date())
  if(date.getDate() == moment.tz(x, timezone).date())
    return moment(date!).tz(timezone).startOf('day').unix() * 1000
  else if (date.getDate() < moment.tz(x, timezone).date())
    return (moment(date!).tz(timezone).subtract(1, 'day').startOf('day').unix()) * 1000
  else return (moment(date!).tz(timezone).add(1, 'day').startOf('day').unix()) * 1000
}

export function getTimestampFromDate(date: Date, timezone: string): number {
  return (moment(date!).tz(timezone).unix()) * 1000
}

export function getStartDaySubtract(numUnit: number, timeUnit: moment.unitOfTime.DurationConstructor , timezone: string){
  return moment().tz(timezone).subtract(numUnit, timeUnit).startOf('day').utc().toDate();
}

export function getStartMonthSubtract(numUnit: number, timeUnit: moment.unitOfTime.DurationConstructor, timezone: string) {
  return moment().tz(timezone).subtract(numUnit, timeUnit).startOf('month').utc().toDate();
}

export function getEndMonthSubtract(numUnit: number, timeUnit: moment.unitOfTime.DurationConstructor, timezone: string) {
  return moment().tz(timezone).subtract(numUnit, timeUnit).endOf('month').utc().toDate();
}

export function getEndDaySubtract(numUnit: number, timeUnit: moment.unitOfTime.DurationConstructor, timezone: string) {
  return moment().tz(timezone).subtract(numUnit, timeUnit).endOf('day').utc().toDate();
}

export function getEndDayAdd(numUnit: number, timeUnit: moment.unitOfTime.DurationConstructor, timezone: string) {
  return moment().tz(timezone).add(numUnit, timeUnit).endOf('day').utc().toDate();
}

export function getStartDayAdd(numUnit: number, timeUnit: moment.unitOfTime.DurationConstructor, timezone: string) {
  return moment().tz(timezone).add(numUnit, timeUnit).startOf('day').utc().toDate();
}

export function getStartOfDate(unit: moment.unitOfTime.StartOf, timezone: string){
  return moment().tz(timezone).startOf(unit).utc().toDate()
}

export function getEndOfDate(unit: moment.unitOfTime.StartOf, timezone: string) {
  return moment().tz(timezone).endOf(unit).utc().toDate()
}

export function getStartDayDate(timezone: string) {
  return moment().tz(timezone).startOf('day').utc().toDate();
}

export function getEndDayDate(timezone: string) {

  return moment().tz(timezone).endOf('day').utc().toDate();
}

export function getDateForCampaigns(timeStamp: number, timezone: string) {
  const isEarlierThanYear = (moment().diff(moment(timeStamp), 'years') >= 1);
  if (isEarlierThanYear) {
    return moment.tz(timeStamp, timezone).format('MMM D, YYYY');
  } else {
    return moment.tz(timeStamp, timezone).format('MMM D'); //'MMM D');
  }
}

export function columnDateFormat(timeStamp: number | Date | string, timezone: string , format: string = 'D MMM, YYYY')
{
  return getTimeInUserTimeZone(timeStamp, timezone, format);
}


/*

input : seconds , Output -> 1 hour 5 min

if time less then an hour: output : 56 min 35 sec

*/

export function secondsToHoursMinutes(seconds: number): string {

  const seconds2 = Math.floor(seconds)
  const hours = Math.floor(seconds2 / 3600); // 1 hour = 3600 seconds
  const remainingSeconds = seconds2 % 3600;
  const minutes = Math.floor(remainingSeconds / 60); // 1 minute = 60 seconds
  const remainingSecondsAfterMinutes = remainingSeconds % 60;

  if (hours > 0) {
    return `${hours} hour ${minutes} min`;
  } else {
    return `${minutes} min ${remainingSecondsAfterMinutes} sec`;
  }

}


