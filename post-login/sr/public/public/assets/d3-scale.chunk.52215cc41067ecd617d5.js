"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["d3-scale"],{35406:function(n,t,r){r.d(t,{Z:function(){return i},x:function(){return c}});var u=r(34808),e=r(24701),o=r(6387);function i(){var n,t,r=(0,o.Z)().unknown(void 0),a=r.domain,c=r.range,f=0,l=1,h=!1,p=0,s=0,g=.5;function m(){var r=a().length,e=l<f,o=e?l:f,i=e?f:l;n=(i-o)/Math.max(1,r-p+2*s),h&&(n=Math.floor(n)),o+=(i-o-n*(r-p))*g,t=n*(1-p),h&&(o=Math.round(o),t=Math.round(t));var m=(0,u.Z)(r).map((function(t){return o+n*t}));return c(e?m.reverse():m)}return delete r.unknown,r.domain=function(n){return arguments.length?(a(n),m()):a()},r.range=function(n){return arguments.length?([f,l]=n,f=+f,l=+l,m()):[f,l]},r.rangeRound=function(n){return[f,l]=n,f=+f,l=+l,h=!0,m()},r.bandwidth=function(){return t},r.step=function(){return n},r.round=function(n){return arguments.length?(h=!!n,m()):h},r.padding=function(n){return arguments.length?(p=Math.min(1,s=+n),m()):p},r.paddingInner=function(n){return arguments.length?(p=Math.min(1,n),m()):p},r.paddingOuter=function(n){return arguments.length?(s=+n,m()):s},r.align=function(n){return arguments.length?(g=Math.max(0,Math.min(1,n)),m()):g},r.copy=function(){return i(a(),[f,l]).round(h).paddingInner(p).paddingOuter(s).align(g)},e.o.apply(m(),arguments)}function a(n){var t=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return a(t())},n}function c(){return a(i.apply(null,arguments).paddingInner(1))}},77810:function(n,t,r){r.d(t,{JG:function(){return s},ZP:function(){return m},yR:function(){return f},l4:function(){return g}});var u=r(25658),e=r(6011),o=r(58983),i=r(7726);var a=r(44786),c=[0,1];function f(n){return n}function l(n,t){return(t-=n=+n)?function(r){return(r-n)/t}:(r=isNaN(t)?NaN:.5,function(){return r});var r}function h(n,t,r){var u=n[0],e=n[1],o=t[0],i=t[1];return e<u?(u=l(e,u),o=r(i,o)):(u=l(u,e),o=r(o,i)),function(n){return o(u(n))}}function p(n,t,r){var e=Math.min(n.length,t.length)-1,o=new Array(e),i=new Array(e),a=-1;for(n[e]<n[0]&&(n=n.slice().reverse(),t=t.slice().reverse());++a<e;)o[a]=l(n[a],n[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=(0,u.ZP)(n,t,1,e)-1;return i[r](o[r](t))}}function s(n,t){return t.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function g(){var n,t,r,u,l,s,g=c,m=c,d=e.Z,y=f;function v(){var n=Math.min(g.length,m.length);return y!==f&&(y=function(n,t){var r;return n>t&&(r=n,n=t,t=r),function(r){return Math.max(n,Math.min(t,r))}}(g[0],g[n-1])),u=n>2?p:h,l=s=null,M}function M(t){return null==t||isNaN(t=+t)?r:(l||(l=u(g.map(n),m,d)))(n(y(t)))}return M.invert=function(r){return y(t((s||(s=u(m,g.map(n),o.Z)))(r)))},M.domain=function(n){return arguments.length?(g=Array.from(n,a.Z),v()):g.slice()},M.range=function(n){return arguments.length?(m=Array.from(n),v()):m.slice()},M.rangeRound=function(n){return m=Array.from(n),d=i.Z,v()},M.clamp=function(n){return arguments.length?(y=!!n||f,v()):y!==f},M.interpolate=function(n){return arguments.length?(d=n,v()):d},M.unknown=function(n){return arguments.length?(r=n,M):r},function(r,u){return n=r,t=u,v()}}function m(){return g()(f,f)}},91803:function(n,t,r){r.d(t,{ti:function(){return u.Z},AB:function(){return gn},Wr:function(){return mn},dK:function(){return yn},KR:function(){return vn},b4:function(){return dn},ez:function(){return i},qm:function(){return x.O},BY:function(){return e.Z},p2:function(){return Z},PK:function(){return x.Z},q2:function(){return u.x},vY:function(){return Q},FT:function(){return _},aE:function(){return K},s$:function(){return I},cJ:function(){return on},$l:function(){return an},bE:function(){return fn},IO:function(){return hn},aA:function(){return ln},lQ:function(){return cn},PU:function(){return E},eh:function(){return b},ut:function(){return U},Xf:function(){return V},KY:function(){return nn},uk:function(){return Mn.Z}});var u=r(35406),e=r(8919),o=r(44786);function i(n){var t;function r(n){return null==n||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(n=Array.from(t,o.Z),r):n.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return i(n).unknown(t)},n=arguments.length?Array.from(n,o.Z):[0,1],(0,e.Q)(r)}var a=r(28760),c=r(95761),f=r(52253);function l(n,t){var r,u=0,e=(n=n.slice()).length-1,o=n[u],i=n[e];return i<o&&(r=u,u=e,e=r,r=o,o=i,i=r),n[u]=t.floor(o),n[e]=t.ceil(i),n}var h=r(77810),p=r(24701);function s(n){return Math.log(n)}function g(n){return Math.exp(n)}function m(n){return-Math.log(-n)}function d(n){return-Math.exp(-n)}function y(n){return isFinite(n)?+("1e"+n):n<0?0:n}function v(n){return(t,r)=>-n(-t,r)}function M(n){const t=n(s,g),r=t.domain;let u,e,o=10;function i(){return u=function(n){return n===Math.E?Math.log:10===n&&Math.log10||2===n&&Math.log2||(n=Math.log(n),t=>Math.log(t)/n)}(o),e=function(n){return 10===n?y:n===Math.E?Math.exp:t=>Math.pow(n,t)}(o),r()[0]<0?(u=v(u),e=v(e),n(m,d)):n(s,g),t}return t.base=function(n){return arguments.length?(o=+n,i()):o},t.domain=function(n){return arguments.length?(r(n),i()):r()},t.ticks=n=>{const t=r();let i=t[0],c=t[t.length-1];const f=c<i;f&&([i,c]=[c,i]);let l,h,p=u(i),s=u(c);const g=null==n?10:+n;let m=[];if(!(o%1)&&s-p<g){if(p=Math.floor(p),s=Math.ceil(s),i>0){for(;p<=s;++p)for(l=1;l<o;++l)if(h=p<0?l/e(-p):l*e(p),!(h<i)){if(h>c)break;m.push(h)}}else for(;p<=s;++p)for(l=o-1;l>=1;--l)if(h=p>0?l/e(-p):l*e(p),!(h<i)){if(h>c)break;m.push(h)}2*m.length<g&&(m=(0,a.ZP)(i,c,g))}else m=(0,a.ZP)(p,s,Math.min(s-p,g)).map(e);return f?m.reverse():m},t.tickFormat=(n,r)=>{if(null==n&&(n=10),null==r&&(r=10===o?"s":","),"function"!==typeof r&&(o%1||null!=(r=(0,c.Z)(r)).precision||(r.trim=!0),r=(0,f.WU)(r)),n===1/0)return r;const i=Math.max(1,o*n/t.ticks().length);return n=>{let t=n/e(Math.round(u(n)));return t*o<o-.5&&(t*=o),t<=i?r(n):""}},t.nice=()=>r(l(r(),{floor:n=>e(Math.floor(u(n))),ceil:n=>e(Math.ceil(u(n)))})),t}function Z(){const n=M((0,h.l4)()).domain([1,10]);return n.copy=()=>(0,h.JG)(n,Z()).base(n.base()),p.o.apply(n,arguments),n}function k(n){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/n))}}function w(n){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*n}}function N(n){var t=1,r=n(k(t),w(t));return r.constant=function(r){return arguments.length?n(k(t=+r),w(t)):t},(0,e.Q)(r)}function b(){var n=N((0,h.l4)());return n.copy=function(){return(0,h.JG)(n,b()).constant(n.constant())},p.o.apply(n,arguments)}var x=r(6387);function A(n){return function(t){return t<0?-Math.pow(-t,n):Math.pow(t,n)}}function O(n){return n<0?-Math.sqrt(-n):Math.sqrt(n)}function R(n){return n<0?-n*n:n*n}function P(n){var t=n(h.yR,h.yR),r=1;function u(){return 1===r?n(h.yR,h.yR):.5===r?n(O,R):n(A(r),A(1/r))}return t.exponent=function(n){return arguments.length?(r=+n,u()):r},(0,e.Q)(t)}function Q(){var n=P((0,h.l4)());return n.copy=function(){return(0,h.JG)(n,Q()).exponent(n.exponent())},p.o.apply(n,arguments),n}function E(){return Q.apply(null,arguments).exponent(.5)}function D(n){return Math.sign(n)*n*n}function G(n){return Math.sign(n)*Math.sqrt(Math.abs(n))}function I(){var n,t=(0,h.ZP)(),r=[0,1],u=!1;function i(r){var e=G(t(r));return isNaN(e)?n:u?Math.round(e):e}return i.invert=function(n){return t.invert(D(n))},i.domain=function(n){return arguments.length?(t.domain(n),i):t.domain()},i.range=function(n){return arguments.length?(t.range((r=Array.from(n,o.Z)).map(D)),i):r.slice()},i.rangeRound=function(n){return i.range(n).round(!0)},i.round=function(n){return arguments.length?(u=!!n,i):u},i.clamp=function(n){return arguments.length?(t.clamp(n),i):t.clamp()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return I(t.domain(),r).round(u).clamp(t.clamp()).unknown(n)},p.o.apply(i,arguments),(0,e.Q)(i)}var q=r(34693),J=r(25658),F=r(45172);function _(){var n,t=[],r=[],u=[];function e(){var n=0,e=Math.max(1,r.length);for(u=new Array(e-1);++n<e;)u[n-1]=(0,q.s7)(t,n/e);return o}function o(t){return null==t||isNaN(t=+t)?n:r[(0,J.ZP)(u,t)]}return o.invertExtent=function(n){var e=r.indexOf(n);return e<0?[NaN,NaN]:[e>0?u[e-1]:t[0],e<u.length?u[e]:t[t.length-1]]},o.domain=function(n){if(!arguments.length)return t.slice();t=[];for(let r of n)null==r||isNaN(r=+r)||t.push(r);return t.sort(F.Z),e()},o.range=function(n){return arguments.length?(r=Array.from(n),e()):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.quantiles=function(){return u.slice()},o.copy=function(){return _().domain(t).range(r).unknown(n)},p.o.apply(o,arguments)}function K(){var n,t=0,r=1,u=1,o=[.5],i=[0,1];function a(t){return null!=t&&t<=t?i[(0,J.ZP)(o,t,0,u)]:n}function c(){var n=-1;for(o=new Array(u);++n<u;)o[n]=((n+1)*r-(n-u)*t)/(u+1);return a}return a.domain=function(n){return arguments.length?([t,r]=n,t=+t,r=+r,c()):[t,r]},a.range=function(n){return arguments.length?(u=(i=Array.from(n)).length-1,c()):i.slice()},a.invertExtent=function(n){var e=i.indexOf(n);return e<0?[NaN,NaN]:e<1?[t,o[0]]:e>=u?[o[u-1],r]:[o[e-1],o[e]]},a.unknown=function(t){return arguments.length?(n=t,a):a},a.thresholds=function(){return o.slice()},a.copy=function(){return K().domain([t,r]).range(i).unknown(n)},p.o.apply((0,e.Q)(a),arguments)}function U(){var n,t=[.5],r=[0,1],u=1;function e(e){return null!=e&&e<=e?r[(0,J.ZP)(t,e,0,u)]:n}return e.domain=function(n){return arguments.length?(t=Array.from(n),u=Math.min(t.length,r.length-1),e):t.slice()},e.range=function(n){return arguments.length?(r=Array.from(n),u=Math.min(t.length,r.length-1),e):r.slice()},e.invertExtent=function(n){var u=r.indexOf(n);return[t[u-1],t[u]]},e.unknown=function(t){return arguments.length?(n=t,e):n},e.copy=function(){return U().domain(t).range(r).unknown(n)},p.o.apply(e,arguments)}var W=r(93676),j=r(18278),B=r(69573),C=r(43992),Y=r(22493),L=r(58017),T=r(83901),$=r(8722),z=r(42148);function S(n){return new Date(n)}function H(n){return n instanceof Date?+n:+new Date(+n)}function X(n,t,r,u,e,o,i,a,c,f){var p=(0,h.ZP)(),s=p.invert,g=p.domain,m=f(".%L"),d=f(":%S"),y=f("%I:%M"),v=f("%I %p"),M=f("%a %d"),Z=f("%b %d"),k=f("%B"),w=f("%Y");function N(n){return(c(n)<n?m:a(n)<n?d:i(n)<n?y:o(n)<n?v:u(n)<n?e(n)<n?M:Z:r(n)<n?k:w)(n)}return p.invert=function(n){return new Date(s(n))},p.domain=function(n){return arguments.length?g(Array.from(n,H)):g().map(S)},p.ticks=function(t){var r=g();return n(r[0],r[r.length-1],null==t?10:t)},p.tickFormat=function(n,t){return null==t?N:f(t)},p.nice=function(n){var r=g();return n&&"function"===typeof n.range||(n=t(r[0],r[r.length-1],null==n?10:n)),n?g(l(r,n)):p},p.copy=function(){return(0,h.JG)(p,X(n,t,r,u,e,o,i,a,c,f))},p}function V(){return p.o.apply(X(W.jK,W._g,j.jB,B.F0,C.Zy,Y.rr,L.WQ,T.Z_,$.E,z.i$).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function nn(){return p.o.apply(X(W.WG,W.jo,j.ol,B.me,C.pI,Y.AN,L.lM,T.rz,$.E,z.g0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}var tn=r(6011),rn=r(7726);function un(){var n,t,r,u,e,o=0,i=1,a=h.yR,c=!1;function f(t){return null==t||isNaN(t=+t)?e:a(0===r?.5:(t=(u(t)-n)*r,c?Math.max(0,Math.min(1,t)):t))}function l(n){return function(t){var r,u;return arguments.length?([r,u]=t,a=n(r,u),f):[a(0),a(1)]}}return f.domain=function(e){return arguments.length?([o,i]=e,n=u(o=+o),t=u(i=+i),r=n===t?0:1/(t-n),f):[o,i]},f.clamp=function(n){return arguments.length?(c=!!n,f):c},f.interpolator=function(n){return arguments.length?(a=n,f):a},f.range=l(tn.Z),f.rangeRound=l(rn.Z),f.unknown=function(n){return arguments.length?(e=n,f):e},function(e){return u=e,n=e(o),t=e(i),r=n===t?0:1/(t-n),f}}function en(n,t){return t.domain(n.domain()).interpolator(n.interpolator()).clamp(n.clamp()).unknown(n.unknown())}function on(){var n=(0,e.Q)(un()(h.yR));return n.copy=function(){return en(n,on())},p.O.apply(n,arguments)}function an(){var n=M(un()).domain([1,10]);return n.copy=function(){return en(n,an()).base(n.base())},p.O.apply(n,arguments)}function cn(){var n=N(un());return n.copy=function(){return en(n,cn()).constant(n.constant())},p.O.apply(n,arguments)}function fn(){var n=P(un());return n.copy=function(){return en(n,fn()).exponent(n.exponent())},p.O.apply(n,arguments)}function ln(){return fn.apply(null,arguments).exponent(.5)}function hn(){var n=[],t=h.yR;function r(r){if(null!=r&&!isNaN(r=+r))return t(((0,J.ZP)(n,r,1)-1)/(n.length-1))}return r.domain=function(t){if(!arguments.length)return n.slice();n=[];for(let r of t)null==r||isNaN(r=+r)||n.push(r);return n.sort(F.Z),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return n.map(((r,u)=>t(u/(n.length-1))))},r.quantiles=function(t){return Array.from({length:t+1},((r,u)=>(0,q.ZP)(n,u/t)))},r.copy=function(){return hn(t).domain(n)},p.O.apply(r,arguments)}var pn=r(40048);function sn(){var n,t,r,u,e,o,i,a=0,c=.5,f=1,l=1,p=h.yR,s=!1;function g(n){return isNaN(n=+n)?i:(n=.5+((n=+o(n))-t)*(l*n<l*t?u:e),p(s?Math.max(0,Math.min(1,n)):n))}function m(n){return function(t){var r,u,e;return arguments.length?([r,u,e]=t,p=(0,pn.Z)(n,[r,u,e]),g):[p(0),p(.5),p(1)]}}return g.domain=function(i){return arguments.length?([a,c,f]=i,n=o(a=+a),t=o(c=+c),r=o(f=+f),u=n===t?0:.5/(t-n),e=t===r?0:.5/(r-t),l=t<n?-1:1,g):[a,c,f]},g.clamp=function(n){return arguments.length?(s=!!n,g):s},g.interpolator=function(n){return arguments.length?(p=n,g):p},g.range=m(tn.Z),g.rangeRound=m(rn.Z),g.unknown=function(n){return arguments.length?(i=n,g):i},function(i){return o=i,n=i(a),t=i(c),r=i(f),u=n===t?0:.5/(t-n),e=t===r?0:.5/(r-t),l=t<n?-1:1,g}}function gn(){var n=(0,e.Q)(sn()(h.yR));return n.copy=function(){return en(n,gn())},p.O.apply(n,arguments)}function mn(){var n=M(sn()).domain([.1,1,10]);return n.copy=function(){return en(n,mn()).base(n.base())},p.O.apply(n,arguments)}function dn(){var n=N(sn());return n.copy=function(){return en(n,dn()).constant(n.constant())},p.O.apply(n,arguments)}function yn(){var n=P(sn());return n.copy=function(){return en(n,yn()).exponent(n.exponent())},p.O.apply(n,arguments)}function vn(){return yn.apply(null,arguments).exponent(.5)}var Mn=r(55805)},24701:function(n,t,r){function u(n,t){switch(arguments.length){case 0:break;case 1:this.range(n);break;default:this.range(t).domain(n)}return this}function e(n,t){switch(arguments.length){case 0:break;case 1:"function"===typeof n?this.interpolator(n):this.range(n);break;default:this.domain(n),"function"===typeof t?this.interpolator(t):this.range(t)}return this}r.d(t,{o:function(){return u},O:function(){return e}})},8919:function(n,t,r){r.d(t,{Q:function(){return a},Z:function(){return c}});var u=r(28760),e=r(77810),o=r(24701),i=r(55805);function a(n){var t=n.domain;return n.ticks=function(n){var r=t();return(0,u.ZP)(r[0],r[r.length-1],null==n?10:n)},n.tickFormat=function(n,r){var u=t();return(0,i.Z)(u[0],u[u.length-1],null==n?10:n,r)},n.nice=function(r){null==r&&(r=10);var e,o,i=t(),a=0,c=i.length-1,f=i[a],l=i[c],h=10;for(l<f&&(o=f,f=l,l=o,o=a,a=c,c=o);h-- >0;){if((o=(0,u.G9)(f,l,r))===e)return i[a]=f,i[c]=l,t(i);if(o>0)f=Math.floor(f/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;f=Math.ceil(f*o)/o,l=Math.floor(l*o)/o}e=o}return n},n}function c(){var n=(0,e.ZP)();return n.copy=function(){return(0,e.JG)(n,c())},o.o.apply(n,arguments),a(n)}},44786:function(n,t,r){function u(n){return+n}r.d(t,{Z:function(){return u}})},6387:function(n,t,r){r.d(t,{O:function(){return o},Z:function(){return i}});var u=r(6491),e=r(24701);const o=Symbol("implicit");function i(){var n=new u.L,t=[],r=[],a=o;function c(u){let e=n.get(u);if(void 0===e){if(a!==o)return a;n.set(u,e=t.push(u)-1)}return r[e%r.length]}return c.domain=function(r){if(!arguments.length)return t.slice();t=[],n=new u.L;for(const u of r)n.has(u)||n.set(u,t.push(u)-1);return c},c.range=function(n){return arguments.length?(r=Array.from(n),c):r.slice()},c.unknown=function(n){return arguments.length?(a=n,c):a},c.copy=function(){return i(t,r).unknown(a)},e.o.apply(c,arguments),c}},55805:function(n,t,r){r.d(t,{Z:function(){return f}});var u=r(28760),e=r(95761),o=r(94198),i=r(52253),a=r(50231),c=r(95308);function f(n,t,r,f){var l,h=(0,u.ly)(n,t,r);switch((f=(0,e.Z)(null==f?",f":f)).type){case"s":var p=Math.max(Math.abs(n),Math.abs(t));return null!=f.precision||isNaN(l=(0,o.Z)(h,p))||(f.precision=l),(0,i.jH)(f,p);case"":case"e":case"g":case"p":case"r":null!=f.precision||isNaN(l=(0,a.Z)(h,Math.max(Math.abs(n),Math.abs(t))))||(f.precision=l-("e"===f.type));break;case"f":case"%":null!=f.precision||isNaN(l=(0,c.Z)(h))||(f.precision=l-2*("%"===f.type))}return(0,i.WU)(f)}}}]);
//# sourceMappingURL=d3-scale.f095f7ae965329dadf6b2297ca730b19.js.map