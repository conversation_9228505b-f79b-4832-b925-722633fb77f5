type CSVPusherResponseStatus = "success" | "error"

interface CSVPusherResponseData {
  status: CSVPusherResponseStatus,
  filename: string,
}

interface CSVPusherSuccessStats {
  total_rows: number,
  total_empty_or_invalid_rows: number,
  total_duplicates_found: number,
  totaL_duplicates_ignored_for_no_edit_permission: number,
  total_duplicates_updated: number,
  total_created: number,
  total_assigned: number
}

interface CSVPusherSuccessResponse extends CSVPusherResponseData {
  status: "success",
  filename: string,
  campaign_id?: number,
  stats: CSVPusherSuccessStats
}

interface CSVPusherErrorResponse extends CSVPusherResponseData {
  status: "error",
  filename: string,
  error: string
}

export type CSVPusherResponse = CSVPusherErrorResponse | CSVPusherSuccessResponse

export function getPusherChannelNameUploadProspectCSV(orgId: number, accId: number) {
  return `psr_org_${orgId}_acc_${accId}`
}

//active conference related

type ConferencePusherSuccessResponse = Conference.IActiveCallParticipantDetails

export type ConferencePusherResponse = ConferencePusherSuccessResponse

export function getPusherChannelNameForCallConferenceParticipantDetails(orgId: number, teamId: number) {
  return `psr_org_${orgId}_team_${teamId}`
}

export type EmailInfraPusherSuccessResponse = EmailInfra.DomainPurchaseSuccess

export function getPusherChannelNameForEmailInfraDetails(orgId: number, teamId: number) {
  return `psr_org_${orgId}_team_${teamId}`
}


export function getPusherChannelNameForOpportunitiesPipeline(data: {
  orgId: number;
  teamId: number;
  pipelineId: String;
}) {
  return `psr_org_${data.orgId}_team_${data.teamId}_pipeline_${data.pipelineId}`;
}

export function getPusherChannelNameForEmailWarmupStatus(data: {
  teamId: number;
}) {
  return `psr_team_${data.teamId}_email_warmup_status`;
}

export function getPusherChannelNameForMagicColumns(data: { teamId: number }) {
  return `psr_team_${data.teamId}_magic_columns`;
}

export function getPusherChannelNameForCampaignAISequence(orgId: number, teamId: number) {
  return `psr_org_${orgId}_team_${teamId}`
}

export function getPusherChannelNameForEmailHealthCheck(teamId?: number) {
  return `psr_team_${teamId}_email_health_check`
}

export function getPusherChannelNameForBillingPlanUpdate(
  orgId: number,
): string {
  return `psr_org_${orgId}_billing_plan_update`
}
