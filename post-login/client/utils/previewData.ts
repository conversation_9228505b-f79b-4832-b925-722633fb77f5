import { Campaigns } from '@sr/shared-product-components';

export function getSubjectFromPreviewStep(step: Campaigns.IPreviewStep){

  const subject: string | undefined =  (step.preview_data.step_type == 'send_email') ? 
    step.preview_data.subject : ((step.preview_data.step_type == 'manual_send_email') ?
    step.preview_data.subject : ((step.preview_data.step_type == 'send_linkedin_inmail')) ?
    step.preview_data.subject : ((step.preview_data.step_type == 'auto_send_linkedin_inmail')) ?
    step.preview_data.subject : (step.preview_data.step_type == "auto_email_magic_content") ?
    step.preview_data.generated_subject  : (step.preview_data.step_type == "manual_email_magic_content") ?
    step.preview_data.generated_subject : undefined)

  return subject

}

export function getBodyPreview(step: Campaigns.IPreviewStep){

  // linkedin view profile doesn't have body preview hence not matching over it.

  const body_preview: string | undefined =  (step.preview_data.step_type == 'send_email') ? 
    step.preview_data.body_preview : (step.preview_data.step_type == 'manual_send_email') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'send_linkedin_connection_request') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'send_linkedin_inmail') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'send_linkedin_message') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'send_whatsapp_message') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'send_sms') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'general_task') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'call') ?
    step.preview_data.body_preview : (step.preview_data.step_type ==  'auto_send_linkedin_connection_request') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'auto_send_linkedin_message') ?
    step.preview_data.body_preview : (step.preview_data.step_type == 'auto_send_linkedin_inmail') ?
    step.preview_data.body_preview : (step.preview_data.step_type == "auto_email_magic_content") ?
    step.preview_data.generated_body_preview  : (step.preview_data.step_type == "manual_email_magic_content") ?
    step.preview_data.generated_body_preview
    : undefined

  return body_preview

}

export function getBodyFromPreviewStep(step: Campaigns.IPreviewStep){

    // linkedin view profile doesn't have body preview hence not matching over it.

    const body: string | undefined =  (step.preview_data.step_type == 'send_email') ? 
    step.preview_data.body : (step.preview_data.step_type == 'manual_send_email') ?
    step.preview_data.body : (step.preview_data.step_type == 'send_linkedin_connection_request') ?
    step.preview_data.body : (step.preview_data.step_type == 'send_linkedin_inmail') ?
    step.preview_data.body : (step.preview_data.step_type == 'send_linkedin_message') ?
    step.preview_data.body : (step.preview_data.step_type == 'send_whatsapp_message') ?
    step.preview_data.body : (step.preview_data.step_type == 'send_sms') ?
    step.preview_data.body : (step.preview_data.step_type == 'general_task') ?
    step.preview_data.body : (step.preview_data.step_type == 'call') ?
    step.preview_data.body : (step.preview_data.step_type ==  'auto_send_linkedin_connection_request') ?
    step.preview_data.body : (step.preview_data.step_type == 'auto_send_linkedin_message') ?
    step.preview_data.body : (step.preview_data.step_type == 'auto_send_linkedin_inmail') ?
    step.preview_data.body : (step.preview_data.step_type == "auto_email_magic_content") ?
    step.preview_data.generated_body  : (step.preview_data.step_type == "manual_email_magic_content") ?
    step.preview_data.generated_body : undefined

  return body

}
export function getEdited(step: Campaigns.IPreviewStep){

  const edited: boolean | undefined =  (step.preview_data.step_type == 'send_email') ? 
    step.preview_data.edited : (step.preview_data.step_type == 'manual_send_email') ?
    step.preview_data.edited : (step.preview_data.step_type == "auto_email_magic_content") ?
    step.preview_data.edited  : (step.preview_data.step_type == "manual_email_magic_content") ?
    step.preview_data.edited  : undefined

  return edited

}

export function getEmailThreadId(step: Campaigns.IPreviewStep){

  const email_thread_id: number | undefined =  (step.preview_data.step_type == 'send_email') ? 
    step.preview_data.email_thread_id : (step.preview_data.step_type == 'manual_send_email') ?
    step.preview_data.email_thread_id :  (step.preview_data.step_type == "auto_email_magic_content") ?
    step.preview_data.email_thread_id  : (step.preview_data.step_type == "manual_email_magic_content") ?
    step.preview_data.email_thread_id : undefined

  return email_thread_id


}

export function getEditableBody(step: Campaigns.IPreviewStep){

  const editable_body: string | undefined =  (step.preview_data.step_type == 'send_email') ? 
    step.preview_data.editable_body : (step.preview_data.step_type == 'manual_send_email') ?
    step.preview_data.editable_body : (step.preview_data.step_type == "auto_email_magic_content") ?
    step.preview_data.editable_body  : (step.preview_data.step_type == "manual_email_magic_content") ?
    step.preview_data.editable_body : undefined

  return editable_body

}
