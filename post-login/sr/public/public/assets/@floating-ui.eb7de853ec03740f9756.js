"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@floating-ui"],{96148:function(t,e,n){n.d(e,{x7:function(){return s},YF:function(){return f}});var o=n(55863),r=n(88301),i=n(89526),c=n(73961),l="undefined"!==typeof document?i.useLayoutEffect:i.useEffect;function u(t,e){if(t===e)return!0;if(typeof t!==typeof e)return!1;if("function"===typeof t&&t.toString()===e.toString())return!0;let n,o,r;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if(n=t.length,n!=e.length)return!1;for(o=n;0!==o--;)if(!u(t[o],e[o]))return!1;return!0}if(r=Object.keys(t),n=r.length,n!==Object.keys(e).length)return!1;for(o=n;0!==o--;)if(!Object.prototype.hasOwnProperty.call(e,r[o]))return!1;for(o=n;0!==o--;){const n=r[o];if(("_owner"!==n||!t.$$typeof)&&!u(t[n],e[n]))return!1}return!0}return t!==t&&e!==e}function f(t){let{middleware:e,placement:n="bottom",strategy:r="absolute",whileElementsMounted:f}=void 0===t?{}:t;const s=i.useRef(null),a=i.useRef(null),d=function(t){const e=i.useRef(t);return l((()=>{e.current=t})),e}(f),h=i.useRef(null),[m,p]=i.useState({x:null,y:null,strategy:r,placement:n,middlewareData:{}}),[g,y]=i.useState(e);u(null==g?void 0:g.map((t=>{let{options:e}=t;return e})),null==e?void 0:e.map((t=>{let{options:e}=t;return e})))||y(e);const w=i.useCallback((()=>{s.current&&a.current&&(0,o.oo)(s.current,a.current,{middleware:g,placement:n,strategy:r}).then((t=>{x.current&&c.flushSync((()=>{p(t)}))}))}),[g,n,r]);l((()=>{x.current&&w()}),[w]);const x=i.useRef(!1);l((()=>(x.current=!0,()=>{x.current=!1})),[]);const v=i.useCallback((()=>{if("function"===typeof h.current&&(h.current(),h.current=null),s.current&&a.current)if(d.current){const t=d.current(s.current,a.current,w);h.current=t}else w()}),[w,d]),b=i.useCallback((t=>{s.current=t,v()}),[v]),R=i.useCallback((t=>{a.current=t,v()}),[v]),L=i.useMemo((()=>({reference:s,floating:a})),[]);return i.useMemo((()=>({...m,update:w,refs:L,reference:b,floating:R})),[m,w,L,b,R])}const s=t=>{const{element:e,padding:n}=t;return{name:"arrow",options:t,fn(t){return o=e,Object.prototype.hasOwnProperty.call(o,"current")?null!=e.current?(0,r.x7)({element:e.current,padding:n}).fn(t):{}:e?(0,r.x7)({element:e,padding:n}).fn(t):{};var o}}}},88301:function(t,e,n){function o(t){return t.split("-")[0]}function r(t){return t.split("-")[1]}function i(t){return["top","bottom"].includes(o(t))?"x":"y"}function c(t){return"y"===t?"height":"width"}function l(t,e,n){let{reference:l,floating:u}=t;const f=l.x+l.width/2-u.width/2,s=l.y+l.height/2-u.height/2,a=i(e),d=c(a),h=l[d]/2-u[d]/2,m="x"===a;let p;switch(o(e)){case"top":p={x:f,y:l.y-u.height};break;case"bottom":p={x:f,y:l.y+l.height};break;case"right":p={x:l.x+l.width,y:s};break;case"left":p={x:l.x-u.width,y:s};break;default:p={x:l.x,y:l.y}}switch(r(e)){case"start":p[a]-=h*(n&&m?-1:1);break;case"end":p[a]+=h*(n&&m?-1:1)}return p}n.d(e,{x7:function(){return p},oo:function(){return u},RR:function(){return R},Cp:function(){return A},dr:function(){return k},cv:function(){return S},JB:function(){return s},uY:function(){return C},dp:function(){return D}});const u=async(t,e,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:c}=n,u=await(null==c.isRTL?void 0:c.isRTL(e));let f=await c.getElementRects({reference:t,floating:e,strategy:r}),{x:s,y:a}=l(f,o,u),d=o,h={},m=0;for(let p=0;p<i.length;p++){const{name:n,fn:g}=i[p],{x:y,y:w,data:x,reset:v}=await g({x:s,y:a,initialPlacement:o,placement:d,strategy:r,middlewareData:h,rects:f,platform:c,elements:{reference:t,floating:e}});s=null!=y?y:s,a=null!=w?w:a,h={...h,[n]:{...h[n],...x}},v&&m<=50&&(m++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(f=!0===v.rects?await c.getElementRects({reference:t,floating:e,strategy:r}):v.rects),({x:s,y:a}=l(f,d,u))),p=-1)}return{x:s,y:a,placement:d,strategy:r,middlewareData:h}};function f(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function s(t){return{...t,top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height}}async function a(t,e){var n;void 0===e&&(e={});const{x:o,y:r,platform:i,rects:c,elements:l,strategy:u}=t,{boundary:a="clippingAncestors",rootBoundary:d="viewport",elementContext:h="floating",altBoundary:m=!1,padding:p=0}=e,g=f(p),y=l[m?"floating"===h?"reference":"floating":h],w=s(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(y)))||n?y:y.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:a,rootBoundary:d,strategy:u})),x=s(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:"floating"===h?{...c.floating,x:o,y:r}:c.reference,offsetParent:await(null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),strategy:u}):c[h]);return{top:w.top-x.top+g.top,bottom:x.bottom-w.bottom+g.bottom,left:w.left-x.left+g.left,right:x.right-w.right+g.right}}const d=Math.min,h=Math.max;function m(t,e,n){return h(t,d(e,n))}const p=t=>({name:"arrow",options:t,async fn(e){const{element:n,padding:o=0}=null!=t?t:{},{x:l,y:u,placement:s,rects:a,platform:d}=e;if(null==n)return{};const h=f(o),p={x:l,y:u},g=i(s),y=r(s),w=c(g),x=await d.getDimensions(n),v="y"===g?"top":"left",b="y"===g?"bottom":"right",R=a.reference[w]+a.reference[g]-p[g]-a.floating[w],L=p[g]-a.reference[g],T=await(null==d.getOffsetParent?void 0:d.getOffsetParent(n));let A=T?"y"===g?T.clientHeight||0:T.clientWidth||0:0;0===A&&(A=a.floating[w]);const S=R/2-L/2,E=h[v],C=A-x[w]-h[b],k=A/2-x[w]/2+S,D=m(E,k,C),O=("start"===y?h[v]:h[b])>0&&k!==D&&a.reference[w]<=a.floating[w];return{[g]:p[g]-(O?k<E?E-k:C-k:0),data:{[g]:D,centerOffset:k-D}}}}),g={left:"right",right:"left",bottom:"top",top:"bottom"};function y(t){return t.replace(/left|right|bottom|top/g,(t=>g[t]))}function w(t,e,n){void 0===n&&(n=!1);const o=r(t),l=i(t),u=c(l);let f="x"===l?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[u]>e.floating[u]&&(f=y(f)),{main:f,cross:y(f)}}const x={start:"end",end:"start"};function v(t){return t.replace(/start|end/g,(t=>x[t]))}const b=["top","right","bottom","left"],R=(b.reduce(((t,e)=>t.concat(e,e+"-start",e+"-end")),[]),function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n;const{placement:r,middlewareData:i,rects:c,initialPlacement:l,platform:u,elements:f}=e,{mainAxis:s=!0,crossAxis:d=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",flipAlignment:p=!0,...g}=t,x=o(r),b=h||(x!==l&&p?function(t){const e=y(t);return[v(t),e,v(e)]}(l):[y(l)]),R=[l,...b],L=await a(e,g),T=[];let A=(null==(n=i.flip)?void 0:n.overflows)||[];if(s&&T.push(L[x]),d){const{main:t,cross:e}=w(r,c,await(null==u.isRTL?void 0:u.isRTL(f.floating)));T.push(L[t],L[e])}if(A=[...A,{placement:r,overflows:T}],!T.every((t=>t<=0))){var S,E;const t=(null!=(S=null==(E=i.flip)?void 0:E.index)?S:0)+1,e=R[t];if(e)return{data:{index:t,overflows:A},reset:{placement:e}};let n="bottom";switch(m){case"bestFit":{var C;const t=null==(C=A.map((t=>[t,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:C[0].placement;t&&(n=t);break}case"initialPlacement":n=l}if(r!==n)return{reset:{placement:n}}}return{}}}});function L(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function T(t){return b.some((e=>t[e]>=0))}const A=function(t){let{strategy:e="referenceHidden",...n}=void 0===t?{}:t;return{name:"hide",async fn(t){const{rects:o}=t;switch(e){case"referenceHidden":{const e=L(await a(t,{...n,elementContext:"reference"}),o.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{const e=L(await a(t,{...n,altBoundary:!0}),o.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}},S=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){const{x:n,y:c}=e,l=await async function(t,e){const{placement:n,platform:c,elements:l}=t,u=await(null==c.isRTL?void 0:c.isRTL(l.floating)),f=o(n),s=r(n),a="x"===i(n),d=["left","top"].includes(f)?-1:1,h=u&&a?-1:1,m="function"==typeof e?e(t):e;let{mainAxis:p,crossAxis:g,alignmentAxis:y}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...m};return s&&"number"==typeof y&&(g="end"===s?-1*y:y),a?{x:g*h,y:p*d}:{x:p*d,y:g*h}}(e,t);return{x:n+l.x,y:c+l.y,data:l}}}};function E(t){return"x"===t?"y":"x"}const C=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:r,placement:c}=e,{mainAxis:l=!0,crossAxis:u=!1,limiter:f={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...s}=t,d={x:n,y:r},h=await a(e,s),p=i(o(c)),g=E(p);let y=d[p],w=d[g];if(l){const t="y"===p?"bottom":"right";y=m(y+h["y"===p?"top":"left"],y,y-h[t])}if(u){const t="y"===g?"bottom":"right";w=m(w+h["y"===g?"top":"left"],w,w-h[t])}const x=f.fn({...e,[p]:y,[g]:w});return{...x,data:{x:x.x-n,y:x.y-r}}}}},k=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:r,placement:c,rects:l,middlewareData:u}=e,{offset:f=0,mainAxis:s=!0,crossAxis:a=!0}=t,d={x:n,y:r},h=i(c),m=E(h);let p=d[h],g=d[m];const y="function"==typeof f?f({...l,placement:c}):f,w="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(s){const t="y"===h?"height":"width",e=l.reference[h]-l.floating[t]+w.mainAxis,n=l.reference[h]+l.reference[t]-w.mainAxis;p<e?p=e:p>n&&(p=n)}if(a){var x,v,b,R;const t="y"===h?"width":"height",e=["top","left"].includes(o(c)),n=l.reference[m]-l.floating[t]+(e&&null!=(x=null==(v=u.offset)?void 0:v[m])?x:0)+(e?0:w.crossAxis),r=l.reference[m]+l.reference[t]+(e?0:null!=(b=null==(R=u.offset)?void 0:R[m])?b:0)-(e?w.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:p,[m]:g}}}},D=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){const{placement:n,rects:i,platform:c,elements:l}=e,{apply:u,...f}=t,s=await a(e,f),d=o(n),m=r(n);let p,g;"top"===d||"bottom"===d?(p=d,g=m===(await(null==c.isRTL?void 0:c.isRTL(l.floating))?"start":"end")?"left":"right"):(g=d,p="end"===m?"top":"bottom");const y=h(s.left,0),w=h(s.right,0),x=h(s.top,0),v=h(s.bottom,0),b={availableHeight:i.floating.height-(["left","right"].includes(n)?2*(0!==x||0!==v?x+v:h(s.top,s.bottom)):s[p]),availableWidth:i.floating.width-(["top","bottom"].includes(n)?2*(0!==y||0!==w?y+w:h(s.left,s.right)):s[g])},R=await c.getDimensions(l.floating);null==u||u({...e,...b});const L=await c.getDimensions(l.floating);return R.width!==L.width||R.height!==L.height?{reset:{rects:!0}}:{}}}}},55863:function(t,e,n){n.d(e,{Me:function(){return P},oo:function(){return M}});var o=n(88301);function r(t){return t&&t.document&&t.location&&t.alert&&t.setInterval}function i(t){if(null==t)return window;if(!r(t)){const e=t.ownerDocument;return e&&e.defaultView||window}return t}function c(t){return i(t).getComputedStyle(t)}function l(t){return r(t)?"":t?(t.nodeName||"").toLowerCase():""}function u(){const t=navigator.userAgentData;return null!=t&&t.brands?t.brands.map((t=>t.brand+"/"+t.version)).join(" "):navigator.userAgent}function f(t){return t instanceof i(t).HTMLElement}function s(t){return t instanceof i(t).Element}function a(t){return"undefined"!=typeof ShadowRoot&&(t instanceof i(t).ShadowRoot||t instanceof ShadowRoot)}function d(t){const{overflow:e,overflowX:n,overflowY:o}=c(t);return/auto|scroll|overlay|hidden/.test(e+o+n)}function h(t){return["table","td","th"].includes(l(t))}function m(t){const e=/firefox/i.test(u()),n=c(t);return"none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||["transform","perspective"].includes(n.willChange)||e&&"filter"===n.willChange||e&&!!n.filter&&"none"!==n.filter}function p(){return!/^((?!chrome|android).)*safari/i.test(u())}const g=Math.min,y=Math.max,w=Math.round;function x(t,e,n){var o,r,c,l;void 0===e&&(e=!1),void 0===n&&(n=!1);const u=t.getBoundingClientRect();let a=1,d=1;e&&f(t)&&(a=t.offsetWidth>0&&w(u.width)/t.offsetWidth||1,d=t.offsetHeight>0&&w(u.height)/t.offsetHeight||1);const h=s(t)?i(t):window,m=!p()&&n,g=(u.left+(m&&null!=(o=null==(r=h.visualViewport)?void 0:r.offsetLeft)?o:0))/a,y=(u.top+(m&&null!=(c=null==(l=h.visualViewport)?void 0:l.offsetTop)?c:0))/d,x=u.width/a,v=u.height/d;return{width:x,height:v,top:y,right:g+x,bottom:y+v,left:g,x:g,y:y}}function v(t){return(e=t,(e instanceof i(e).Node?t.ownerDocument:t.document)||window.document).documentElement;var e}function b(t){return s(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function R(t){return x(v(t)).left+b(t).scrollLeft}function L(t,e,n){const o=f(e),r=v(e),i=x(t,o&&function(t){const e=x(t);return w(e.width)!==t.offsetWidth||w(e.height)!==t.offsetHeight}(e),"fixed"===n);let c={scrollLeft:0,scrollTop:0};const u={x:0,y:0};if(o||!o&&"fixed"!==n)if(("body"!==l(e)||d(r))&&(c=b(e)),f(e)){const t=x(e,!0);u.x=t.x+e.clientLeft,u.y=t.y+e.clientTop}else r&&(u.x=R(r));return{x:i.left+c.scrollLeft-u.x,y:i.top+c.scrollTop-u.y,width:i.width,height:i.height}}function T(t){return"html"===l(t)?t:t.assignedSlot||t.parentNode||(a(t)?t.host:null)||v(t)}function A(t){return f(t)&&"fixed"!==getComputedStyle(t).position?t.offsetParent:null}function S(t){const e=i(t);let n=A(t);for(;n&&h(n)&&"static"===getComputedStyle(n).position;)n=A(n);return n&&("html"===l(n)||"body"===l(n)&&"static"===getComputedStyle(n).position&&!m(n))?e:n||function(t){let e=T(t);for(a(e)&&(e=e.host);f(e)&&!["html","body"].includes(l(e));){if(m(e))return e;e=e.parentNode}return null}(t)||e}function E(t){if(f(t))return{width:t.offsetWidth,height:t.offsetHeight};const e=x(t);return{width:e.width,height:e.height}}function C(t){const e=T(t);return["html","body","#document"].includes(l(e))?t.ownerDocument.body:f(e)&&d(e)?e:C(e)}function k(t,e){var n;void 0===e&&(e=[]);const o=C(t),r=o===(null==(n=t.ownerDocument)?void 0:n.body),c=i(o),l=r?[c].concat(c.visualViewport||[],d(o)?o:[]):o,u=e.concat(l);return r?u:u.concat(k(l))}function D(t,e,n){return"viewport"===e?(0,o.JB)(function(t,e){const n=i(t),o=v(t),r=n.visualViewport;let c=o.clientWidth,l=o.clientHeight,u=0,f=0;if(r){c=r.width,l=r.height;const t=p();(t||!t&&"fixed"===e)&&(u=r.offsetLeft,f=r.offsetTop)}return{width:c,height:l,x:u,y:f}}(t,n)):s(e)?function(t,e){const n=x(t,!1,"fixed"===e),o=n.top+t.clientTop,r=n.left+t.clientLeft;return{top:o,left:r,x:r,y:o,right:r+t.clientWidth,bottom:o+t.clientHeight,width:t.clientWidth,height:t.clientHeight}}(e,n):(0,o.JB)(function(t){var e;const n=v(t),o=b(t),r=null==(e=t.ownerDocument)?void 0:e.body,i=y(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),l=y(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0);let u=-o.scrollLeft+R(t);const f=-o.scrollTop;return"rtl"===c(r||n).direction&&(u+=y(n.clientWidth,r?r.clientWidth:0)-i),{width:i,height:l,x:u,y:f}}(v(t)))}function O(t){const e=k(t),n=["absolute","fixed"].includes(c(t).position)&&f(t)?S(t):t;return s(n)?e.filter((t=>s(t)&&function(t,e){const n=null==e.getRootNode?void 0:e.getRootNode();if(t.contains(e))return!0;if(n&&a(n)){let n=e;do{if(n&&t===n)return!0;n=n.parentNode||n.host}while(n)}return!1}(t,n)&&"body"!==l(t))):[]}const H={getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:r}=t;const i=[..."clippingAncestors"===n?O(e):[].concat(n),o],c=i[0],l=i.reduce(((t,n)=>{const o=D(e,n,r);return t.top=y(o.top,t.top),t.right=g(o.right,t.right),t.bottom=g(o.bottom,t.bottom),t.left=y(o.left,t.left),t}),D(e,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{rect:e,offsetParent:n,strategy:o}=t;const r=f(n),i=v(n);if(n===i)return e;let c={scrollLeft:0,scrollTop:0};const u={x:0,y:0};if((r||!r&&"fixed"!==o)&&(("body"!==l(n)||d(i))&&(c=b(n)),f(n))){const t=x(n,!0);u.x=t.x+n.clientLeft,u.y=t.y+n.clientTop}return{...e,x:e.x-c.scrollLeft+u.x,y:e.y-c.scrollTop+u.y}},isElement:s,getDimensions:E,getOffsetParent:S,getDocumentElement:v,getElementRects:t=>{let{reference:e,floating:n,strategy:o}=t;return{reference:L(e,S(n),o),floating:{...E(n),x:0,y:0}}},getClientRects:t=>Array.from(t.getClientRects()),isRTL:t=>"rtl"===c(t).direction};function P(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:c=!0,animationFrame:l=!1}=o,u=r&&!l,f=i&&!l,a=u||f?[...s(t)?k(t):[],...k(e)]:[];a.forEach((t=>{u&&t.addEventListener("scroll",n,{passive:!0}),f&&t.addEventListener("resize",n)}));let d,h=null;if(c){let o=!0;h=new ResizeObserver((()=>{o||n(),o=!1})),s(t)&&!l&&h.observe(t),h.observe(e)}let m=l?x(t):null;return l&&function e(){const o=x(t);!m||o.x===m.x&&o.y===m.y&&o.width===m.width&&o.height===m.height||n(),m=o,d=requestAnimationFrame(e)}(),n(),()=>{var t;a.forEach((t=>{u&&t.removeEventListener("scroll",n),f&&t.removeEventListener("resize",n)})),null==(t=h)||t.disconnect(),h=null,l&&cancelAnimationFrame(d)}}const M=(t,e,n)=>(0,o.oo)(t,e,{platform:H,...n})},37317:function(t,e,n){function o(){return"undefined"!==typeof window}function r(t){return l(t)?(t.nodeName||"").toLowerCase():"#document"}function i(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function c(t){var e;return null==(e=(l(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function l(t){return!!o()&&(t instanceof Node||t instanceof i(t).Node)}function u(t){return!!o()&&(t instanceof Element||t instanceof i(t).Element)}function f(t){return!!o()&&(t instanceof HTMLElement||t instanceof i(t).HTMLElement)}function s(t){return!(!o()||"undefined"===typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof i(t).ShadowRoot)}n.d(e,{Dx:function(){return A},gQ:function(){return b},tF:function(){return c},wK:function(){return D},wk:function(){return r},Lw:function(){return S},Kx:function(){return k},Ow:function(){return E},Jj:function(){return i},hT:function(){return v},kK:function(){return u},Re:function(){return f},Py:function(){return T},ao:function(){return d},Ze:function(){return m},tR:function(){return g},Pf:function(){return R}});const a=new Set(["inline","contents"]);function d(t){const{overflow:e,overflowX:n,overflowY:o,display:r}=A(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!a.has(r)}const h=new Set(["table","td","th"]);function m(t){return h.has(r(t))}const p=[":popover-open",":modal"];function g(t){return p.some((e=>{try{return t.matches(e)}catch(n){return!1}}))}const y=["transform","translate","scale","rotate","perspective"],w=["transform","translate","scale","rotate","perspective","filter"],x=["paint","layout","strict","content"];function v(t){const e=R(),n=u(t)?A(t):t;return y.some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||w.some((t=>(n.willChange||"").includes(t)))||x.some((t=>(n.contain||"").includes(t)))}function b(t){let e=E(t);for(;f(e)&&!T(e);){if(v(e))return e;if(g(e))return null;e=E(e)}return null}function R(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const L=new Set(["html","body","#document"]);function T(t){return L.has(r(t))}function A(t){return i(t).getComputedStyle(t)}function S(t){return u(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function E(t){if("html"===r(t))return t;const e=t.assignedSlot||t.parentNode||s(t)&&t.host||c(t);return s(e)?e.host:e}function C(t){const e=E(t);return T(e)?t.ownerDocument?t.ownerDocument.body:t.body:f(e)&&d(e)?e:C(e)}function k(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const r=C(t),c=r===(null==(o=t.ownerDocument)?void 0:o.body),l=i(r);if(c){const t=D(l);return e.concat(l,l.visualViewport||[],d(r)?r:[],t&&n?k(t):[])}return e.concat(r,k(r,[],n))}function D(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}},71347:function(t,e,n){n.d(e,{ze:function(){return l},GW:function(){return c},Fp:function(){return r},VV:function(){return o},JB:function(){return u},NM:function(){return i}});const o=Math.min,r=Math.max,i=Math.round,c=Math.floor,l=t=>({x:t,y:t});function u(t){const{x:e,y:n,width:o,height:r}=t;return{width:o,height:r,top:n,left:e,right:e+o,bottom:n+r,x:e,y:n}}}}]);
//# sourceMappingURL=@floating-ui.2e738ae2b04f6db099b226edeab28477.js.map