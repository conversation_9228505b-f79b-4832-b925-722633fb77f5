import * as _ from 'lodash';
import { LogIn, SRSearch } from '@sr/shared-product-components';
import { Stats } from '../components/stats/stats';

export function getFilterCount(filterObj: SRSearch.ICustomDGFilter) {

  if (!filterObj) { // it May be null sometimes

    return 0;

  } else {

    const count = filterObj.filters.length;
    return count;
  }
}

export function getReportsFilterCount(filterObj: Stats.ICustomStatsFilter) {
  //TODO
  return false;
}

export function filterOperatorSymbol(op: string) {

  if (op === "equals") {

    return ":";

  } else if (op === "greater_than") {

    return ">";

  } else if (op === "less_than") {

    return "<";

  } else {
    return op.replace('_', ' ');
  }

}

export function makeOwnerLabel(
  owner_ids: number[],
  acct: LogIn.IAccount,
  currentTeamId: number
) {

  let ownerLabel = '';
  const members = _.find(acct.teams, t => t.team_id === currentTeamId)?.all_members;

  if (
    owner_ids.length === 1 &&
    owner_ids[0] === acct.internal_id
  ) {

    ownerLabel = 'Owned by Me';

  } else if (
    owner_ids.length === 1 &&
    owner_ids[0] === 0
  ) {

    ownerLabel = 'Owned by All';

  } else {

    const names = members?.filter(m => _.includes(owner_ids, m.user_id))
      .map(m => {

        if (m.user_id === acct.internal_id) {
          return "Me"
        } else {
          return m.first_name
        }

      });

    ownerLabel = `Owned by ${names?.join(', ')}`

  }

  return ownerLabel

}


export function timePeriodLabelText(period: Stats.IStatsTimePeriod): string {

  if (period === 'alltime') {
    return 'All time'
  } else if (period === 'custom') {
    return 'Custom range'
  } else if (period === 'month') {
    return 'Last month'
  } else if (period === 'month_till_date') {
    return 'Month till date'
  } else if (period === 'week') {
    return 'Last week'
  } else {
    return period
  }

}