import { AvatarNameData } from "@sr/design-component";
import { stringifyEmail, stringifyMultipleEmails } from "./editor";
import * as _ from 'lodash';

export function getSenderName(messageObject: Inbox.messageObjectType) {
  if (messageObject.object === 'email_message') {
    return messageObject.from.name
  }
  else if (messageObject.object === 'linkedin_message') {
    return ''
  }
  else {
    return ''
  }
}


export function getSenderEmail(messageObject: Inbox.messageObjectType) {
  if (messageObject.object === 'email_message') {
    return messageObject.from.email
  }
  else if (messageObject.object === 'linkedin_message') {
    return messageObject.from_profile_url.profile_url
  }
  else {
    return ''
  }
}

export function getRecevierEmails(messageObject: Inbox.messageObjectType) {
  if (messageObject.object === 'email_message') {
    return messageObject.to.map(e => _.toLower(e.email))
  }
  else if (messageObject.object === 'linkedin_message') {
    return [messageObject.to_profile_url.profile_url]
  }
  else {
    return []
  }
}

function getFullNameFromFirstAndLastName(first_name?: string, last_name?: string) {
  if (first_name) {
    if (last_name) {
      return first_name + " " + last_name
    }
    else {
      return first_name
    }
  }
  else {
    return undefined
  }
}

export function getInitialsDataFromContact(contact: Inbox.IConversationContact): AvatarNameData {
  if (contact.prospect_basic_info) {
    const firstName = contact.prospect_basic_info.first_name
    const lastName = contact.prospect_basic_info.last_name

    if (contact.channelType === 'email_channel') {
      return {
        name: getFullNameFromFirstAndLastName(firstName, lastName),
        first_name: firstName,
        last_name: lastName,
        identifier: contact.prospect_basic_info.email
      }
    }
    else if (contact.channelType === 'linkedin_channel') {
      return {
        name: getFullNameFromFirstAndLastName(firstName, lastName),
        first_name: firstName,
        last_name: lastName,
        identifier: contact.prospect_basic_info.profile_url
      }
    }
    else {
      return {
        identifier: ''
      }
    }
  }
  else {
    if (contact.channelType === 'email_channel') {
      return {
        name: contact.email.name,
        identifier: contact.email.email
      }
    }
    else if (contact.channelType === 'linkedin_channel') {
      return {
        name: contact.profile_url.name,
        identifier: contact.profile_url.profile_url
      }
    }
    else {
      return {
        identifier: ''
      }
    }
  }
}

export function getContactName(contact: Inbox.IConversationContact) {
  return getInitialsDataFromContact(contact).name
}

export function getContactUniqueValue(contact: Inbox.IConversationContact) {
  return getInitialsDataFromContact(contact).identifier
}

export function getStringifiedDetails(email: Inbox.messageObjectType) {
  if (email.object === 'email_message') {
    return stringifyEmail(email.from)
  }
  else {
    return email.from_profile_url.profile_url
  }
}

export function getStringifiedDetailsOfMultipleUsers(email: Inbox.messageObjectType) {
  if (email.object === 'email_message') {
    return stringifyMultipleEmails(email.to)
  }
  else {
    return email.to_profile_url.profile_url
  }
}