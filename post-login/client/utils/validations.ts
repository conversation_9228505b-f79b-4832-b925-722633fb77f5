import { CONSTANTS } from '../data/constants';
import {isValidPhoneNumber} from 'libphonenumber-js';

export function validateEmail(email: string) {
  const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email);
}

export function validateDomain(domain: string) {
  console.log('validate domain', domain);
  const re = /(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]/;
  // const re = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
  return re.test(domain);
}


export function newPasswordValidation(password: string) {
  let passowrdError: string | undefined = undefined
  let hasUpperCase = password.match("[A-Z]")
  let hasLowerCase = password.match("[a-z]")
  let hasDigit = password.match("\\d")
  let inRange = password.length < 50 && password.length > 7
  let passwordErrors = []
  if (!inRange) passwordErrors.push('between 8 to 50 characters')
  if (!hasUpperCase) passwordErrors.push('at least one uppercase letter')
  if (!hasLowerCase) passwordErrors.push('at least one lower letter')
  if (!hasDigit) passwordErrors.push('at least one number')

  if (passwordErrors.length > 0) {
    passowrdError = 'Password should have ' + passwordErrors.join(", ")
  }
  return passowrdError
}

export function validateTag(tag: string) {
  //only alphanumbers,spaces and hyphens are allowed
  const regex = new RegExp(CONSTANTS.TAGS_VALIDITY_REGEX);
  const isValid = regex.test(tag) && ((tag || '').length <= 20);
  return isValid
}

export function validatePhoneNumber(number: string){
  return isValidPhoneNumber(number);
}

export function isValidUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);

    // Optional: Add custom validations if needed, e.g., specific protocols
    const allowedProtocols = ["http:", "https:"];
    if (!allowedProtocols.includes(parsedUrl.protocol)) {
      return false;
    }

    return true;
  } catch (error) {
    // If URL constructor throws, it's not a valid URL
    return false;
  }
}