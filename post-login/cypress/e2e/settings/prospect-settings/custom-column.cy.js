// beforeEach(() => {
//   cy.login('signing')
// })

it.skip('Add Custom fields', () => {
  cy.visit('/');
  cy.pause();
  cy.xpath('//*[@id="root"]/div/div/div[3]/div/div[1]/div[2]').click(); //SELECT SETTINGS TAB
  // cy.pause();
  cy.get('a.item:contains("Team Settings")').click();

  cy.contains('PROSPECT SETTINGS').click();

  cy.contains('Custom fields').click();
  cy.contains('Add column').click();

  cy.get('input[placeholder="Enter Column name"]').type(`${Math.random().toString(36).substring(2, 12)}`);
  cy.get('input[name="field_type"][value="text"]').check();
  cy.get('button[type="submit"]').contains('Add').click();

  cy.pause();

});

it.skip('Delete Custom fields', () => {
  cy.visit('/');
  cy.pause();
  cy.xpath('//*[@id="root"]/div/div/div[3]/div/div[1]/div[2]').click(); //SELECT SETTINGS TAB
  // cy.pause();
  cy.get('a.item:contains("Team Settings")').click();

  cy.contains('PROSPECT SETTINGS').click();

  cy.contains('Custom fields').click();
  cy.get('.simple-icon-button').last().click();
  cy.contains('button', 'Delete').click();

  cy.pause();

});