"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["d3-zoom"],{30556:function(t,n,e){e.d(n,{sP:function(){return w},CR:function(){return f},P2:function(){return m}});var o=e(23975),i=e(12941),u=e(15713),r=e(22195),h=e(35827),s=e(24194),a=t=>()=>t;function c(t,{sourceEvent:n,target:e,transform:o,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:i}})}function l(t,n,e){this.k=t,this.x=n,this.y=e}l.prototype={constructor:l,scale:function(t){return 1===t?this:new l(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new l(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var f=new l(1,0,0);function m(t){for(;!t.__zoom;)if(!(t=t.parentNode))return f;return t.__zoom}function p(t){t.stopImmediatePropagation()}function v(t){t.preventDefault(),t.stopImmediatePropagation()}function y(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function d(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function _(){return this.__zoom||f}function z(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function g(){return navigator.maxTouchPoints||"ontouchstart"in this}function k(t,n,e){var o=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],u=t.invertY(n[0][1])-e[0][1],r=t.invertY(n[1][1])-e[1][1];return t.translate(i>o?(o+i)/2:Math.min(0,o)||Math.max(0,i),r>u?(u+r)/2:Math.min(0,u)||Math.max(0,r))}function w(){var t,n,e,m=y,w=d,x=k,b=z,T=g,M=[0,1/0],Z=[[-1/0,-1/0],[1/0,1/0]],E=250,Y=u.Z,X=(0,o.Z)("start","zoom","end"),P=500,D=0,V=10;function B(t){t.property("__zoom",_).on("wheel.zoom",j,{passive:!1}).on("mousedown.zoom",A).on("dblclick.zoom",H).filter(T).on("touchstart.zoom",N).on("touchmove.zoom",O).on("touchend.zoom touchcancel.zoom",R).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function C(t,n){return(n=Math.max(M[0],Math.min(M[1],n)))===t.k?t:new l(n,t.x,t.y)}function K(t,n,e){var o=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return o===t.x&&i===t.y?t:new l(t.k,o,i)}function S(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function q(t,n,e,o){t.on("start.zoom",(function(){G(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){G(this,arguments).event(o).end()})).tween("zoom",(function(){var t=this,i=arguments,u=G(t,i).event(o),r=w.apply(t,i),h=null==e?S(r):"function"===typeof e?e.apply(t,i):e,s=Math.max(r[1][0]-r[0][0],r[1][1]-r[0][1]),a=t.__zoom,c="function"===typeof n?n.apply(t,i):n,f=Y(a.invert(h).concat(s/a.k),c.invert(h).concat(s/c.k));return function(t){if(1===t)t=c;else{var n=f(t),e=s/n[2];t=new l(e,h[0]-n[0]*e,h[1]-n[1]*e)}u.zoom(null,t)}}))}function G(t,n,e){return!e&&t.__zooming||new I(t,n)}function I(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=w.apply(t,n),this.taps=0}function j(t,...n){if(m.apply(this,arguments)){var e=G(this,n).event(t),o=this.__zoom,i=Math.max(M[0],Math.min(M[1],o.k*Math.pow(2,b.apply(this,arguments)))),u=(0,h.Z)(t);if(e.wheel)e.mouse[0][0]===u[0]&&e.mouse[0][1]===u[1]||(e.mouse[1]=o.invert(e.mouse[0]=u)),clearTimeout(e.wheel);else{if(o.k===i)return;e.mouse=[u,o.invert(u)],(0,s.e1)(this),e.start()}v(t),e.wheel=setTimeout(r,150),e.zoom("mouse",x(K(C(o,i),e.mouse[0],e.mouse[1]),e.extent,Z))}function r(){e.wheel=null,e.end()}}function A(t,...n){if(!e&&m.apply(this,arguments)){var o=t.currentTarget,u=G(this,n,!0).event(t),a=(0,r.Z)(t.view).on("mousemove.zoom",y,!0).on("mouseup.zoom",d,!0),c=(0,h.Z)(t,o),l=t.clientX,f=t.clientY;(0,i.Z)(t.view),p(t),u.mouse=[c,this.__zoom.invert(c)],(0,s.e1)(this),u.start()}function y(t){if(v(t),!u.moved){var n=t.clientX-l,e=t.clientY-f;u.moved=n*n+e*e>D}u.event(t).zoom("mouse",x(K(u.that.__zoom,u.mouse[0]=(0,h.Z)(t,o),u.mouse[1]),u.extent,Z))}function d(t){a.on("mousemove.zoom mouseup.zoom",null),(0,i.D)(t.view,u.moved),v(t),u.event(t).end()}}function H(t,...n){if(m.apply(this,arguments)){var e=this.__zoom,o=(0,h.Z)(t.changedTouches?t.changedTouches[0]:t,this),i=e.invert(o),u=e.k*(t.shiftKey?.5:2),s=x(K(C(e,u),o,i),w.apply(this,n),Z);v(t),E>0?(0,r.Z)(this).transition().duration(E).call(q,s,o,t):(0,r.Z)(this).call(B.transform,s,o,t)}}function N(e,...o){if(m.apply(this,arguments)){var i,u,r,a,c=e.touches,l=c.length,f=G(this,o,e.changedTouches.length===l).event(e);for(p(e),u=0;u<l;++u)r=c[u],a=[a=(0,h.Z)(r,this),this.__zoom.invert(a),r.identifier],f.touch0?f.touch1||f.touch0[2]===a[2]||(f.touch1=a,f.taps=0):(f.touch0=a,i=!0,f.taps=1+!!t);t&&(t=clearTimeout(t)),i&&(f.taps<2&&(n=a[0],t=setTimeout((function(){t=null}),P)),(0,s.e1)(this),f.start())}}function O(t,...n){if(this.__zooming){var e,o,i,u,r=G(this,n).event(t),s=t.changedTouches,a=s.length;for(v(t),e=0;e<a;++e)o=s[e],i=(0,h.Z)(o,this),r.touch0&&r.touch0[2]===o.identifier?r.touch0[0]=i:r.touch1&&r.touch1[2]===o.identifier&&(r.touch1[0]=i);if(o=r.that.__zoom,r.touch1){var c=r.touch0[0],l=r.touch0[1],f=r.touch1[0],m=r.touch1[1],p=(p=f[0]-c[0])*p+(p=f[1]-c[1])*p,y=(y=m[0]-l[0])*y+(y=m[1]-l[1])*y;o=C(o,Math.sqrt(p/y)),i=[(c[0]+f[0])/2,(c[1]+f[1])/2],u=[(l[0]+m[0])/2,(l[1]+m[1])/2]}else{if(!r.touch0)return;i=r.touch0[0],u=r.touch0[1]}r.zoom("touch",x(K(o,i,u),r.extent,Z))}}function R(t,...o){if(this.__zooming){var i,u,s=G(this,o).event(t),a=t.changedTouches,c=a.length;for(p(t),e&&clearTimeout(e),e=setTimeout((function(){e=null}),P),i=0;i<c;++i)u=a[i],s.touch0&&s.touch0[2]===u.identifier?delete s.touch0:s.touch1&&s.touch1[2]===u.identifier&&delete s.touch1;if(s.touch1&&!s.touch0&&(s.touch0=s.touch1,delete s.touch1),s.touch0)s.touch0[1]=this.__zoom.invert(s.touch0[0]);else if(s.end(),2===s.taps&&(u=(0,h.Z)(u,this),Math.hypot(n[0]-u[0],n[1]-u[1])<V)){var l=(0,r.Z)(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return B.transform=function(t,n,e,o){var i=t.selection?t.selection():t;i.property("__zoom",_),t!==i?q(t,n,e,o):i.interrupt().each((function(){G(this,arguments).event(o).start().zoom(null,"function"===typeof n?n.apply(this,arguments):n).end()}))},B.scaleBy=function(t,n,e,o){B.scaleTo(t,(function(){var t=this.__zoom.k,e="function"===typeof n?n.apply(this,arguments):n;return t*e}),e,o)},B.scaleTo=function(t,n,e,o){B.transform(t,(function(){var t=w.apply(this,arguments),o=this.__zoom,i=null==e?S(t):"function"===typeof e?e.apply(this,arguments):e,u=o.invert(i),r="function"===typeof n?n.apply(this,arguments):n;return x(K(C(o,r),i,u),t,Z)}),e,o)},B.translateBy=function(t,n,e,o){B.transform(t,(function(){return x(this.__zoom.translate("function"===typeof n?n.apply(this,arguments):n,"function"===typeof e?e.apply(this,arguments):e),w.apply(this,arguments),Z)}),null,o)},B.translateTo=function(t,n,e,o,i){B.transform(t,(function(){var t=w.apply(this,arguments),i=this.__zoom,u=null==o?S(t):"function"===typeof o?o.apply(this,arguments):o;return x(f.translate(u[0],u[1]).scale(i.k).translate("function"===typeof n?-n.apply(this,arguments):-n,"function"===typeof e?-e.apply(this,arguments):-e),t,Z)}),o,i)},I.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1===++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0===--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=(0,r.Z)(this.that).datum();X.call(t,this.that,new c(t,{sourceEvent:this.sourceEvent,target:B,type:t,transform:this.that.__zoom,dispatch:X}),n)}},B.wheelDelta=function(t){return arguments.length?(b="function"===typeof t?t:a(+t),B):b},B.filter=function(t){return arguments.length?(m="function"===typeof t?t:a(!!t),B):m},B.touchable=function(t){return arguments.length?(T="function"===typeof t?t:a(!!t),B):T},B.extent=function(t){return arguments.length?(w="function"===typeof t?t:a([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),B):w},B.scaleExtent=function(t){return arguments.length?(M[0]=+t[0],M[1]=+t[1],B):[M[0],M[1]]},B.translateExtent=function(t){return arguments.length?(Z[0][0]=+t[0][0],Z[1][0]=+t[1][0],Z[0][1]=+t[0][1],Z[1][1]=+t[1][1],B):[[Z[0][0],Z[0][1]],[Z[1][0],Z[1][1]]]},B.constrain=function(t){return arguments.length?(x=t,B):x},B.duration=function(t){return arguments.length?(E=+t,B):E},B.interpolate=function(t){return arguments.length?(Y=t,B):Y},B.on=function(){var t=X.on.apply(X,arguments);return t===X?B:t},B.clickDistance=function(t){return arguments.length?(D=(t=+t)*t,B):Math.sqrt(D)},B.tapDistance=function(t){return arguments.length?(V=+t,B):V},B}m.prototype=l.prototype}}]);
//# sourceMappingURL=d3-zoom.de8f834d7c8184a23b5e483b4cf869c6.js.map