import * as React from 'react';
// import { camelCase as lo_camelCase, upperFirst as lo_upperFirst} from 'lodash';
import {
  SrIconAdd, SrIconRevert, SrIconDone, SrIconEdit, SrIconForward, SrIconMail, SrIconMore, SrIconReply, SrIconSnooze, SrIconCompany, SrIconChevronRight, SrIconUser, SrIconHelp, SrIconChevronLeft, SrIconChevronUp, SrIconChevronDown, SrIconSearch, SrIconClose, SrIconAccounts, SrIconAccountsSolid, SrIconAlert, SrIconCampaign, SrIconCampaignSolid, SrIconTasks, SrIconTasksSolid, SrIconFeed, SrIconFeedSolid, SrIconInbox, SrIconInboxSolid, SrIconIssues, SrIconIssuesSolid, SrIconProspects, SrIconProspectsSolid, SrIconReports, SrIconReportsSolid, SrIconSettings, SrIconSettingsSolid, SrIconSpamTest, SrIconSpamTestSolid, SrIconTemplate, SrIconTemplateSolid, SrIconLogIn, SrIconLogOut, SrIconPause, SrIconPlay, SrRefresh, SrInfo, SrIconStars, SrIconTick, SrIconTickCircle, SrIconUpload, SrIconContent, SrIconContentSolid, SRIconTag, SrIconArrowLeft, SrIconChannelSetup, SrIconChannelSetupSolid, SrIconPreview, SrIconPreviewSolid, SrIconAddCircle, SrIconFilter, SrIconSave, SrIconShowContent, SRIconWhatsapp, SRIconLinkedin, SRIconSmiley, SrIconOutlineCircle, SrIconDownload, SrIconDelete, SrIconSoftStart, SrIconQuestionTelegram, SrIconQuestionMark, SRIconPhone, SRIconGeneral, SrIconCalendar, SRIconUpgradePlan, SrIconAssign, SrIconUnAssign, SrIconCategoryChange, SrIconSend, SrIconEmailOpen, SrIconSms, SrAIIcon, SrIconCheckFilled, SrIconVideo, SrIconHideContent,
  SRIconArchive,SRIconUnArchive,SrIconCopy,SrIconMinus, SrIconCircleFilled, SrIconQuickStartSolid, SrIconQuickStart,
  ISRIconProps,
  SrIconExternalIcon,
  SrIconBilling,
  SrIconYourAccount,
  SrIconTeamSettings,
  SrIconOppotunities,SRLeadFinderIcon
} from '@sr/design-component';

export function classNames(...classes: any) {
  return classes.filter(Boolean).join(' ')
}

export function fetchIcon(icon: string, props?: ISRIconProps) {
  // const IconKey = lo_upperFirst(lo_camelCase(icon));
  // console.log('icon key', IconKey);
  // const Func = new Function(
  //   "return function "+ IconKey +
  // )
  // // const iconComponent = Components[iconKey];
  // return (
  // <>
  // {/* <IconKey /> */}
  // {React.createElement(SrIconAccounts, {})}

  // </>
  // )
  // return React.createElement(IconKey, {});
  switch (icon) {
    case 'sr_icon_add':
      return <SrIconAdd {...props} />
    case 'sr_icon_minus':
      return <SrIconMinus {...props} />
    case 'sr_ai_icon':
      return <SrAIIcon {...props} />
    case 'sr_icon_revert':
      return <SrIconRevert {...props} />
    case 'sr_icon_done':
      return <SrIconDone {...props} />
    case 'sr_icon_edit':
      return <SrIconEdit {...props} />
    case 'sr_icon_forward':
      return <SrIconForward {...props} />
    case 'sr_icon_mail':
      return <SrIconMail {...props} />
    case 'sr_icon_more':
      return <SrIconMore {...props} />
    case 'sr_icon_reply':
      return <SrIconReply {...props} />
    case 'sr_icon_snooze':
      return <SrIconSnooze {...props} />
    case 'sr_icon_company':
      return <SrIconCompany {...props} />
    case 'sr_icon_chevron_right':
      return <SrIconChevronRight {...props} />
    case 'sr_icon_user':
      return <SrIconUser {...props} />
    case 'sr_icon_help':
      return <SrIconHelp {...props} />
    case 'sr_icon_chevron_left':
      return <SrIconChevronLeft {...props} />
    case 'sr_icon_chevron_up':
      return <SrIconChevronUp {...props} />
    case 'sr_icon_chevron_down':
      return <SrIconChevronDown {...props} />
    case 'sr_icon_user':
      return <SrIconUser {...props} />
    case 'sr_icon_search':
      return <SrIconSearch {...props} />
    case 'sr_icon_close':
      return <SrIconClose {...props} />
    case 'sr_icon_accounts':
      return <SrIconAccounts {...props} />
    case 'sr_icon_accounts_solid':
      return <SrIconAccountsSolid {...props} />
    case 'sr_icon_alert':
      return <SrIconAlert {...props} />
    case 'sr_icon_campaign':
      return <SrIconCampaign {...props} />
    case 'sr_icon_campaign_solid':
      return <SrIconCampaignSolid {...props} />
    case 'sr_icon_tasks':
      return <SrIconTasks {...props} />
    case 'sr_icon_tasks_solid':
      return <SrIconTasksSolid {...props} />
    case 'sr_icon_feed':
      return <SrIconFeed {...props} />
    case 'sr_icon_feed_solid':
      return <SrIconFeedSolid {...props} />
    case 'sr_icon_inbox':
      return <SrIconInbox {...props} />
    case 'sr_icon_inbox_solid':
      return <SrIconInboxSolid {...props} />
    case 'sr_icon_issues':
      return <SrIconIssues {...props} />
    case 'sr_icon_issues_solid':
      return <SrIconIssuesSolid {...props} />
    case 'sr_icon_prospects':
      return <SrIconProspects {...props} />
    case 'sr_icon_prospects_solid':
      return <SrIconProspectsSolid {...props} />
    case 'sr_icon_reports':
      return <SrIconReports {...props} />
    case 'sr_icon_reports_solid':
      return <SrIconReportsSolid {...props} />
    case 'sr_icon_settings':
      return <SrIconSettings {...props} />
    case 'sr_icon_settings_solid':
      return <SrIconSettingsSolid {...props} />
    case 'sr_icon_spam_test':
      return <SrIconSpamTest {...props} />
    case 'sr_icon_spam_test_solid':
      return <SrIconSpamTestSolid {...props} />
    case 'sr_icon_template':
      return <SrIconTemplate {...props} />
    case 'sr_icon_template_solid':
      return <SrIconTemplateSolid {...props} />
    case 'sr_icon_log_in':
      return <SrIconLogIn {...props} />
    case 'sr_icon_log_out':
      return <SrIconLogOut {...props} />
    case 'sr_icon_refresh':
      return <SrRefresh {...props} />
    case 'sr_icon_info':
      return <SrInfo {...props} />
    case 'sr_icon_pause':
      return <SrIconPause {...props} />
    case 'sr_icon_play':
      return <SrIconPlay {...props} />
    case 'sr_icon_stars':
      return <SrIconStars {...props} />
    case 'sr_icon_tick':
      return <SrIconTick {...props} />
    case 'sr_icon_upload':
      return <SrIconUpload {...props} />
    case 'sr_icon_tick_circle':
      return <SrIconTickCircle {...props} />
    case 'sr_icon_show_content':
      return <SrIconShowContent {...props} />
    case 'sr_icon_hide_content':
      return <SrIconHideContent {...props} />
    case 'sr_icon_filter':
      return <SrIconFilter {...props} />
    case 'sr_icon_content':
      return <SrIconContent {...props} />
    case 'sr_icon_content_solid':
      return <SrIconContentSolid {...props} />
    case 'sr_icon_tag':
      return <SRIconTag {...props} />
    case 'sr_icon_arrow_left':
      return <SrIconArrowLeft {...props} />
    case 'sr_icon_channel_setup':
      return <SrIconChannelSetup {...props} />
    case 'sr_icon_channel_setup_solid':
      return <SrIconChannelSetupSolid {...props} />
    case 'sr_icon_preview':
      return <SrIconPreview {...props} />
    case 'sr_icon_preview_solid':
      return <SrIconPreviewSolid {...props} />
    case 'sr_icon_add_circle':
      return <SrIconAddCircle {...props} />
    case 'sr_icon_save':
      return <SrIconSave {...props} />
    case 'sr_icon_whatsapp':
      return <SRIconWhatsapp {...props} />
    case 'sr_icon_linkedin':
      return <SRIconLinkedin {...props} />
    case 'sr_icon_smiley':
      return <SRIconSmiley {...props} />
    case 'sr_icon_outline_circle':
      return <SrIconOutlineCircle {...props} />
    case 'sr_icon_download':
      return <SrIconDownload {...props} />
    case 'sr_icon_delete':
      return <SrIconDelete {...props} />
    case 'sr_icon_soft_start':
      return <SrIconSoftStart {...props} />
    case 'sr_icon_question_mark':
      return <SrIconQuestionMark {...props} />
    case 'sr_icon_question_telegram':
      return <SrIconQuestionTelegram {...props} />
    case 'sr_icon_calendar':
      return <SrIconCalendar {...props} />
    case 'sr_icon_phone':
      return <SRIconPhone {...props} />
    case 'sr_icon_general':
      return <SRIconGeneral {...props} />
    case 'sr_icon_upgrade_plan':
      return <SRIconUpgradePlan {...props} />
    case 'sr_icon_sms':
      return <SrIconSms {...props} />
    case 'sr_icon_assign':
      return <SrIconAssign {...props} />
    case 'sr_icon_copy':
      return <SrIconCopy {...props} />
    case 'sr_icon_unassign':
      return <SrIconUnAssign {...props} />
    case 'sr_icon_changed_category':
      return <SrIconCategoryChange {...props} />
    case 'sr_icon_send':
      return <SrIconSend {...props} />
    case 'sr_icon_email_open':
      return <SrIconEmailOpen {...props} />
    case 'sr_icon_check_filled':
      return <SrIconCheckFilled {...props} />
    case 'sr_icon_video':
      return <SrIconVideo {...props} />
    case 'sr_icon_archive':
      return <SRIconArchive {...props} />
    case 'sr_icon_unarchive':
      return <SRIconUnArchive {...props} />
    case 'sr_icon_circle_filled':
      return <SrIconCircleFilled {...props} />
   case 'sr_quick_start_solid':
      return <SrIconQuickStartSolid {...props} />
   case 'sr_quick_start':
      return <SrIconQuickStart {...props} />
    case 'sr_icon_external':
      return <SrIconExternalIcon {...props} />
    case 'sr_icon_billing':
      return <SrIconBilling {...props} />
    case 'sr_icon_your_account':
      return <SrIconYourAccount {...props} />
    case 'sr_icon_team_settings':
      return <SrIconTeamSettings {...props} />
    case 'sr_icon_opportunities':
      return <SrIconOppotunities {...props} />
      case 'sr_lead_finder_icon':
        return <SRLeadFinderIcon classes={props?.className}/>
    default:
      return <SrIconAdd {...props} />
  }
}
