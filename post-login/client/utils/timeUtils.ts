import { formatDistance } from "date-fns";

export function convertDateToDistance(date: number) {
  var dateToDateType = new Date(date)
  return formatDistance(dateToDateType, new Date())
}

// Format timestamp to show relative time like "2 hours ago", "3 days ago"
export function formatTimeAgo(timestamp: number | string | undefined): string {
  if (!timestamp || timestamp === 0 || timestamp === '') return 'Never checked';

  const now = new Date();
  let formattedDate: Date;

  // Handle different timestamp formats
  if (typeof timestamp === 'string') {
    // Handle ISO date strings like "2025-06-17T16:49:50.650+05:30"
    formattedDate = new Date(timestamp);
  } else if (typeof timestamp === 'number') {
    // Handle Unix timestamps (seconds or milliseconds)
    let timestampInMs: number;

    if (timestamp.toString().length === 13) {
      timestampInMs = timestamp; // Already in milliseconds
    } else if (timestamp.toString().length === 10) {
      timestampInMs = timestamp * 1000; // Convert seconds to milliseconds
    } else {
      return 'Invalid date';
    }

    formattedDate = new Date(timestampInMs);
  } else {
    return 'Invalid date';
  }

  // Check if the date is valid
  if (isNaN(formattedDate.getTime())) {
    return 'Invalid date';
  }

  // Check if the date is in the future (which would be unusual)
  if (formattedDate > now) {
    return 'Recently checked';
  }

  try {
    const distance = formatDistance(formattedDate, now, { addSuffix: true });
    return `checked ${distance}`;
  } catch (error) {
    console.warn('Error formatting date distance:', error, 'timestamp:', timestamp);
    return 'Recently checked';
  }
}