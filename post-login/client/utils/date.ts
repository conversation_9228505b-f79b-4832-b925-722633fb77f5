import * as moment from 'moment';
import formatISO from 'date-fns/formatISO';

export function dateExportFormat(date: Date) {
  return formatISO(date);
}



export function dateFormat(timeStamp: number | Date | string) {

  let formattedDate = '';

  if (timeStamp) {
    const date = new Date(timeStamp as any).getDate();

    const monthNum = new Date(timeStamp as any).getMonth() + 1;

    const month = (monthNum === 1) ? 'Jan' :
      (monthNum === 2) ? 'Feb' :
        (monthNum === 3) ? 'Mar' :
          (monthNum === 4) ? 'Apr' :
            (monthNum === 5) ? 'May' :
              (monthNum === 6) ? 'Jun' :
                (monthNum === 7) ? 'Jul' :
                  (monthNum === 8) ? 'Aug' :
                    (monthNum === 9) ? 'Sep' :
                      (monthNum === 10) ? 'Oct' :
                        (monthNum === 11) ? 'Nov' :
                          (monthNum === 12) ? 'Dec' : '';

    const year = new Date(timeStamp as any).getFullYear();

    formattedDate = date + ' ' + month + ', ' + year;

  }

  return (formattedDate);
}

export function statsBarChartDateFormat(date: Date | string) {
  return moment(date).format('DD MMM')
}

export function getIsBefore(start: number | Date | string, lengthInDays: number) {
  const end = moment(start).add(lengthInDays, 'days');
  const today = moment();
  return today.isBefore(end);
}

export function getEndDate(start: number | Date | string, lengthInDays: number) {
  const end = moment(start).add(lengthInDays, 'days');
  return moment(end).format('DD MMM YYYY');
}

export function dateFormatMoment(timeStamp: number | Date | string, format: string) {
  return moment(timeStamp).format(format);
}

export function differenceFromInDays(from: number | Date | string) {
  const diffInDays = Math.ceil(moment().diff(from, 'days', true));
  return diffInDays;
}

export function differenceToInDays(from: number | Date | string) {
  const diffInDays = moment(from).diff(moment(), 'days');
  return diffInDays;
}

// function pad(num: number | string) {
//   return ("0" + num).slice(-2);
// }
export function getTimeFromDate(timestamp: number) {
  const date = new Date(timestamp * 1000);
  let hours = date.getHours();
  let minutes = date.getMinutes();
  var ampm = hours >= 12 ? 'pm' : 'am';
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  const strMinutes = minutes < 10 ? '0' + minutes : minutes;
  var strTime = hours + ':' + strMinutes + ' ' + ampm;
  return strTime;
  // var date = new Date(timestamp * 1000);
  // var hours = date.getHours();
  // var minutes = date.getMinutes();
  // return pad(hours) + ":" + pad(minutes)
}

export function getDateForCampaigns(timeStamp: number) {
  const isEarlierThanYear = (moment().diff(moment(timeStamp), 'years') >= 1);
  if (isEarlierThanYear) {
    return moment(timeStamp).format('MMM D, YYYY');
  } else {
    return moment(timeStamp).format('MMM D');
  }
}

export function getUtcTimeOfDate(date: Date) {
  console.log('date', date);
  const dateFormat = 'YYYY-MM-DD';
  const dateInFormat = moment(date).format(dateFormat);

  const timeStamp = moment.tz(dateInFormat, dateFormat, 'UTC').valueOf();
  console.log('timeStamp', timeStamp, dateInFormat);
  return timeStamp;
}

