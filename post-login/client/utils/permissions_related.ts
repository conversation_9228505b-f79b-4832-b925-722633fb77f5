import { LogIn } from '@sr/shared-product-components';

export function checkPermission(currentPermission: LogIn.IPermissionV2, isOwner?: boolean): boolean {
  if (isOwner === undefined) {
    return (currentPermission.ownership === 'all' || currentPermission.ownership === 'owned')
  }
  else if (isOwner) {
    return (currentPermission.ownership === 'all' || currentPermission.ownership === 'owned')
  }
  else {
    return currentPermission.ownership === 'all'
  }

}


export function checkBillingPermission(currentPermission: LogIn.IPermissionV2, isAgencyAdmin: boolean): boolean {
  return (currentPermission.ownership === 'all' || isAgencyAdmin)
}
