import * as _ from 'lodash';
import * as awsApi from '../api/aws';
import * as dompurify from 'dompurify';
import { logInStore } from '../stores/LogInStore';
import { getRecevierEmails, getSenderEmail } from './inboxUtils';
import { CONSTANTS } from '../data/constants';
import { Campaigns } from '@sr/shared-product-components';

export const TINYMCE_URL = 'https://cdnjs.cloudflare.com/ajax/libs/tinymce/5.2.2/tinymce.min.js';

function randomString() {
  const length = 32;
  const allowedChars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'; // need to be url safe
  var result = '';
  for (var i = length; i > 0; --i) {
    result += allowedChars[Math.floor(Math.random() * allowedChars.length)];
  }
  return result;
}

export function generateImageFileName(accountId: number, filename: string) {
  const isProdction = _.includes(document.location.origin, "smartreach.io");

  const randomStringBlob = randomString();

  const name = accountId + '_' + Math.floor(Date.now()) + '_' + randomStringBlob + '_' + 'cmpn' + '_' + (isProdction ? '' : 'dev_' + filename);
  return name;
}


export function getTinymceOptions(data: {
  autoFocusOnEditor: boolean,
  accountId: number
}) {

  const aptosFontFaces = `
      @font-face {
        font-family: 'Aptos';
        src: url('${CONSTANTS.CDN_URL+ "/assets/jan25/fonts/Aptos.woff2"}') format('woff2');
        font-weight: normal;
        font-style: normal;
      }
      @font-face {
        font-family: 'Aptos';
        src: url('${CONSTANTS.CDN_URL+ "/assets/jan25/fonts/Aptos-Bold.woff2"}') format('woff2');
        font-weight: bold;
        font-style: normal;
      }
  `;

  const contentStyle = (logInStore.getAccountInfo.org.id === 3283) 
    ? `${aptosFontFaces} p {margin: 0; padding: 0} body {line-height: 1.4; font-family:arial, 'Aptos'; font-size: 14px} html {border: 1px dotted lightgrey}`
    : `${aptosFontFaces} p {margin: 0; padding: 0} html {height: 300px !important; overflow: auto !important;} body {line-height: 1.4; font-family:arial, 'Aptos'; font-size: 14px;}`;

  return {
    plugins: 'link image code lists textcolor emoticons autoresize paste table',
    auto_focus: data.autoFocusOnEditor || undefined ,

    toolbar: 'insertMergeTagButton | aiOptions | selectTemplateButton | formatgroup | link unlink image emoticons | table | code ',
    // toolbar: 'insertMergeTagButton | selectTemplateButton | fontsizeselect | fontselect | bold italic underline forecolor backcolor | indent outdent | bullist numlist | link unlink image emoticons | table | code |',
    table_toolbar: "tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | tablemergecells | tablesplitcells",

    toolbar_groups: {
      formatgroup: {
        icon: 'format',
        tooltip: 'Formatting',
        items: 'fontsizeselect | fontselect | bold italic underline forecolor backcolor | indent outdent | bullist numlist'
      }
    },
    // force_br_newlines: true,
    // force_p_newlines: false,
    forced_root_block: false, // Needed for 3.x
    // forced_root_block: 'div', // Needed for 3.x
    branding: false,
    statusbar: false,
    menubar: false,
    contextmenu: 'copy rephrase shorten link',
    // fullpage_default_font_family: 'arial',
    fontsize_formats: '6px 7px 8px 9px 10px 11px 12px 13px 14px 15px 16px 18px 20px 24px 30px 36px',
    body_class: 'mce-body-default',
    // content_style: 'p {margin: 0; padding: 0} body {line-height: 1.4}',
    content_style: contentStyle,
    // min_height: 200,
    autoresize_min_height: 200,
    autoresize_bottom_margin: 0,
    entity_encoding: 'raw' as const,
    font_formats: 'Aptos=Aptos;Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino; Calibri = calibri;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings,zapf dingbats;',

    allow_conditional_comments: true,

    paste_as_text: true,
    setup: '' as any,

    toolbar_mode: 'floating' as const,
    toolbar_location: 'bottom' as const,
    // skin: 'fabric',
    // content_css: 'fabric',

    // relative_urls: false,
    // remove_script_host: false,
    // document_base_url: "",

    // REF: https://www.tiny.cloud/docs/get-started/upload-images/#rollingyourownimagehandler
    // REF: https://www.tiny.cloud/docs/demo/local-upload/
    // we override default upload handler to simulate successful upload
    images_upload_handler: function (blobInfo: any, success: any, failure: any) {

      const file = blobInfo.blob();
      var fileSize = file.size / 1024 / 1024; // in MB

      if (fileSize > 1) {

        failure('Maximum allowed image size is 1MB: ' + fileSize);

      } else {

        const imageFilename = generateImageFileName(data.accountId, file.name);


        awsApi.getSignedUrl({ filename: imageFilename })
          .then((res) => {
            // console.log('Executing...');
            const URL = res.data.presigned_url;

            // console.log('found aws url: ', URL);

            const xhr = new XMLHttpRequest();
            xhr.open('PUT', URL);
            xhr.setRequestHeader('Content-Type', file.type);
            xhr.setRequestHeader('Content-Disposition', 'inline');
            xhr.send(file);
            xhr.addEventListener('load', () => {
              const linkUrl = xhr.responseURL.split('?');
              success(linkUrl[0]);
            });
            xhr.addEventListener('error', () => {
              const error = 'Unable to upload file Retry';
              failure(error);
            });
          })
          .catch((err) => {
            console.log('Failed to get URL');
            failure(err);
          });

      }

    },

  }
}

export function removeStyleTag(body: string) {
  // console.log('body 1', body);
  // let newBody = body.replace(/<!--[\s\S]*?-->/g, "");
  // console.log('body 2', newBody);
  // newBody = newBody.replace(/'/g, '"');
  // newBody = _.replace(newBody, /<style>↵\* {.*?<\/style>/g, "");
  // newBody = _.replace(newBody, /<style>\n\* {.*?<\/style>/g, "");
  const newBody = _.replace(body, /(<style[\w\W]+style>)/g, "");
  // newBody = _.replace(newBody, /([*]\s*{[^}]*})/gm, "");
  //ref: https://stackoverflow.com/questions/47178935/javascript-regex-to-match-universal-selector-in-a-multi-line-style-tag
  // console.log('body 3', newBody);
  return newBody;
}

////
export function sanitizeHTML(html: string) {
  const sanitizedHtml = dompurify.sanitize(html);
  console.log('sanitize 1', sanitizedHtml);
  if (_.includes(sanitizedHtml, '<style')) {
    const withoutStyleTag = removeStyleTag(sanitizedHtml);
    console.log('sanitize 2', withoutStyleTag);
    // console.log('sanitize 3', (sanitizedHtml.replace(/<style.*?<\/style>/g, '')));
    return withoutStyleTag;
  } else {
    return sanitizedHtml;
  }

}

export function getSubjectLineWhileReplying(data: {
  latestEmailSubject: string,
  isFwd: boolean
}): string {

  const prevSubject = data.latestEmailSubject;

  let newSubjectLine = '';

  if (data.isFwd) {
    const prevSubHasFwd = _.chain(prevSubject).toLower().trim().startsWith('fwd:').value();
    newSubjectLine = prevSubHasFwd ? prevSubject : 'Fwd: ' + prevSubject;
  } else {
    const prevSubHasRe = _.chain(prevSubject).toLower().trim().startsWith('re:').value();
    newSubjectLine = prevSubHasRe ? prevSubject : 'Re: ' + prevSubject;
  }

  return newSubjectLine;

}

export function stringifyEmail(email: Inbox.IEmailAddress): string {
  if (email.name && email.name.length > 0) {
    return `${email.name} <${email.email}>`
  } else {
    return email.email;
  }
}

export function stringifyMultipleEmails(emails: Inbox.IEmailAddress[]): string {

  return emails.map(e => stringifyEmail(e)).join(', ')

}

export function getToEmailsForReply(

  emailsInConversation: Inbox.IEmailMessageObject[]

): Inbox.IEmailAddress[] | undefined {

  if (_.isEmpty(emailsInConversation)) {
    return undefined;
  } else {

    const latestMsgFromProspect = _.findLast(emailsInConversation, em => {
      return !em.from_user;
    });

    if (latestMsgFromProspect) {
      // if prospect has replied in the thread, send new reply back to them

      return [latestMsgFromProspect.from];

    } else {
      // if prospect has not replied in the thread (only user has been sending emails), send new reply back to the latest to email


      const latestMsg = _.last(emailsInConversation);

      return latestMsg?.to

    }
  }

}

export function getFromEmailSettingIdForReply(

  emailsInConversation: Inbox.IEmailMessageObject[],

  emailsList: Campaigns.IEmailList[]

): number | undefined {

  const fromEmailsList = _.filter(emailsList, e => e.can_send);

  const latestMsg = _.last(emailsInConversation);

  if (!latestMsg) {

    if (_.isEmpty(fromEmailsList)) {
      return undefined;
    } else {
      return _.first(fromEmailsList)?.id
    }

  } else {

    if (latestMsg.from_user) {

      const email = _.find(fromEmailsList, e => {
        return e.name === getSenderEmail(latestMsg)
      });

      return email?.id;

      // return latestMsg.from.email;

    } else {
      const latestToEmails = getRecevierEmails(latestMsg)
      const latestCCEmails = latestMsg.object === 'email_message' ? latestMsg.cc_emails?.map(e => _.toLower(e.email)) || [] : [];
      const latestBCCEmails = latestMsg.object === 'email_message' ? latestMsg.bcc_emails?.map(e => _.toLower(e.email)) || [] : [];


      const inbox = _.find(fromEmailsList, em => {

        const lowerEmail = _.toLower(em.name);

        return (

          _.includes(latestToEmails, lowerEmail) ||
          _.includes(latestCCEmails, lowerEmail) ||
          _.includes(latestBCCEmails, lowerEmail)

        );

      });


      return inbox?.id

    }
  }

}
