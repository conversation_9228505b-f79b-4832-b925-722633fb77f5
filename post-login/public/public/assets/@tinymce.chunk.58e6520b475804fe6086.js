"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@tinymce"],{92836:function(n,t,e){e.d(t,{M:function(){return b}});var o=e(89526),r=e(2652),i=function(){return i=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},i.apply(this,arguments)},u={onActivate:r.func,onAddUndo:r.func,onBeforeAddUndo:r.func,onBeforeExecCommand:r.func,onBeforeGetContent:r.func,onBeforeRenderUI:r.func,onBeforeSetContent:r.func,onBeforePaste:r.func,onBlur:r.func,onChange:r.func,onClearUndos:r.func,onClick:r.func,onContextMenu:r.func,onCopy:r.func,onCut:r.func,onDblclick:r.func,onDeactivate:r.func,onDirty:r.func,onDrag:r.func,onDragDrop:r.func,onDragEnd:r.func,onDragGesture:r.func,onDragOver:r.func,onDrop:r.func,onExecCommand:r.func,onFocus:r.func,onFocusIn:r.func,onFocusOut:r.func,onGetContent:r.func,onHide:r.func,onInit:r.func,onKeyDown:r.func,onKeyPress:r.func,onKeyUp:r.func,onLoadContent:r.func,onMouseDown:r.func,onMouseEnter:r.func,onMouseLeave:r.func,onMouseMove:r.func,onMouseOut:r.func,onMouseOver:r.func,onMouseUp:r.func,onNodeChange:r.func,onObjectResizeStart:r.func,onObjectResized:r.func,onObjectSelected:r.func,onPaste:r.func,onPostProcess:r.func,onPostRender:r.func,onPreProcess:r.func,onProgressState:r.func,onRedo:r.func,onRemove:r.func,onReset:r.func,onSaveContent:r.func,onSelectionChange:r.func,onSetAttrib:r.func,onSetContent:r.func,onShow:r.func,onSubmit:r.func,onUndo:r.func,onVisualAid:r.func},s=i({apiKey:r.string,id:r.string,inline:r.bool,init:r.object,initialValue:r.string,onEditorChange:r.func,outputFormat:r.oneOf(["html","text"]),value:r.string,tagName:r.string,cloudChannel:r.string,plugins:r.oneOfType([r.string,r.array]),toolbar:r.oneOfType([r.string,r.array]),disabled:r.bool,textareaName:r.string,tinymceScriptSrc:r.string},u),c=function(n){return"function"===typeof n},p=function(n){return n in u},a=function(n,t,e){(function(n){return Object.keys(n).filter(p).filter((function(t){return c(n[t])})).map((function(t){return{handler:n[t],eventName:t.substring(2)}}))})(t).forEach((function(t){var o=e[t.eventName];c(o)&&n.off(t.eventName,o);var r=function(e){return t.handler(e,n)};e[t.eventName]=r,n.on(t.eventName,r)}))},f=0,l=function(n){var t=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++f+String(t)},d=function(n){return"undefined"===typeof n||""===n?[]:Array.isArray(n)?n:n.split(" ")},h=function(){return{listeners:[],scriptId:l("tiny-script"),scriptLoaded:!1}},y=function(){var n=h();return{load:function(t,e,o){n.scriptLoaded?o():(n.listeners.push(o),t.getElementById(n.scriptId)||function(n,t,e,o){var r=t.createElement("script");r.referrerPolicy="origin",r.type="application/javascript",r.id=n,r.src=e;var i=function(){r.removeEventListener("load",i),o()};r.addEventListener("load",i),t.head&&t.head.appendChild(r)}(n.scriptId,t,e,(function(){n.listeners.forEach((function(n){return n()})),n.scriptLoaded=!0})))},reinitialize:function(){n=h()}}}(),m=function(){var n="undefined"!==typeof window?window:e.g;return n&&n.tinymce?n.tinymce:null},g=e(85663),v=function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var e in t)t.hasOwnProperty(e)&&(n[e]=t[e])},n(t,e)};return function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}}(),C=function(){return C=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},C.apply(this,arguments)},b=function(n){function t(t){var e=n.call(this,t)||this;return e.initialise=function(){var n,t,o,r=C(C({},e.props.init),{target:e.elementRef.current,readonly:e.props.disabled,inline:e.inline,plugins:(n=e.props.init&&e.props.init.plugins,t=e.props.plugins,d(n).concat(d(t))),toolbar:e.props.toolbar||e.props.init&&e.props.init.toolbar,setup:function(n){e.editor=n,n.on("init",(function(t){e.initEditor(t,n)})),e.props.init&&"function"===typeof e.props.init.setup&&e.props.init.setup(n)}});null!==(o=e.elementRef.current)&&"textarea"===o.tagName.toLowerCase()&&(e.elementRef.current.style.visibility=""),m().init(r)},e.id=e.props.id||l("tiny-react"),e.elementRef=o.createRef(),e.inline=e.props.inline?e.props.inline:e.props.init&&e.props.init.inline,e.boundHandlers={},e}return v(t,n),t.prototype.componentDidUpdate=function(n){this.editor&&this.editor.initialized&&(a(this.editor,this.props,this.boundHandlers),this.currentContent=this.currentContent||this.editor.getContent({format:this.props.outputFormat}),"string"===typeof this.props.value&&this.props.value!==n.value&&this.props.value!==this.currentContent&&this.editor.setContent(this.props.value),"boolean"===typeof this.props.disabled&&this.props.disabled!==n.disabled&&this.editor.setMode(this.props.disabled?"readonly":"design"))},t.prototype.componentDidMount=function(){null!==m()?this.initialise():this.elementRef.current&&this.elementRef.current.ownerDocument&&y.load(this.elementRef.current.ownerDocument,this.getScriptSrc(),this.initialise)},t.prototype.componentWillUnmount=function(){null!==m()&&m().remove(this.editor)},t.prototype.render=function(){return this.inline?this.renderInline():this.renderIframe()},t.prototype.getScriptSrc=function(){var n=this.props.cloudChannel,t=this.props.apiKey?this.props.apiKey:"no-api-key";return(0,g.isNullOrUndefined)(this.props.tinymceScriptSrc)?"https://cdn.tiny.cloud/1/"+t+"/tinymce/"+n+"/tinymce.min.js":this.props.tinymceScriptSrc},t.prototype.initEditor=function(n,t){var e=this,o="string"===typeof this.props.value?this.props.value:"string"===typeof this.props.initialValue?this.props.initialValue:"";t.setContent(o),c(this.props.onEditorChange)&&t.on("change keyup setcontent",(function(n){var o=t.getContent({format:e.props.outputFormat});o!==e.currentContent&&(e.currentContent=o,c(e.props.onEditorChange)&&e.props.onEditorChange(e.currentContent,t))})),c(this.props.onInit)&&this.props.onInit(n,t),a(t,this.props,this.boundHandlers)},t.prototype.renderInline=function(){var n=this.props.tagName,t=void 0===n?"div":n;return o.createElement(t,{ref:this.elementRef,id:this.id})},t.prototype.renderIframe=function(){return o.createElement("textarea",{ref:this.elementRef,style:{visibility:"hidden"},name:this.props.textareaName,id:this.id})},t.propTypes=s,t.defaultProps={cloudChannel:"5"},t}(o.Component)}}]);
//# sourceMappingURL=@tinymce.1303f62ff00cabeac89d4686ca0d9623.js.map