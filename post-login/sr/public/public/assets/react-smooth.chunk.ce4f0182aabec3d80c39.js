"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-smooth"],{80397:function(t,e,n){n.d(e,{ZP:function(){return it}});var r=n(89526),o=n(2652),i=n.n(o),u=n(32690);function a(t){"undefined"!==typeof requestAnimationFrame&&requestAnimationFrame(t)}function c(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1,r=function r(o){n<0&&(n=o),o-n>e?(t(o),n=-1):a(r)};requestAnimationFrame(r)}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function l(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function y(){var t=function(){return null},e=!1,n=function n(r){if(!e){if(Array.isArray(r)){if(!r.length)return;var o=l(r),i=o[0],u=o.slice(1);return"number"===typeof i?void c(n.bind(null,u),i):(n(i),void c(n.bind(null,u)))}"object"===f(r)&&t(r),"function"===typeof r&&r()}};return{stop:function(){e=!0},start:function(t){e=!1,n(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function b(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?b(Object(n),!0).forEach((function(e){v(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function v(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==p(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===p(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var d=function(t){return t},h=function(t,e){return Object.keys(e).reduce((function(n,r){return m(m({},n),{},v({},r,t(r,e[r])))}),{})},g=function(t,e,n){return t.map((function(t){return"".concat((r=t,r.replace(/([A-Z])/g,(function(t){return"-".concat(t.toLowerCase())})))," ").concat(e,"ms ").concat(n);var r})).join(",")};function O(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,e)||A(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(t){return function(t){if(Array.isArray(t))return j(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||A(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(t,e){if(t){if("string"===typeof t)return j(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?j(t,e):void 0}}function j(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var w=1e-4,P=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},E=function(t,e){return t.map((function(t,n){return t*Math.pow(e,n)})).reduce((function(t,e){return t+e}))},k=function(t,e){return function(n){var r=P(t,e);return E(r,n)}},T=function(t,e){return function(n){var r=P(t,e),o=[].concat(S(r.map((function(t,e){return t*e})).slice(1)),[0]);return E(o,n)}},I=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],o=e[1],i=e[2],u=e[3];if(1===e.length)switch(e[0]){case"linear":r=0,o=0,i=1,u=1;break;case"ease":r=.25,o=.1,i=.25,u=1;break;case"ease-in":r=.42,o=0,i=1,u=1;break;case"ease-out":r=.42,o=0,i=.58,u=1;break;case"ease-in-out":r=0,o=0,i=.58,u=1;break;default:var a=e[0].split("(");if("cubic-bezier"===a[0]&&4===a[1].split(")")[0].split(",").length){var c=a[1].split(")")[0].split(",").map((function(t){return parseFloat(t)})),f=O(c,4);r=f[0],o=f[1],i=f[2],u=f[3]}}[r,i,o,u].every((function(t){return"number"===typeof t&&t>=0&&t<=1}));var l=k(r,i),s=k(o,u),y=T(r,i),p=function(t){return t>1?1:t<0?0:t},b=function(t){for(var e=t>1?1:t,n=e,r=0;r<8;++r){var o=l(n)-e,i=y(n);if(Math.abs(o-e)<w||i<w)return s(n);n=p(n-o/i)}return s(n)};return b.isStepper=!1,b},C=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,n=void 0===e?100:e,r=t.damping,o=void 0===r?8:r,i=t.dt,u=void 0===i?17:i,a=function(t,e,r){var i=r+(-(t-e)*n-r*o)*u/1e3,a=r*u/1e3+t;return Math.abs(a-e)<w&&Math.abs(i)<w?[e,0]:[a,i]};return a.isStepper=!0,a.dt=u,a};function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function R(t){return function(t){if(Array.isArray(t))return F(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||q(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?N(Object(n),!0).forEach((function(e){B(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):N(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function B(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==D(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==D(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===D(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,e)||q(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(t,e){if(t){if("string"===typeof t)return F(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?F(t,e):void 0}}function F(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var J=function(t,e,n){return t+(e-t)*n},x=function(t){return t.from!==t.to},U=function t(e,n,r){var o=h((function(t,n){if(x(n)){var r=M(e(n.from,n.to,n.velocity),2),o=r[0],i=r[1];return _(_({},n),{},{from:o,velocity:i})}return n}),n);return r<1?h((function(t,e){return x(e)?_(_({},e),{},{velocity:J(e.velocity,o[t].velocity,r),from:J(e.from,o[t].from,r)}):e}),n):t(e,o,r-1)},$=function(t,e,n,r,o){var i,u,a,c,f=(i=t,u=e,[Object.keys(i),Object.keys(u)].reduce((function(t,e){return t.filter((function(t){return e.includes(t)}))}))),l=f.reduce((function(n,r){return _(_({},n),{},B({},r,[t[r],e[r]]))}),{}),s=f.reduce((function(n,r){return _(_({},n),{},B({},r,{from:t[r],velocity:0,to:e[r]}))}),{}),y=-1,p=function(){return null};return p=n.isStepper?function(r){a||(a=r);var i=(r-a)/n.dt;s=U(n,s,i),o(_(_(_({},t),e),h((function(t,e){return e.from}),s))),a=r,Object.values(s).filter(x).length&&(y=requestAnimationFrame(p))}:function(i){c||(c=i);var u=(i-c)/r,a=h((function(t,e){return J.apply(void 0,R(e).concat([n(u)]))}),l);if(o(_(_(_({},t),e),a)),u<1)y=requestAnimationFrame(p);else{var f=h((function(t,e){return J.apply(void 0,R(e).concat([n(1)]))}),l);o(_(_(_({},t),e),f))}},function(){return requestAnimationFrame(p),function(){cancelAnimationFrame(y)}}};function Z(t){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Z(t)}var z=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function L(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function W(t){return function(t){if(Array.isArray(t))return G(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return G(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return G(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function H(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function K(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?H(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):H(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Q(t,e,n){return(e=X(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function V(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,X(r.key),r)}}function X(t){var e=function(t,e){if("object"!==Z(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==Z(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Z(e)?e:String(e)}function Y(t,e){return Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Y(t,e)}function tt(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=rt(t);if(e){var o=rt(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return et(this,n)}}function et(t,e){if(e&&("object"===Z(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return nt(t)}function nt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function rt(t){return rt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},rt(t)}var ot=function(t){!function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Y(t,e)}(a,t);var e,n,o,i=tt(a);function a(t,e){var n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a);var r=(n=i.call(this,t,e)).props,o=r.isActive,u=r.attributeName,c=r.from,f=r.to,l=r.steps,s=r.children,y=r.duration;if(n.handleStyleChange=n.handleStyleChange.bind(nt(n)),n.changeStyle=n.changeStyle.bind(nt(n)),!o||y<=0)return n.state={style:{}},"function"===typeof s&&(n.state={style:f}),et(n);if(l&&l.length)n.state={style:l[0].style};else if(c){if("function"===typeof s)return n.state={style:c},et(n);n.state={style:u?Q({},u,c):c}}else n.state={style:{}};return n}return e=a,n=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,n=t.canBegin;this.mounted=!0,e&&n&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,n=e.isActive,r=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,f=this.state.style;if(r)if(n){if(!((0,u.vZ)(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?c:t.to;if(this.state&&f){var y={style:o?Q({},o,s):s};(o&&f[o]!==s||!o&&f!==s)&&this.setState(y)}this.runAnimation(K(K({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?Q({},o,a):a};this.state&&f&&(o&&f[o]!==a||!o&&f!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,n=t.from,r=t.to,o=t.duration,i=t.easing,u=t.begin,a=t.onAnimationEnd,c=t.onAnimationStart,f=$(n,r,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0];if("string"===typeof r)switch(r){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return I(r);case"spring":return C();default:if("cubic-bezier"===r.split("(")[0])return I(r)}return"function"===typeof r?r:null}(i),o,this.changeStyle);this.manager.start([c,u,function(){e.stopJSAnimation=f()},o,a])}},{key:"runStepAnimation",value:function(t){var e=this,n=t.steps,r=t.begin,o=t.onAnimationStart,i=n[0],u=i.style,a=i.duration,c=void 0===a?0:a;return this.manager.start([o].concat(W(n.reduce((function(t,r,o){if(0===o)return t;var i=r.duration,u=r.easing,a=void 0===u?"ease":u,c=r.style,f=r.properties,l=r.onAnimationEnd,s=o>0?n[o-1]:r,y=f||Object.keys(c);if("function"===typeof a||"spring"===a)return[].concat(W(t),[e.runJSAnimation.bind(e,{from:s.style,to:c,duration:i,easing:a}),i]);var p=g(y,i,a),b=K(K(K({},s.style),c),{},{transition:p});return[].concat(W(t),[b,i,l]).filter(d)}),[u,Math.max(c,r)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=y());var e=t.begin,n=t.duration,r=t.attributeName,o=t.to,i=t.easing,u=t.onAnimationStart,a=t.onAnimationEnd,c=t.steps,f=t.children,l=this.manager;if(this.unSubscribe=l.subscribe(this.handleStyleChange),"function"!==typeof i&&"function"!==typeof f&&"spring"!==i)if(c.length>1)this.runStepAnimation(t);else{var s=r?Q({},r,o):o,p=g(Object.keys(s),n,i);l.start([u,e,K(K({},s),{},{transition:p}),n,a])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,e=t.children,n=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,L(t,z)),u=r.Children.count(e),a=this.state.style;if("function"===typeof e)return e(a);if(!o||0===u||n<=0)return e;var c=function(t){var e=t.props,n=e.style,o=void 0===n?{}:n,u=e.className;return(0,r.cloneElement)(t,K(K({},i),{},{style:K(K({},o),a),className:u}))};return 1===u?c(r.Children.only(e)):r.createElement("div",null,r.Children.map(e,(function(t){return c(t)})))}}],n&&V(e.prototype,n),o&&V(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(r.PureComponent);ot.displayName="Animate",ot.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ot.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};var it=ot}}]);
//# sourceMappingURL=react-smooth.fa4b8c343a6034a9e733b237264cc513.js.map