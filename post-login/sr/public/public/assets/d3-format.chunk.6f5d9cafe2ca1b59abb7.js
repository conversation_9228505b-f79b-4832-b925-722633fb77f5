"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["d3-format"],{52253:function(t,n,i){i.d(n,{WU:function(){return l},jH:function(){return f}});var r=i(67470);var e=i(95761);var o,a=i(86061);function s(t,n){var i=(0,a.V)(t,n);if(!i)return t+"";var r=i[0],e=i[1];return e<0?"0."+new Array(-e).join("0")+r:r.length>e+1?r.slice(0,e+1)+"."+r.slice(e+1):r+new Array(e-r.length+2).join("0")}var u={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:a.Z,e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>s(100*t,n),r:s,s:function(t,n){var i=(0,a.V)(t,n);if(!i)return t+"";var r=i[0],e=i[1],s=e-(o=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,u=r.length;return s===u?r:s>u?r+new Array(s-u+1).join("0"):s>0?r.slice(0,s)+"."+r.slice(s):"0."+new Array(1-s).join("0")+(0,a.V)(t,Math.max(0,n+s-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function c(t){return t}var h,l,f,d=Array.prototype.map,m=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function p(t){var n,i,a=void 0===t.grouping||void 0===t.thousands?c:(n=d.call(t.grouping,Number),i=t.thousands+"",function(t,r){for(var e=t.length,o=[],a=0,s=n[0],u=0;e>0&&s>0&&(u+s+1>r&&(s=Math.max(1,r-u)),o.push(t.substring(e-=s,e+s)),!((u+=s+1)>r));)s=n[a=(a+1)%n.length];return o.reverse().join(i)}),s=void 0===t.currency?"":t.currency[0]+"",h=void 0===t.currency?"":t.currency[1]+"",l=void 0===t.decimal?".":t.decimal+"",f=void 0===t.numerals?c:function(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}(d.call(t.numerals,String)),p=void 0===t.percent?"%":t.percent+"",g=void 0===t.minus?"\u2212":t.minus+"",v=void 0===t.nan?"NaN":t.nan+"";function M(t){var n=(t=(0,e.Z)(t)).fill,i=t.align,r=t.sign,c=t.symbol,d=t.zero,M=t.width,y=t.comma,b=t.precision,x=t.trim,w=t.type;"n"===w?(y=!0,w="g"):u[w]||(void 0===b&&(b=12),x=!0,w="g"),(d||"0"===n&&"="===i)&&(d=!0,n="0",i="=");var Z="$"===c?s:"#"===c&&/[boxX]/.test(w)?"0"+w.toLowerCase():"",k="$"===c?h:/[%p]/.test(w)?p:"",S=u[w],j=/[defgprs%]/.test(w);function z(t){var e,s,u,c=Z,h=k;if("c"===w)h=S(t)+h,t="";else{var p=(t=+t)<0||1/t<0;if(t=isNaN(t)?v:S(Math.abs(t),b),x&&(t=function(t){t:for(var n,i=t.length,r=1,e=-1;r<i;++r)switch(t[r]){case".":e=n=r;break;case"0":0===e&&(e=r),n=r;break;default:if(!+t[r])break t;e>0&&(e=0)}return e>0?t.slice(0,e)+t.slice(n+1):t}(t)),p&&0===+t&&"+"!==r&&(p=!1),c=(p?"("===r?r:g:"-"===r||"("===r?"":r)+c,h=("s"===w?m[8+o/3]:"")+h+(p&&"("===r?")":""),j)for(e=-1,s=t.length;++e<s;)if(48>(u=t.charCodeAt(e))||u>57){h=(46===u?l+t.slice(e+1):t.slice(e))+h,t=t.slice(0,e);break}}y&&!d&&(t=a(t,1/0));var z=c.length+t.length+h.length,A=z<M?new Array(M-z+1).join(n):"";switch(y&&d&&(t=a(A+t,A.length?M-h.length:1/0),A=""),i){case"<":t=c+t+h+A;break;case"=":t=c+A+t+h;break;case"^":t=A.slice(0,z=A.length>>1)+c+t+h+A.slice(z);break;default:t=A+c+t+h}return f(t)}return b=void 0===b?6:/[gprs]/.test(w)?Math.max(1,Math.min(21,b)):Math.max(0,Math.min(20,b)),z.toString=function(){return t+""},z}return{format:M,formatPrefix:function(t,n){var i=M(((t=(0,e.Z)(t)).type="f",t)),o=3*Math.max(-8,Math.min(8,Math.floor((0,r.Z)(n)/3))),a=Math.pow(10,-o),s=m[8+o/3];return function(t){return i(a*t)+s}}}}h=p({thousands:",",grouping:[3],currency:["$",""]}),l=h.format,f=h.formatPrefix},67470:function(t,n,i){i.d(n,{Z:function(){return e}});var r=i(86061);function e(t){return(t=(0,r.V)(Math.abs(t)))?t[1]:NaN}},86061:function(t,n,i){function r(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function e(t,n){if((i=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var i,r=t.slice(0,i);return[r.length>1?r[0]+r.slice(2):r,+t.slice(i+1)]}i.d(n,{Z:function(){return r},V:function(){return e}})},95761:function(t,n,i){i.d(n,{Z:function(){return e}});var r=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function e(t){if(!(n=r.exec(t)))throw new Error("invalid format: "+t);var n;return new o({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function o(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}e.prototype=o.prototype,o.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type}},95308:function(t,n,i){i.d(n,{Z:function(){return e}});var r=i(67470);function e(t){return Math.max(0,-(0,r.Z)(Math.abs(t)))}},94198:function(t,n,i){i.d(n,{Z:function(){return e}});var r=i(67470);function e(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor((0,r.Z)(n)/3)))-(0,r.Z)(Math.abs(t)))}},50231:function(t,n,i){i.d(n,{Z:function(){return e}});var r=i(67470);function e(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,(0,r.Z)(n)-(0,r.Z)(t))+1}}}]);
//# sourceMappingURL=d3-format.8a2316b78c3b0bc41e0e606c636a9bd0.js.map