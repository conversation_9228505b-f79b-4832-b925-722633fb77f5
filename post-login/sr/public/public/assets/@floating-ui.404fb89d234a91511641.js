"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@floating-ui"],{14645:function(t,e,n){n.d(e,{x7:function(){return pt},Me:function(){return ct},oo:function(){return ht},RR:function(){return at},Cp:function(){return dt},dr:function(){return mt},cv:function(){return st},uY:function(){return ft},dp:function(){return ut}});const o=["top","right","bottom","left"],r=Math.min,i=Math.max,l=Math.round,c=Math.floor,s=t=>({x:t,y:t}),f={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function u(t,e,n){return i(t,r(e,n))}function d(t,e){return"function"===typeof t?t(e):t}function p(t){return t.split("-")[0]}function m(t){return t.split("-")[1]}function h(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}function y(t){return["top","bottom"].includes(p(t))?"y":"x"}function w(t){return h(y(t))}function x(t){return t.replace(/start|end/g,(t=>a[t]))}function v(t){return t.replace(/left|right|bottom|top/g,(t=>f[t]))}function b(t){return"number"!==typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function R(t){const{x:e,y:n,width:o,height:r}=t;return{width:o,height:r,top:n,left:e,right:e+o,bottom:n+r,x:e,y:n}}function A(t,e,n){let{reference:o,floating:r}=t;const i=y(e),l=w(e),c=g(l),s=p(e),f="y"===i,a=o.x+o.width/2-r.width/2,u=o.y+o.height/2-r.height/2,d=o[c]/2-r[c]/2;let h;switch(s){case"top":h={x:a,y:o.y-r.height};break;case"bottom":h={x:a,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:u};break;case"left":h={x:o.x-r.width,y:u};break;default:h={x:o.x,y:o.y}}switch(m(e)){case"start":h[l]-=d*(n&&f?-1:1);break;case"end":h[l]+=d*(n&&f?-1:1)}return h}async function L(t,e){var n;void 0===e&&(e={});const{x:o,y:r,platform:i,rects:l,elements:c,strategy:s}=t,{boundary:f="clippingAncestors",rootBoundary:a="viewport",elementContext:u="floating",altBoundary:p=!1,padding:m=0}=d(e,t),h=b(m),g=c[p?"floating"===u?"reference":"floating":u],y=R(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(c.floating)),boundary:f,rootBoundary:a,strategy:s})),w="floating"===u?{x:o,y:r,width:l.floating.width,height:l.floating.height}:l.reference,x=await(null==i.getOffsetParent?void 0:i.getOffsetParent(c.floating)),v=await(null==i.isElement?void 0:i.isElement(x))&&await(null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},A=R(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:w,offsetParent:x,strategy:s}):w);return{top:(y.top-A.top+h.top)/v.y,bottom:(A.bottom-y.bottom+h.bottom)/v.y,left:(y.left-A.left+h.left)/v.x,right:(A.right-y.right+h.right)/v.x}}function T(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function E(t){return o.some((e=>t[e]>=0))}function D(){return"undefined"!==typeof window}function C(t){return P(t)?(t.nodeName||"").toLowerCase():"#document"}function O(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function S(t){var e;return null==(e=(P(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function P(t){return!!D()&&(t instanceof Node||t instanceof O(t).Node)}function k(t){return!!D()&&(t instanceof Element||t instanceof O(t).Element)}function F(t){return!!D()&&(t instanceof HTMLElement||t instanceof O(t).HTMLElement)}function H(t){return!(!D()||"undefined"===typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof O(t).ShadowRoot)}function M(t){const{overflow:e,overflowX:n,overflowY:o,display:r}=N(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(r)}function W(t){return["table","td","th"].includes(C(t))}function B(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function V(t){const e=z(),n=k(t)?N(t):t;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function z(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function _(t){return["html","body","#document"].includes(C(t))}function N(t){return O(t).getComputedStyle(t)}function Y(t){return k(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function j(t){if("html"===C(t))return t;const e=t.assignedSlot||t.parentNode||H(t)&&t.host||S(t);return H(e)?e.host:e}function $(t){const e=j(t);return _(e)?t.ownerDocument?t.ownerDocument.body:t.body:F(e)&&M(e)?e:$(e)}function I(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const r=$(t),i=r===(null==(o=t.ownerDocument)?void 0:o.body),l=O(r);if(i){const t=q(l);return e.concat(l,l.visualViewport||[],M(r)?r:[],t&&n?I(t):[])}return e.concat(r,I(r,[],n))}function q(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function X(t){const e=N(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const r=F(t),i=r?t.offsetWidth:n,c=r?t.offsetHeight:o,s=l(n)!==i||l(o)!==c;return s&&(n=i,o=c),{width:n,height:o,$:s}}function G(t){return k(t)?t:t.contextElement}function J(t){const e=G(t);if(!F(e))return s(1);const n=e.getBoundingClientRect(),{width:o,height:r,$:i}=X(e);let c=(i?l(n.width):n.width)/o,f=(i?l(n.height):n.height)/r;return c&&Number.isFinite(c)||(c=1),f&&Number.isFinite(f)||(f=1),{x:c,y:f}}const K=s(0);function Q(t){const e=O(t);return z()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:K}function U(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const r=t.getBoundingClientRect(),i=G(t);let l=s(1);e&&(o?k(o)&&(l=J(o)):l=J(t));const c=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==O(t))&&e}(i,n,o)?Q(i):s(0);let f=(r.left+c.x)/l.x,a=(r.top+c.y)/l.y,u=r.width/l.x,d=r.height/l.y;if(i){const t=O(i),e=o&&k(o)?O(o):o;let n=t,r=q(n);for(;r&&o&&e!==n;){const t=J(r),e=r.getBoundingClientRect(),o=N(r),i=e.left+(r.clientLeft+parseFloat(o.paddingLeft))*t.x,l=e.top+(r.clientTop+parseFloat(o.paddingTop))*t.y;f*=t.x,a*=t.y,u*=t.x,d*=t.y,f+=i,a+=l,n=O(r),r=q(n)}}return R({width:u,height:d,x:f,y:a})}function Z(t,e){const n=Y(t).scrollLeft;return e?e.left+n:U(S(t)).left+n}function tt(t,e,n){let o;if("viewport"===e)o=function(t,e){const n=O(t),o=S(t),r=n.visualViewport;let i=o.clientWidth,l=o.clientHeight,c=0,s=0;if(r){i=r.width,l=r.height;const t=z();(!t||t&&"fixed"===e)&&(c=r.offsetLeft,s=r.offsetTop)}return{width:i,height:l,x:c,y:s}}(t,n);else if("document"===e)o=function(t){const e=S(t),n=Y(t),o=t.ownerDocument.body,r=i(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),l=i(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let c=-n.scrollLeft+Z(t);const s=-n.scrollTop;return"rtl"===N(o).direction&&(c+=i(e.clientWidth,o.clientWidth)-r),{width:r,height:l,x:c,y:s}}(S(t));else if(k(e))o=function(t,e){const n=U(t,!0,"fixed"===e),o=n.top+t.clientTop,r=n.left+t.clientLeft,i=F(t)?J(t):s(1);return{width:t.clientWidth*i.x,height:t.clientHeight*i.y,x:r*i.x,y:o*i.y}}(e,n);else{const n=Q(t);o={...e,x:e.x-n.x,y:e.y-n.y}}return R(o)}function et(t,e){const n=j(t);return!(n===e||!k(n)||_(n))&&("fixed"===N(n).position||et(n,e))}function nt(t,e,n){const o=F(e),r=S(e),i="fixed"===n,l=U(t,!0,i,e);let c={scrollLeft:0,scrollTop:0};const f=s(0);if(o||!o&&!i)if(("body"!==C(e)||M(r))&&(c=Y(e)),o){const t=U(e,!0,i,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else r&&(f.x=Z(r));let a=0,u=0;if(r&&!o&&!i){const t=r.getBoundingClientRect();u=t.top+c.scrollTop,a=t.left+c.scrollLeft-Z(r,t)}return{x:l.left+c.scrollLeft-f.x-a,y:l.top+c.scrollTop-f.y-u,width:l.width,height:l.height}}function ot(t){return"static"===N(t).position}function rt(t,e){if(!F(t)||"fixed"===N(t).position)return null;if(e)return e(t);let n=t.offsetParent;return S(t)===n&&(n=n.ownerDocument.body),n}function it(t,e){const n=O(t);if(B(t))return n;if(!F(t)){let e=j(t);for(;e&&!_(e);){if(k(e)&&!ot(e))return e;e=j(e)}return n}let o=rt(t,e);for(;o&&W(o)&&ot(o);)o=rt(o,e);return o&&_(o)&&ot(o)&&!V(o)?n:o||function(t){let e=j(t);for(;F(e)&&!_(e);){if(V(e))return e;if(B(e))return null;e=j(e)}return null}(t)||n}const lt={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:r}=t;const i="fixed"===r,l=S(o),c=!!e&&B(e.floating);if(o===l||c&&i)return n;let f={scrollLeft:0,scrollTop:0},a=s(1);const u=s(0),d=F(o);if((d||!d&&!i)&&(("body"!==C(o)||M(l))&&(f=Y(o)),F(o))){const t=U(o);a=J(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-f.scrollLeft*a.x+u.x,y:n.y*a.y-f.scrollTop*a.y+u.y}},getDocumentElement:S,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:l}=t;const c="clippingAncestors"===n?B(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=I(t,[],!1).filter((t=>k(t)&&"body"!==C(t))),r=null;const i="fixed"===N(t).position;let l=i?j(t):t;for(;k(l)&&!_(l);){const e=N(l),n=V(l);n||"fixed"!==e.position||(r=null),(i?!n&&!r:!n&&"static"===e.position&&r&&["absolute","fixed"].includes(r.position)||M(l)&&!n&&et(t,l))?o=o.filter((t=>t!==l)):r=e,l=j(l)}return e.set(t,o),o}(e,this._c):[].concat(n),s=[...c,o],f=s[0],a=s.reduce(((t,n)=>{const o=tt(e,n,l);return t.top=i(o.top,t.top),t.right=r(o.right,t.right),t.bottom=r(o.bottom,t.bottom),t.left=i(o.left,t.left),t}),tt(e,f,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:it,getElementRects:async function(t){const e=this.getOffsetParent||it,n=this.getDimensions,o=await n(t.floating);return{reference:nt(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=X(t);return{width:e,height:n}},getScale:J,isElement:k,isRTL:function(t){return"rtl"===N(t).direction}};function ct(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:f="function"===typeof ResizeObserver,layoutShift:a="function"===typeof IntersectionObserver,animationFrame:u=!1}=o,d=G(t),p=l||s?[...d?I(d):[],...I(e)]:[];p.forEach((t=>{l&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)}));const m=d&&a?function(t,e){let n,o=null;const l=S(t);function s(){var t;clearTimeout(n),null==(t=o)||t.disconnect(),o=null}return function f(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),s();const{left:d,top:p,width:m,height:h}=t.getBoundingClientRect();if(a||e(),!m||!h)return;const g={rootMargin:-c(p)+"px "+-c(l.clientWidth-(d+m))+"px "+-c(l.clientHeight-(p+h))+"px "+-c(d)+"px",threshold:i(0,r(1,u))||1};let y=!0;function w(t){const e=t[0].intersectionRatio;if(e!==u){if(!y)return f();e?f(!1,e):n=setTimeout((()=>{f(!1,1e-7)}),1e3)}y=!1}try{o=new IntersectionObserver(w,{...g,root:l.ownerDocument})}catch(x){o=new IntersectionObserver(w,g)}o.observe(t)}(!0),s}(d,n):null;let h,g=-1,y=null;f&&(y=new ResizeObserver((t=>{let[o]=t;o&&o.target===d&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame((()=>{var t;null==(t=y)||t.observe(e)}))),n()})),d&&!u&&y.observe(d),y.observe(e));let w=u?U(t):null;return u&&function e(){const o=U(t);!w||o.x===w.x&&o.y===w.y&&o.width===w.width&&o.height===w.height||n();w=o,h=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach((t=>{l&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)})),null==m||m(),null==(t=y)||t.disconnect(),y=null,u&&cancelAnimationFrame(h)}}const st=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:r,y:i,placement:l,middlewareData:c}=e,s=await async function(t,e){const{placement:n,platform:o,elements:r}=t,i=await(null==o.isRTL?void 0:o.isRTL(r.floating)),l=p(n),c=m(n),s="y"===y(n),f=["left","top"].includes(l)?-1:1,a=i&&s?-1:1,u=d(e,t);let{mainAxis:h,crossAxis:g,alignmentAxis:w}="number"===typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&"number"===typeof w&&(g="end"===c?-1*w:w),s?{x:g*a,y:h*f}:{x:h*f,y:g*a}}(e,t);return l===(null==(n=c.offset)?void 0:n.placement)&&null!=(o=c.arrow)&&o.alignmentOffset?{}:{x:r+s.x,y:i+s.y,data:{...s,placement:l}}}}},ft=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:r}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...s}=d(t,e),f={x:n,y:o},a=await L(e,s),m=y(p(r)),g=h(m);let w=f[g],x=f[m];if(i){const t="y"===g?"bottom":"right";w=u(w+a["y"===g?"top":"left"],w,w-a[t])}if(l){const t="y"===m?"bottom":"right";x=u(x+a["y"===m?"top":"left"],x,x-a[t])}const v=c.fn({...e,[g]:w,[m]:x});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[g]:i,[m]:l}}}}}},at=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:r,middlewareData:i,rects:l,initialPlacement:c,platform:s,elements:f}=e,{mainAxis:a=!0,crossAxis:u=!0,fallbackPlacements:h,fallbackStrategy:b="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:A=!0,...T}=d(t,e);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const E=p(r),D=y(c),C=p(c)===c,O=await(null==s.isRTL?void 0:s.isRTL(f.floating)),S=h||(C||!A?[v(c)]:function(t){const e=v(t);return[x(t),e,x(e)]}(c)),P="none"!==R;!h&&P&&S.push(...function(t,e,n,o){const r=m(t);let i=function(t,e,n){const o=["left","right"],r=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(t){case"top":case"bottom":return n?e?r:o:e?o:r;case"left":case"right":return e?i:l;default:return[]}}(p(t),"start"===n,o);return r&&(i=i.map((t=>t+"-"+r)),e&&(i=i.concat(i.map(x)))),i}(c,A,R,O));const k=[c,...S],F=await L(e,T),H=[];let M=(null==(o=i.flip)?void 0:o.overflows)||[];if(a&&H.push(F[E]),u){const t=function(t,e,n){void 0===n&&(n=!1);const o=m(t),r=w(t),i=g(r);let l="x"===r?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[i]>e.floating[i]&&(l=v(l)),[l,v(l)]}(r,l,O);H.push(F[t[0]],F[t[1]])}if(M=[...M,{placement:r,overflows:H}],!H.every((t=>t<=0))){var W,B;const t=((null==(W=i.flip)?void 0:W.index)||0)+1,e=k[t];if(e)return{data:{index:t,overflows:M},reset:{placement:e}};let n=null==(B=M.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:B.placement;if(!n)switch(b){case"bestFit":{var V;const t=null==(V=M.filter((t=>{if(P){const e=y(t.placement);return e===D||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:V[0];t&&(n=t);break}case"initialPlacement":n=c}if(r!==n)return{reset:{placement:n}}}return{}}}},ut=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:l,rects:c,platform:s,elements:f}=e,{apply:a=(()=>{}),...u}=d(t,e),h=await L(e,u),g=p(l),w=m(l),x="y"===y(l),{width:v,height:b}=c.floating;let R,A;"top"===g||"bottom"===g?(R=g,A=w===(await(null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(A=g,R="end"===w?"top":"bottom");const T=b-h.top-h.bottom,E=v-h.left-h.right,D=r(b-h[R],T),C=r(v-h[A],E),O=!e.middlewareData.shift;let S=D,P=C;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(P=E),null!=(o=e.middlewareData.shift)&&o.enabled.y&&(S=T),O&&!w){const t=i(h.left,0),e=i(h.right,0),n=i(h.top,0),o=i(h.bottom,0);x?P=v-2*(0!==t||0!==e?t+e:i(h.left,h.right)):S=b-2*(0!==n||0!==o?n+o:i(h.top,h.bottom))}await a({...e,availableWidth:P,availableHeight:S});const k=await s.getDimensions(f.floating);return v!==k.width||b!==k.height?{reset:{rects:!0}}:{}}}},dt=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:o="referenceHidden",...r}=d(t,e);switch(o){case"referenceHidden":{const t=T(await L(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:E(t)}}}case"escaped":{const t=T(await L(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:E(t)}}}default:return{}}}}},pt=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:l,platform:c,elements:s,middlewareData:f}=e,{element:a,padding:p=0}=d(t,e)||{};if(null==a)return{};const h=b(p),y={x:n,y:o},x=w(i),v=g(x),R=await c.getDimensions(a),A="y"===x,L=A?"top":"left",T=A?"bottom":"right",E=A?"clientHeight":"clientWidth",D=l.reference[v]+l.reference[x]-y[x]-l.floating[v],C=y[x]-l.reference[x],O=await(null==c.getOffsetParent?void 0:c.getOffsetParent(a));let S=O?O[E]:0;S&&await(null==c.isElement?void 0:c.isElement(O))||(S=s.floating[E]||l.floating[v]);const P=D/2-C/2,k=S/2-R[v]/2-1,F=r(h[L],k),H=r(h[T],k),M=F,W=S-R[v]-H,B=S/2-R[v]/2+P,V=u(M,B,W),z=!f.arrow&&null!=m(i)&&B!==V&&l.reference[v]/2-(B<M?F:H)-R[v]/2<0,_=z?B<M?B-M:B-W:0;return{[x]:y[x]+_,data:{[x]:V,centerOffset:B-V-_,...z&&{alignmentOffset:_}},reset:z}}}),mt=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:r,rects:i,middlewareData:l}=e,{offset:c=0,mainAxis:s=!0,crossAxis:f=!0}=d(t,e),a={x:n,y:o},u=y(r),m=h(u);let g=a[m],w=a[u];const x=d(c,e),v="number"===typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(s){const t="y"===m?"height":"width",e=i.reference[m]-i.floating[t]+v.mainAxis,n=i.reference[m]+i.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(f){var b,R;const t="y"===m?"width":"height",e=["top","left"].includes(p(r)),n=i.reference[u]-i.floating[t]+(e&&(null==(b=l.offset)?void 0:b[u])||0)+(e?0:v.crossAxis),o=i.reference[u]+i.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[u])||0)-(e?v.crossAxis:0);w<n?w=n:w>o&&(w=o)}return{[m]:g,[u]:w}}}},ht=(t,e,n)=>{const o=new Map,r={platform:lt,...n},i={...r.platform,_c:o};return(async(t,e,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:l}=n,c=i.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(e));let f=await l.getElementRects({reference:t,floating:e,strategy:r}),{x:a,y:u}=A(f,o,s),d=o,p={},m=0;for(let h=0;h<c.length;h++){const{name:n,fn:i}=c[h],{x:g,y:y,data:w,reset:x}=await i({x:a,y:u,initialPlacement:o,placement:d,strategy:r,middlewareData:p,rects:f,platform:l,elements:{reference:t,floating:e}});a=null!=g?g:a,u=null!=y?y:u,p={...p,[n]:{...p[n],...w}},x&&m<=50&&(m++,"object"===typeof x&&(x.placement&&(d=x.placement),x.rects&&(f=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:r}):x.rects),({x:a,y:u}=A(f,d,s))),h=-1)}return{x:a,y:u,placement:d,strategy:r,middlewareData:p}})(t,e,{...r,platform:i})}},1371:function(t,e,n){n.d(e,{x7:function(){return x},RR:function(){return g},Cp:function(){return w},dr:function(){return h},cv:function(){return p},uY:function(){return m},dp:function(){return y},YF:function(){return u}});var o=n(14645),r=n(89526),i=n(73961),l="undefined"!==typeof document?r.useLayoutEffect:r.useEffect;function c(t,e){if(t===e)return!0;if(typeof t!==typeof e)return!1;if("function"===typeof t&&t.toString()===e.toString())return!0;let n,o,r;if(t&&e&&"object"===typeof t){if(Array.isArray(t)){if(n=t.length,n!==e.length)return!1;for(o=n;0!==o--;)if(!c(t[o],e[o]))return!1;return!0}if(r=Object.keys(t),n=r.length,n!==Object.keys(e).length)return!1;for(o=n;0!==o--;)if(!{}.hasOwnProperty.call(e,r[o]))return!1;for(o=n;0!==o--;){const n=r[o];if(("_owner"!==n||!t.$$typeof)&&!c(t[n],e[n]))return!1}return!0}return t!==t&&e!==e}function s(t){if("undefined"===typeof window)return 1;return(t.ownerDocument.defaultView||window).devicePixelRatio||1}function f(t,e){const n=s(t);return Math.round(e*n)/n}function a(t){const e=r.useRef(t);return l((()=>{e.current=t})),e}function u(t){void 0===t&&(t={});const{placement:e="bottom",strategy:n="absolute",middleware:u=[],platform:d,elements:{reference:p,floating:m}={},transform:h=!0,whileElementsMounted:g,open:y}=t,[w,x]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=r.useState(u);c(v,u)||b(u);const[R,A]=r.useState(null),[L,T]=r.useState(null),E=r.useCallback((t=>{t!==S.current&&(S.current=t,A(t))}),[]),D=r.useCallback((t=>{t!==P.current&&(P.current=t,T(t))}),[]),C=p||R,O=m||L,S=r.useRef(null),P=r.useRef(null),k=r.useRef(w),F=null!=g,H=a(g),M=a(d),W=a(y),B=r.useCallback((()=>{if(!S.current||!P.current)return;const t={placement:e,strategy:n,middleware:v};M.current&&(t.platform=M.current),(0,o.oo)(S.current,P.current,t).then((t=>{const e={...t,isPositioned:!1!==W.current};V.current&&!c(k.current,e)&&(k.current=e,i.flushSync((()=>{x(e)})))}))}),[v,e,n,M,W]);l((()=>{!1===y&&k.current.isPositioned&&(k.current.isPositioned=!1,x((t=>({...t,isPositioned:!1}))))}),[y]);const V=r.useRef(!1);l((()=>(V.current=!0,()=>{V.current=!1})),[]),l((()=>{if(C&&(S.current=C),O&&(P.current=O),C&&O){if(H.current)return H.current(C,O,B);B()}}),[C,O,B,H,F]);const z=r.useMemo((()=>({reference:S,floating:P,setReference:E,setFloating:D})),[E,D]),_=r.useMemo((()=>({reference:C,floating:O})),[C,O]),N=r.useMemo((()=>{const t={position:n,left:0,top:0};if(!_.floating)return t;const e=f(_.floating,w.x),o=f(_.floating,w.y);return h?{...t,transform:"translate("+e+"px, "+o+"px)",...s(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:o}}),[n,h,_.floating,w.x,w.y]);return r.useMemo((()=>({...w,update:B,refs:z,elements:_,floatingStyles:N})),[w,B,z,_,N])}const d=t=>({name:"arrow",options:t,fn(e){const{element:n,padding:r}="function"===typeof t?t(e):t;return n&&(i=n,{}.hasOwnProperty.call(i,"current"))?null!=n.current?(0,o.x7)({element:n.current,padding:r}).fn(e):{}:n?(0,o.x7)({element:n,padding:r}).fn(e):{};var i}}),p=(t,e)=>({...(0,o.cv)(t),options:[t,e]}),m=(t,e)=>({...(0,o.uY)(t),options:[t,e]}),h=(t,e)=>({...(0,o.dr)(t),options:[t,e]}),g=(t,e)=>({...(0,o.RR)(t),options:[t,e]}),y=(t,e)=>({...(0,o.dp)(t),options:[t,e]}),w=(t,e)=>({...(0,o.Cp)(t),options:[t,e]}),x=(t,e)=>({...d(t),options:[t,e]})}}]);
//# sourceMappingURL=@floating-ui.4fbe2c2ff57af39a0a9a57bdb1aa3ea2.js.map