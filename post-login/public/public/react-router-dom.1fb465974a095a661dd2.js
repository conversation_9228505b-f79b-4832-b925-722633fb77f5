"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-router-dom"],{16478:function(e,t,n){n.d(t,{VK:function(){return p},rU:function(){return d}});var r=n(78469);function o(e,t){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},o(e,t)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,o(e,t)}var i=n(89526),c=n(16420);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}function l(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}var f=n(78109),p=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).history=(0,c.lX)(t.props),t}return a(t,e),t.prototype.render=function(){return i.createElement(r.F0,{history:this.history,children:this.props.children})},t}(i.Component);i.Component;var s=function(e,t){return"function"===typeof e?e(t):e},v=function(e,t){return"string"===typeof e?(0,c.ob)(e,null,null,t):e},y=function(e){return e},h=i.forwardRef;"undefined"===typeof h&&(h=y);var m=h((function(e,t){var n=e.innerRef,r=e.navigate,o=e.onClick,a=l(e,["innerRef","navigate","onClick"]),c=a.target,f=u({},a,{onClick:function(e){try{o&&o(e)}catch(t){throw e.preventDefault(),t}e.defaultPrevented||0!==e.button||c&&"_self"!==c||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)||(e.preventDefault(),r())}});return f.ref=y!==h&&t||n,i.createElement("a",f)}));var d=h((function(e,t){var n=e.component,o=void 0===n?m:n,a=e.replace,c=e.to,p=e.innerRef,d=l(e,["component","replace","to","innerRef"]);return i.createElement(r.s6.Consumer,null,(function(e){e||(0,f.Z)(!1);var n=e.history,r=v(s(c,e.location),e.location),l=r?n.createHref(r):"",m=u({},d,{href:l,navigate:function(){var t=s(c,e.location);(a?n.replace:n.push)(t)}});return y!==h?m.ref=t||p:m.innerRef=p,i.createElement(o,m)}))})),b=function(e){return e},g=i.forwardRef;"undefined"===typeof g&&(g=b);g((function(e,t){var n=e["aria-current"],o=void 0===n?"page":n,a=e.activeClassName,c=void 0===a?"active":a,p=e.activeStyle,y=e.className,h=e.exact,m=e.isActive,O=e.location,C=e.strict,R=e.style,w=e.to,_=e.innerRef,j=l(e,["aria-current","activeClassName","activeStyle","className","exact","isActive","location","strict","style","to","innerRef"]);return i.createElement(r.s6.Consumer,null,(function(e){e||(0,f.Z)(!1);var n=O||e.location,a=v(s(w,n),n),l=a.pathname,k=l&&l.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1"),E=k?(0,r.LX)(n.pathname,{path:k,exact:h,strict:C}):null,K=!!(m?m(E,n):E),N=K?function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((function(e){return e})).join(" ")}(y,c):y,P=K?u({},R,{},p):R,x=u({"aria-current":K&&o||null,className:N,style:P,to:a},j);return b!==g?x.ref=t||_:x.innerRef=_,i.createElement(d,x)}))}))}}]);
//# sourceMappingURL=react-router-dom.9e6d78a5c527be3746d14ca6edc176a6.js.map