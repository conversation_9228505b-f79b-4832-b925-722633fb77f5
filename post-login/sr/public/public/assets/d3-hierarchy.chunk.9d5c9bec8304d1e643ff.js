"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["d3-hierarchy"],{93804:function(n,t,r){function e(n){var t=0,r=n.children,e=r&&r.length;if(e)for(;--e>=0;)t+=r[e].value;else t=1;n.value=t}function i(n,t){n instanceof Map?(n=[void 0,n],void 0===t&&(t=u)):void 0===t&&(t=o);for(var r,e,i,h,f,l=new c(n),p=[l];r=p.pop();)if((i=t(r.data))&&(f=(i=Array.from(i)).length))for(r.children=i,h=f-1;h>=0;--h)p.push(e=i[h]=new c(i[h])),e.parent=r,e.depth=r.depth+1;return l.eachBefore(a)}function o(n){return n.children}function u(n){return Array.isArray(n)?n[1]:null}function h(n){void 0!==n.data.value&&(n.value=n.data.value),n.data=n.data.data}function a(n){var t=0;do{n.height=t}while((n=n.parent)&&n.height<++t)}function c(n){this.data=n,this.depth=this.height=0,this.parent=null}r.d(t,{NB:function(){return c},le:function(){return a}}),c.prototype=i.prototype={constructor:c,count:function(){return this.eachAfter(e)},each:function(n,t){let r=-1;for(const e of this)n.call(t,e,++r,this);return this},eachAfter:function(n,t){for(var r,e,i,o=this,u=[o],h=[],a=-1;o=u.pop();)if(h.push(o),r=o.children)for(e=0,i=r.length;e<i;++e)u.push(r[e]);for(;o=h.pop();)n.call(t,o,++a,this);return this},eachBefore:function(n,t){for(var r,e,i=this,o=[i],u=-1;i=o.pop();)if(n.call(t,i,++u,this),r=i.children)for(e=r.length-1;e>=0;--e)o.push(r[e]);return this},find:function(n,t){let r=-1;for(const e of this)if(n.call(t,e,++r,this))return e},sum:function(n){return this.eachAfter((function(t){for(var r=+n(t.data)||0,e=t.children,i=e&&e.length;--i>=0;)r+=e[i].value;t.value=r}))},sort:function(n){return this.eachBefore((function(t){t.children&&t.children.sort(n)}))},path:function(n){for(var t=this,r=function(n,t){if(n===t)return n;var r=n.ancestors(),e=t.ancestors(),i=null;n=r.pop(),t=e.pop();for(;n===t;)i=n,n=r.pop(),t=e.pop();return i}(t,n),e=[t];t!==r;)t=t.parent,e.push(t);for(var i=e.length;n!==r;)e.splice(i,0,n),n=n.parent;return e},ancestors:function(){for(var n=this,t=[n];n=n.parent;)t.push(n);return t},descendants:function(){return Array.from(this)},leaves:function(){var n=[];return this.eachBefore((function(t){t.children||n.push(t)})),n},links:function(){var n=this,t=[];return n.each((function(r){r!==n&&t.push({source:r.parent,target:r})})),t},copy:function(){return i(this).eachBefore(h)},[Symbol.iterator]:function*(){var n,t,r,e,i=this,o=[i];do{for(n=o.reverse(),o=[];i=n.pop();)if(yield i,t=i.children)for(r=0,e=t.length;r<e;++r)o.push(t[r])}while(o.length)}}},7520:function(n,t,r){function e(n){return null==n?null:function(n){if("function"!==typeof n)throw new Error;return n}(n)}r.d(t,{Z:function(){return f}});var i=r(93804),o={depth:-1},u={},h={};function a(n){return n.id}function c(n){return n.parentId}function f(){var n,t=a,r=c;function f(e){var a,c,f,s,d,v,g,m,w=Array.from(e),y=t,z=r,B=new Map;if(null!=n){const t=w.map(((t,r)=>function(n){n=`${n}`;let t=n.length;p(n,t-1)&&!p(n,t-2)&&(n=n.slice(0,-1));return"/"===n[0]?n:`/${n}`}(n(t,r,e)))),r=t.map(l),i=new Set(t).add("");for(const n of r)i.has(n)||(i.add(n),t.push(n),r.push(l(n)),w.push(h));y=(n,r)=>t[r],z=(n,t)=>r[t]}for(f=0,a=w.length;f<a;++f)c=w[f],v=w[f]=new i.NB(c),null!=(g=y(c,f,e))&&(g+="")&&(m=v.id=g,B.set(m,B.has(m)?u:v)),null!=(g=z(c,f,e))&&(g+="")&&(v.parent=g);for(f=0;f<a;++f)if(g=(v=w[f]).parent){if(!(d=B.get(g)))throw new Error("missing: "+g);if(d===u)throw new Error("ambiguous: "+g);d.children?d.children.push(v):d.children=[v],v.parent=d}else{if(s)throw new Error("multiple roots");s=v}if(!s)throw new Error("no root");if(null!=n){for(;s.data===h&&1===s.children.length;)s=s.children[0],--a;for(let n=w.length-1;n>=0&&(v=w[n]).data===h;--n)v.data=null}if(s.parent=o,s.eachBefore((function(n){n.depth=n.parent.depth+1,--a})).eachBefore(i.le),s.parent=null,a>0)throw new Error("cycle");return s}return f.id=function(n){return arguments.length?(t=e(n),f):t},f.parentId=function(n){return arguments.length?(r=e(n),f):r},f.path=function(t){return arguments.length?(n=e(t),f):n},f}function l(n){let t=n.length;if(t<2)return"";for(;--t>1&&!p(n,t););return n.slice(0,t)}function p(n,t){if("/"===n[t]){let r=0;for(;t>0&&"\\"===n[--t];)++r;if(0===(1&r))return!0}return!1}},74619:function(n,t,r){r.d(t,{Z:function(){return f}});var e=r(93804);function i(n,t){return n.parent===t.parent?1:2}function o(n){var t=n.children;return t?t[0]:n.t}function u(n){var t=n.children;return t?t[t.length-1]:n.t}function h(n,t,r){var e=r/(t.i-n.i);t.c-=e,t.s+=r,n.c+=e,t.z+=r,t.m+=r}function a(n,t,r){return n.a.parent===t.parent?n.a:r}function c(n,t){this._=n,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=t}function f(){var n=i,t=1,r=1,e=null;function f(i){var o=function(n){for(var t,r,e,i,o,u=new c(n,0),h=[u];t=h.pop();)if(e=t._.children)for(t.children=new Array(o=e.length),i=o-1;i>=0;--i)h.push(r=t.children[i]=new c(e[i],i)),r.parent=t;return(u.parent=new c(null,0)).children=[u],u}(i);if(o.eachAfter(l),o.parent.m=-o.z,o.eachBefore(p),e)i.eachBefore(s);else{var u=i,h=i,a=i;i.eachBefore((function(n){n.x<u.x&&(u=n),n.x>h.x&&(h=n),n.depth>a.depth&&(a=n)}));var f=u===h?1:n(u,h)/2,d=f-u.x,v=t/(h.x+f+d),g=r/(a.depth||1);i.eachBefore((function(n){n.x=(n.x+d)*v,n.y=n.depth*g}))}return i}function l(t){var r=t.children,e=t.parent.children,i=t.i?e[t.i-1]:null;if(r){!function(n){for(var t,r=0,e=0,i=n.children,o=i.length;--o>=0;)(t=i[o]).z+=r,t.m+=r,r+=t.s+(e+=t.c)}(t);var c=(r[0].z+r[r.length-1].z)/2;i?(t.z=i.z+n(t._,i._),t.m=t.z-c):t.z=c}else i&&(t.z=i.z+n(t._,i._));t.parent.A=function(t,r,e){if(r){for(var i,c=t,f=t,l=r,p=c.parent.children[0],s=c.m,d=f.m,v=l.m,g=p.m;l=u(l),c=o(c),l&&c;)p=o(p),(f=u(f)).a=t,(i=l.z+v-c.z-s+n(l._,c._))>0&&(h(a(l,t,e),t,i),s+=i,d+=i),v+=l.m,s+=c.m,g+=p.m,d+=f.m;l&&!u(f)&&(f.t=l,f.m+=v-d),c&&!o(p)&&(p.t=c,p.m+=s-g,e=t)}return e}(t,i,t.parent.A||e[0])}function p(n){n._.x=n.z+n.parent.m,n.m+=n.parent.m}function s(n){n.x*=t,n.y=n.depth*r}return f.separation=function(t){return arguments.length?(n=t,f):n},f.size=function(n){return arguments.length?(e=!1,t=+n[0],r=+n[1],f):e?null:[t,r]},f.nodeSize=function(n){return arguments.length?(e=!0,t=+n[0],r=+n[1],f):e?[t,r]:null},f}c.prototype=Object.create(e.NB.prototype)}}]);
//# sourceMappingURL=d3-hierarchy.b1dd55d9e930f70272920467b4469768.js.map