"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["react-virtuoso"],{26246:function(e,t,o){o.d(t,{OO:function(){return ao}});var n=o(67557),r=o(89526),i=o(73961);function l(e){return()=>e}function s(e){e()}function c(e,t){return o=>e(t(o))}function a(e,t){return()=>e(t)}function u(e){return void 0!==e}function d(){}function h(e,t){return t(e),e}function m(e,t){return t(e)}function f(...e){return e}function p(e,t){return e(1,t)}function g(e,t){e(0,t)}function x(e){e(2)}function v(e){return e(4)}function I(e,t){return p(e,function(e,t){return o=>e(t,o)}(t,0))}function T(e,t){const o=e(1,(e=>{o(),t(e)}));return o}function S(e){let t,o;return n=>r=>{t=r,o&&clearTimeout(o),o=setTimeout((()=>{n(t)}),e)}}function w(e,t){return e===t}function C(e=w){let t;return o=>n=>{e(t,n)||(t=n,o(n))}}function H(e){return t=>o=>{e(o)&&t(o)}}function y(e){return t=>c(t,e)}function b(e){return t=>()=>{t(e)}}function z(e,...t){const o=function(...e){return t=>e.reduceRight(m,t)}(...t);return(t,n)=>{switch(t){case 2:return void x(e);case 1:return p(e,o(n))}}}function R(e,t){return o=>n=>{o(t=e(t,n))}}function B(e){return t=>o=>{e>0?e--:t(o)}}function E(e){let t,o=null;return n=>r=>{o=r,!t&&(t=setTimeout((()=>{t=void 0,n(o)}),e))}}function k(...e){const t=new Array(e.length);let o=0,n=null;const r=Math.pow(2,e.length)-1;return e.forEach(((e,i)=>{const l=Math.pow(2,i);p(e,(e=>{const s=o;o|=l,t[i]=e,s!==r&&o===r&&n&&(n(),n=null)}))})),e=>i=>{const l=()=>{e([i].concat(t))};o===r?l():n=l}}function L(e){let t=e;const o=O();return(e,n)=>{switch(e){case 0:t=n;break;case 1:n(t);break;case 4:return t}return o(e,n)}}function F(e,t){return h(L(t),(t=>I(e,t)))}function O(){const e=[];return(t,o)=>{switch(t){case 0:return void e.slice().forEach((e=>{e(o)}));case 2:return void e.splice(0,e.length);case 1:return e.push(o),()=>{const t=e.indexOf(o);t>-1&&e.splice(t,1)}}}}function j(e){return h(O(),(t=>I(e,t)))}function M(e,t=[],{singleton:o}={singleton:!0}){return{constructor:e,dependencies:t,id:W(),singleton:o}}const W=()=>Symbol();function P(...e){const t=O(),o=new Array(e.length);let n=0;const r=Math.pow(2,e.length)-1;return e.forEach(((e,i)=>{const l=Math.pow(2,i);p(e,(e=>{o[i]=e,n|=l,n===r&&g(t,o)}))})),function(e,i){switch(e){case 2:return void x(t);case 1:return n===r&&i(o),p(t,i)}}}function A(e,t=w){return z(e,C(t))}function V(...e){return function(t,o){switch(t){case 2:return;case 1:return function(...e){return()=>{e.map(s)}}(...e.map((e=>p(e,o))))}}}var D=(e=>(e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e))(D||{});const G={0:"debug",3:"error",1:"log",2:"warn"},N=M((()=>{const e=L(3);return{log:L(((t,o,n=1)=>{var r;n>=(null!=(r=(typeof globalThis>"u"?window:globalThis).VIRTUOSO_LOG_LEVEL)?r:v(e))&&console[G[n]]("%creact-virtuoso: %c%s %o","color: #0253b3; font-weight: bold","color: initial",t,o)})),logLevel:e}}),[],{singleton:!0});function _(e,t,o){return U(e,t,o).callbackRef}function U(e,t,o){const n=r.useRef(null);let i=e=>{};const l=r.useMemo((()=>typeof ResizeObserver<"u"?new ResizeObserver((t=>{const n=()=>{const o=t[0].target;null!==o.offsetParent&&e(o)};o?n():requestAnimationFrame(n)})):null),[e,o]);return i=e=>{e&&t?(null==l||l.observe(e),n.current=e):(n.current&&(null==l||l.unobserve(n.current)),n.current=null)},{callbackRef:i,ref:n}}function K(e,t,o,n,i,l,s,c,a){const u=r.useCallback((o=>{const r=function(e,t,o,n){const r=e.length;if(0===r)return null;const i=[];for(let l=0;l<r;l++){const r=e.item(l);if(void 0===r.dataset.index)continue;const s=parseInt(r.dataset.index),c=parseFloat(r.dataset.knownSize),a=t(r,o);if(0===a&&n("Zero-sized element, this should not happen",{child:r},D.ERROR),a===c)continue;const u=i[i.length-1];0===i.length||u.size!==a||u.endIndex!==s-1?i.push({endIndex:s,size:a,startIndex:s}):i[i.length-1].endIndex++}return i}(o.children,t,c?"offsetWidth":"offsetHeight",i);let a=o.parentElement;for(;!a.dataset.virtuosoScroller;)a=a.parentElement;const u="window"===a.lastElementChild.dataset.viewportType;let d;u&&(d=a.ownerDocument.defaultView);const h=s?c?s.scrollLeft:s.scrollTop:u?c?d.scrollX||d.document.documentElement.scrollLeft:d.scrollY||d.document.documentElement.scrollTop:c?a.scrollLeft:a.scrollTop,m=s?c?s.scrollWidth:s.scrollHeight:u?c?d.document.documentElement.scrollWidth:d.document.documentElement.scrollHeight:c?a.scrollWidth:a.scrollHeight,f=s?c?s.offsetWidth:s.offsetHeight:u?c?d.innerWidth:d.innerHeight:c?a.offsetWidth:a.offsetHeight;n({scrollHeight:m,scrollTop:Math.max(h,0),viewportHeight:f}),null==l||l(c?q("column-gap",getComputedStyle(o).columnGap,i):q("row-gap",getComputedStyle(o).rowGap,i)),null!==r&&e(r)}),[e,t,i,l,s,n,c]);return U(u,o,a)}function q(e,t,o){return"normal"!==t&&!(null!=t&&t.endsWith("px"))&&o(`${e} was not resolved to pixel value correctly`,t,D.WARN),"normal"===t?0:parseInt(null!=t?t:"0",10)}function $(e,t,o){const n=r.useRef(null),i=r.useCallback((o=>{if(null==o||!o.offsetParent)return;const r=o.getBoundingClientRect(),i=r.width;let l,c;if(t){const e=t.getBoundingClientRect(),o=r.top-e.top;c=e.height-Math.max(0,o),l=o+t.scrollTop}else{const e=s.current.ownerDocument.defaultView;c=e.innerHeight-Math.max(0,r.top),l=r.top+e.scrollY}n.current={offsetTop:l,visibleHeight:c,visibleWidth:i},e(n.current)}),[e,t]),{callbackRef:l,ref:s}=U(i,!0,o),c=r.useCallback((()=>{i(s.current)}),[i,s]);return r.useEffect((()=>{var e;if(t){t.addEventListener("scroll",c);const e=new ResizeObserver((()=>{requestAnimationFrame(c)}));return e.observe(t),()=>{t.removeEventListener("scroll",c),e.unobserve(t)}}{const t=null==(e=s.current)?void 0:e.ownerDocument.defaultView;return null==t||t.addEventListener("scroll",c),null==t||t.addEventListener("resize",c),()=>{null==t||t.removeEventListener("scroll",c),null==t||t.removeEventListener("resize",c)}}}),[c,t,s]),l}const Y=M((()=>{const e=O(),t=O(),o=L(0),n=O(),r=L(0),i=O(),l=O(),s=L(0),c=L(0),a=L(0),u=L(0),d=O(),h=O(),m=L(!1),f=L(!1),p=L(!1);return I(z(e,y((({scrollTop:e})=>e))),t),I(z(e,y((({scrollHeight:e})=>e))),l),I(t,r),{deviation:o,fixedFooterHeight:a,fixedHeaderHeight:c,footerHeight:u,headerHeight:s,horizontalDirection:f,scrollBy:h,scrollContainerState:e,scrollHeight:l,scrollingInProgress:m,scrollTo:d,scrollTop:t,skipAnimationFrameInResizeObserver:p,smoothScrollTargetReached:n,statefulScrollTop:r,viewportHeight:i}}),[],{singleton:!0}),X={lvl:0};function Z(e,t){const o=e.length;if(0===o)return[];let{index:n,value:r}=t(e[0]);const i=[];for(let l=1;l<o;l++){const{index:o,value:s}=t(e[l]);i.push({end:o-1,start:n,value:r}),n=o,r=s}return i.push({end:1/0,start:n,value:r}),i}function J(e){return e===X}function Q(e,t){if(!J(e))return t===e.k?e.v:t<e.k?Q(e.l,t):Q(e.r,t)}function ee(e,t,o="k"){if(J(e))return[-1/0,void 0];if(Number(e[o])===t)return[e.k,e.v];if(Number(e[o])<t){const n=ee(e.r,t,o);return n[0]===-1/0?[e.k,e.v]:n}return ee(e.l,t,o)}function te(e,t,o){return J(e)?he(t,o,1):t===e.k?ce(e,{k:t,v:o}):t<e.k?me(ce(e,{l:te(e.l,t,o)})):me(ce(e,{r:te(e.r,t,o)}))}function oe(){return X}function ne(e,t,o){if(J(e))return[];return function(e){return Z(e,(({k:e,v:t})=>({index:e,value:t})))}(le(e,ee(e,t)[0],o))}function re(e,t){if(J(e))return X;const{k:o,l:n,r:r}=e;if(t===o){if(J(n))return r;if(J(r))return n;{const[t,o]=de(n);return se(ce(e,{k:t,l:ae(n),v:o}))}}return se(ce(e,t<o?{l:re(n,t)}:{r:re(r,t)}))}function ie(e){return J(e)?[]:[...ie(e.l),{k:e.k,v:e.v},...ie(e.r)]}function le(e,t,o){if(J(e))return[];const{k:n,l:r,r:i,v:l}=e;let s=[];return n>t&&(s=s.concat(le(r,t,o))),n>=t&&n<=o&&s.push({k:n,v:l}),n<=o&&(s=s.concat(le(i,t,o))),s}function se(e){const{l:t,lvl:o,r:n}=e;if(n.lvl>=o-1&&t.lvl>=o-1)return e;if(o>n.lvl+1){if(ue(t))return fe(ce(e,{lvl:o-1}));if(!J(t)&&!J(t.r))return ce(t.r,{l:ce(t,{r:t.r.l}),lvl:o,r:ce(e,{l:t.r.r,lvl:o-1})});throw new Error("Unexpected empty nodes")}if(ue(e))return pe(ce(e,{lvl:o-1}));if(J(n)||J(n.l))throw new Error("Unexpected empty nodes");{const t=n.l,r=ue(t)?n.lvl-1:n.lvl;return ce(t,{l:ce(e,{lvl:o-1,r:t.l}),lvl:t.lvl+1,r:pe(ce(n,{l:t.r,lvl:r}))})}}function ce(e,t){return he(void 0!==t.k?t.k:e.k,void 0!==t.v?t.v:e.v,void 0!==t.lvl?t.lvl:e.lvl,void 0!==t.l?t.l:e.l,void 0!==t.r?t.r:e.r)}function ae(e){return J(e.r)?e.l:se(ce(e,{r:ae(e.r)}))}function ue(e){return J(e)||e.lvl>e.r.lvl}function de(e){return J(e.r)?[e.k,e.v]:de(e.r)}function he(e,t,o,n=X,r=X){return{k:e,l:n,lvl:o,r:r,v:t}}function me(e){return pe(fe(e))}function fe(e){const{l:t}=e;return J(t)||t.lvl!==e.lvl?e:ce(t,{r:ce(e,{l:t.r})})}function pe(e){const{lvl:t,r:o}=e;return J(o)||J(o.r)||o.lvl!==t||o.r.lvl!==t?e:ce(o,{l:ce(e,{r:o.l}),lvl:t+1})}function ge(e,t){return!(!e||e.startIndex!==t.startIndex||e.endIndex!==t.endIndex)}function xe(e,t){return!(!e||e[0]!==t[0]||e[1]!==t[1])}const ve=M((()=>({recalcInProgress:L(!1)})),[],{singleton:!0});function Ie(e,t,o){return e[Te(e,t,o)]}function Te(e,t,o,n=0){let r=e.length-1;for(;n<=r;){const i=Math.floor((n+r)/2),l=o(e[i],t);if(0===l)return i;if(-1===l){if(r-n<2)return i-1;r=i-1}else{if(r===n)return i;n=i+1}}throw new Error(`Failed binary finding record in array - ${e.join(",")}, searched for ${t}`)}function Se(e,t){return Math.round(e.getBoundingClientRect()[t])}function we(e){return!J(e.groupOffsetTree)}function Ce({index:e},t){return t===e?0:t<e?-1:1}function He({offset:e},t){return t===e?0:t<e?-1:1}function ye(e,t,o){if(0===t.length)return 0;const{index:n,offset:r,size:i}=Ie(t,e,Ce),l=e-n,s=i*l+(l-1)*o+r;return s>0?s+o:s}function be(e,t){if(!we(t))return e;let o=0;for(;t.groupIndices[o]<=e+o;)o++;return e+o}function ze(e,t,o){if(function(e){return typeof e.groupIndex<"u"}(e))return t.groupIndices[e.groupIndex]+1;{let n=be("LAST"===e.index?o:e.index,t);return n=Math.max(0,n,Math.min(o,n)),n}}function Re(e,t,o,n=0){return n>0&&(t=Math.max(t,Ie(e,n,Ce).offset)),Z(function(e,t,o,n){const r=Te(e,t,n),i=Te(e,o,n,r);return e.slice(r,i+1)}(e,t,o,He),Le)}function Be(e,[t,o,n,r]){t.length>0&&n("received item sizes",t,D.DEBUG);const i=e.sizeTree;let l=i,s=0;if(o.length>0&&J(i)&&2===t.length){const e=t[0].size,n=t[1].size;l=o.reduce(((t,o)=>te(te(t,o,e),o+1,n)),l)}else[l,s]=function(e,t){let o=J(e)?0:1/0;for(const n of t){const{endIndex:t,size:r,startIndex:i}=n;if(o=Math.min(o,i),J(e)){e=te(e,0,r);continue}const l=ne(e,i-1,t+1);if(l.some(Fe(n)))continue;let s=!1,c=!1;for(const{end:o,start:n,value:a}of l)s?(t>=n||r===a)&&(e=re(e,n)):(c=a!==r,s=!0),o>t&&t>=n&&a!==r&&(e=te(e,t+1,a));c&&(e=te(e,i,r))}return[e,o]}(l,t);if(l===i)return e;const{lastIndex:c,lastOffset:a,lastSize:u,offsetTree:d}=ke(e.offsetTree,s,l,r);return{groupIndices:o,groupOffsetTree:o.reduce(((e,t)=>te(e,t,ye(t,d,r))),oe()),lastIndex:c,lastOffset:a,lastSize:u,offsetTree:d,sizeTree:l}}function Ee(e,t){let o=0,n=0;for(;o<e;)o+=t[n+1]-t[n]-1,n++;return n-(o===e?0:1)}function ke(e,t,o,n){let r=e,i=0,l=0,s=0,c=0;if(0!==t){c=Te(r,t-1,Ce),s=r[c].offset;const e=ee(o,t-1);i=e[0],l=e[1],r.length&&r[c].size===ee(o,t)[1]&&(c-=1),r=r.slice(0,c+1)}else r=[];for(const{start:a,value:u}of ne(o,t,1/0)){const e=a-i,t=e*l+s+e*n;r.push({index:a,offset:t,size:u}),i=a,s=t,l=u}return{lastIndex:i,lastOffset:s,lastSize:l,offsetTree:r}}function Le(e){return{index:e.index,value:e}}function Fe(e){const{endIndex:t,size:o,startIndex:n}=e;return e=>e.start===n&&(e.end===t||e.end===1/0)&&e.value===o}const Oe={offsetHeight:"height",offsetWidth:"width"},je=M((([{log:e},{recalcInProgress:t}])=>{const o=O(),n=O(),r=F(n,0),i=O(),l=O(),s=L(0),c=L([]),a=L(void 0),u=L(void 0),d=L(((e,t)=>Se(e,Oe[t]))),h=L(void 0),m=L(0),f={groupIndices:[],groupOffsetTree:oe(),lastIndex:0,lastOffset:0,lastSize:0,offsetTree:[],sizeTree:oe()},x=F(z(o,k(c,e,m),R(Be,f),C()),f),T=F(z(c,C(),R(((e,t)=>({current:t,prev:e.current})),{current:[],prev:[]}),y((({prev:e})=>e))),[]);I(z(c,H((e=>e.length>0)),k(x,m),y((([e,t,o])=>{const n=e.reduce(((e,n,r)=>te(e,n,ye(n,t.offsetTree,o)||r)),oe());return{...t,groupIndices:e,groupOffsetTree:n}}))),x),I(z(n,k(x),H((([e,{lastIndex:t}])=>e<t)),y((([e,{lastIndex:t,lastSize:o}])=>[{endIndex:t,size:o,startIndex:e}]))),o),I(a,u);const S=F(z(a,y((e=>void 0===e))),!0);I(z(u,H((e=>void 0!==e&&J(v(x).sizeTree))),y((e=>[{endIndex:0,size:e,startIndex:0}]))),o);const w=j(z(o,k(x),R((({sizes:e},[t,o])=>({changed:o!==e,sizes:o})),{changed:!1,sizes:f}),y((e=>e.changed))));p(z(s,R(((e,t)=>({diff:e.prev-t,prev:t})),{diff:0,prev:0}),y((e=>e.diff))),(e=>{const{groupIndices:o}=v(x);if(e>0)g(t,!0),g(i,e+Ee(e,o));else if(e<0){const t=v(T);t.length>0&&(e-=Ee(-e,t)),g(l,e)}})),p(z(s,k(e)),(([e,t])=>{e<0&&t("`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value",{firstItemIndex:s},D.ERROR)}));const b=j(i);I(z(i,k(x),y((([e,t])=>{const o=t.groupIndices.length>0,n=[],r=t.lastSize;if(o){const o=Q(t.sizeTree,0);let i=0,l=0;for(;i<e;){const e=t.groupIndices[l],s=t.groupIndices.length===l+1?1/0:t.groupIndices[l+1]-e-1;n.push({endIndex:e,size:o,startIndex:e}),n.push({endIndex:e+1+s-1,size:r,startIndex:e+1}),l++,i+=s+1}const s=ie(t.sizeTree);return i!==e&&s.shift(),s.reduce(((t,{k:o,v:n})=>{let r=t.ranges;return 0!==t.prevSize&&(r=[...t.ranges,{endIndex:o+e-1,size:t.prevSize,startIndex:t.prevIndex}]),{prevIndex:o+e,prevSize:n,ranges:r}}),{prevIndex:e,prevSize:0,ranges:n}).ranges}return ie(t.sizeTree).reduce(((t,{k:o,v:n})=>({prevIndex:o+e,prevSize:n,ranges:[...t.ranges,{endIndex:o+e-1,size:t.prevSize,startIndex:t.prevIndex}]})),{prevIndex:0,prevSize:r,ranges:[]}).ranges}))),o);const B=j(z(l,k(x,m),y((([e,{offsetTree:t},o])=>ye(-e,t,o)))));return I(z(l,k(x,m),y((([e,t,o])=>{if(t.groupIndices.length>0){if(J(t.sizeTree))return t;let n=oe();const r=v(T);let i=0,l=0,s=0;for(;i<-e;){s=r[l];const e=r[l+1]-s-1;l++,i+=e+1}if(n=ie(t.sizeTree).reduce(((t,{k:o,v:n})=>te(t,Math.max(0,o+e),n)),n),i!==-e){n=te(n,0,Q(t.sizeTree,s));n=te(n,1,ee(t.sizeTree,1-e)[1])}return{...t,sizeTree:n,...ke(t.offsetTree,0,n,o)}}{const n=ie(t.sizeTree).reduce(((t,{k:o,v:n})=>te(t,Math.max(0,o+e),n)),oe());return{...t,sizeTree:n,...ke(t.offsetTree,0,n,o)}}}))),x),{beforeUnshiftWith:b,data:h,defaultItemSize:u,firstItemIndex:s,fixedItemSize:a,gap:m,groupIndices:c,itemSize:d,listRefresh:w,shiftWith:l,shiftWithOffset:B,sizeRanges:o,sizes:x,statefulTotalCount:r,totalCount:n,trackItemSizes:S,unshiftWith:i}}),f(N,ve),{singleton:!0});function Me(e){return e.reduce(((e,t)=>(e.groupIndices.push(e.totalCount),e.totalCount+=t+1,e)),{groupIndices:[],totalCount:0})}const We=M((([{groupIndices:e,sizes:t,totalCount:o},{headerHeight:n,scrollTop:r}])=>{const i=O(),l=O(),s=j(z(i,y(Me)));return I(z(s,y((e=>e.totalCount))),o),I(z(s,y((e=>e.groupIndices))),e),I(z(P(r,t,n),H((([e,t])=>we(t))),y((([e,t,o])=>ee(t.groupOffsetTree,Math.max(e-o,0),"v")[0])),C(),y((e=>[e]))),l),{groupCounts:i,topItemsIndexes:l}}),f(je,Y)),Pe=M((([{log:e}])=>{const t=L(!1),o=j(z(t,H((e=>e)),C()));return p(t,(t=>{t&&v(e)("props updated",{},D.DEBUG)})),{didMount:o,propsReady:t}}),f(N),{singleton:!0}),Ae=typeof document<"u"&&"scrollBehavior"in document.documentElement.style;function Ve(e){const t="number"==typeof e?{index:e}:e;return t.align||(t.align="start"),(!t.behavior||!Ae)&&(t.behavior="auto"),t.offset||(t.offset=0),t}const De=M((([{gap:e,listRefresh:t,sizes:o,totalCount:n},{fixedFooterHeight:r,fixedHeaderHeight:i,footerHeight:l,headerHeight:s,scrollingInProgress:c,scrollTo:a,smoothScrollTargetReached:u,viewportHeight:d},{log:h}])=>{const m=O(),f=O(),x=L(0);let v=null,S=null,w=null;function C(){v&&(v(),v=null),w&&(w(),w=null),S&&(clearTimeout(S),S=null),g(c,!1)}return I(z(m,k(o,d,n,x,s,l,h),k(e,i,r),y((([[e,o,n,r,i,l,s,a],d,h,x])=>{const I=Ve(e),{align:H,behavior:y,offset:b}=I,R=r-1,B=ze(I,o,R);let E=ye(B,o.offsetTree,d)+l;"end"===H?(E+=h+ee(o.sizeTree,B)[1]-n+x,B===R&&(E+=s)):"center"===H?E+=(h+ee(o.sizeTree,B)[1]-n+x)/2:E-=i,b&&(E+=b);const k=t=>{C(),t?(a("retrying to scroll to",{location:e},D.DEBUG),g(m,e)):(g(f,!0),a("list did not change, scroll successful",{},D.DEBUG))};if(C(),"smooth"===y){let e=!1;w=p(t,(t=>{e=e||t})),v=T(u,(()=>{k(e)}))}else v=T(z(t,function(e){return t=>{const o=setTimeout((()=>{t(!1)}),e);return e=>{e&&(t(!0),clearTimeout(o))}}}(150)),k);return S=setTimeout((()=>{C()}),1200),g(c,!0),a("scrolling from index to",{behavior:y,index:B,top:E},D.DEBUG),{behavior:y,top:E}}))),a),{scrollTargetReached:f,scrollToIndex:m,topListHeight:x}}),f(je,Y,N),{singleton:!0});function Ge(e,t){0==e?t():requestAnimationFrame((()=>{Ge(e-1,t)}))}function Ne(e,t){const o=t-1;return"number"==typeof e?e:"LAST"===e.index?o:e.index}const _e=M((([{defaultItemSize:e,listRefresh:t,sizes:o},{scrollTop:n},{scrollTargetReached:r,scrollToIndex:i},{didMount:l}])=>{const s=L(!0),c=L(0),a=L(!0);return I(z(l,k(c),H((([e,t])=>!!t)),b(!1)),s),I(z(l,k(c),H((([e,t])=>!!t)),b(!1)),a),p(z(P(t,l),k(s,o,e,a),H((([[,e],t,{sizeTree:o},n,r])=>e&&(!J(o)||u(n))&&!t&&!r)),k(c)),(([,e])=>{T(r,(()=>{g(a,!0)})),Ge(4,(()=>{T(n,(()=>{g(s,!0)})),g(i,e)}))})),{initialItemFinalLocationReached:a,initialTopMostItemIndex:c,scrolledToInitialItem:s}}),f(je,Y,De,Pe),{singleton:!0});function Ue(e,t){return Math.abs(e-t)<1.01}const Ke="up",qe="down",$e={atBottom:!1,notAtBottomBecause:"NOT_SHOWING_LAST_ITEM",state:{offsetBottom:0,scrollHeight:0,scrollTop:0,viewportHeight:0}},Ye=M((([{footerHeight:e,headerHeight:t,scrollBy:o,scrollContainerState:n,scrollTop:r,viewportHeight:i}])=>{const l=L(!1),s=L(!0),c=O(),a=O(),u=L(4),d=L(0),h=F(z(V(z(A(r),B(1),b(!0)),z(A(r),B(1),b(!1),S(100))),C()),!1),m=F(z(V(z(o,b(!0)),z(o,b(!1),S(200))),C()),!1);I(z(P(A(r),A(d)),y((([e,t])=>e<=t)),C()),s),I(z(s,E(50)),a);const f=j(z(P(n,A(i),A(t),A(e),A(u)),R(((e,[{scrollHeight:t,scrollTop:o},n,r,i,l])=>{const s={scrollHeight:t,scrollTop:o,viewportHeight:n};if(o+n-t>-l){let t,n;return o>e.state.scrollTop?(t="SCROLLED_DOWN",n=e.state.scrollTop-o):(t="SIZE_DECREASED",n=e.state.scrollTop-o||e.scrollTopDelta),{atBottom:!0,atBottomBecause:t,scrollTopDelta:n,state:s}}let c;return c=s.scrollHeight>e.state.scrollHeight?"SIZE_INCREASED":n<e.state.viewportHeight?"VIEWPORT_HEIGHT_DECREASING":o<e.state.scrollTop?"SCROLLING_UPWARDS":"NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM",{atBottom:!1,notAtBottomBecause:c,state:s}}),$e),C(((e,t)=>e&&e.atBottom===t.atBottom)))),p=F(z(n,R(((e,{scrollHeight:t,scrollTop:o,viewportHeight:n})=>{if(Ue(e.scrollHeight,t))return{changed:!1,jump:0,scrollHeight:t,scrollTop:o};{const r=t-(o+n)<1;return e.scrollTop!==o&&r?{changed:!0,jump:e.scrollTop-o,scrollHeight:t,scrollTop:o}:{changed:!0,jump:0,scrollHeight:t,scrollTop:o}}}),{changed:!1,jump:0,scrollHeight:0,scrollTop:0}),H((e=>e.changed)),y((e=>e.jump))),0);I(z(f,y((e=>e.atBottom))),l),I(z(l,E(50)),c);const g=L(qe);I(z(n,y((({scrollTop:e})=>e)),C(),R(((e,t)=>v(m)?{direction:e.direction,prevScrollTop:t}:{direction:t<e.prevScrollTop?Ke:qe,prevScrollTop:t}),{direction:qe,prevScrollTop:0}),y((e=>e.direction))),g),I(z(n,E(50),b("none")),g);const x=L(0);return I(z(h,H((e=>!e)),b(0)),x),I(z(r,E(100),k(h),H((([e,t])=>!!t)),R((([e,t],[o])=>[t,o]),[0,0]),y((([e,t])=>t-e))),x),{atBottomState:f,atBottomStateChange:c,atBottomThreshold:u,atTopStateChange:a,atTopThreshold:d,isAtBottom:l,isAtTop:s,isScrolling:h,lastJumpDueToItemResize:p,scrollDirection:g,scrollVelocity:x}}),f(Y)),Xe="top",Ze="bottom",Je="none";function Qe(e,t,o){return"number"==typeof e?o===Ke&&t===Xe||o===qe&&t===Ze?e:0:o===Ke?t===Xe?e.main:e.reverse:t===Ze?e.main:e.reverse}function et(e,t){var o;return"number"==typeof e?e:null!=(o=e[t])?o:0}const tt=M((([{deviation:e,fixedHeaderHeight:t,headerHeight:o,scrollTop:n,viewportHeight:r}])=>{const i=O(),l=L(0),s=L(0),c=L(0);return{increaseViewportBy:s,listBoundary:i,overscan:c,topListHeight:l,visibleRange:F(z(P(A(n),A(r),A(o),A(i,xe),A(c),A(l),A(t),A(e),A(s)),y((([e,t,o,[n,r],i,l,s,c,a])=>{const u=e-c,d=l+s,h=Math.max(o-u,0);let m=Je;const f=et(a,Xe),p=et(a,Ze);return n-=c,r+=o+s,(n+=o+s)>e+d-f&&(m=Ke),(r-=c)<e-h+t+p&&(m=qe),m!==Je?[Math.max(u-o-Qe(i,Xe,m)-f,0),u-h-s+t+Qe(i,Ze,m)+p]:null})),H((e=>null!=e)),C(xe)),[0,0])}}),f(Y),{singleton:!0});const ot={bottom:0,firstItemIndex:0,items:[],offsetBottom:0,offsetTop:0,top:0,topItems:[],topListHeight:0,totalCount:0};function nt(e,t,o,n,r,i){const{lastIndex:l,lastOffset:s,lastSize:c}=r;let a=0,u=0;if(e.length>0){a=e[0].offset;const t=e[e.length-1];u=t.offset+t.size}const d=o-l,h=a,m=s+d*c+(d-1)*n-u;return{bottom:u,firstItemIndex:i,items:it(e,r,i),offsetBottom:m,offsetTop:a,top:h,topItems:it(t,r,i),topListHeight:t.reduce(((e,t)=>t.size+e),0),totalCount:o}}function rt(e,t,o,n,r,i){let l=0;if(o.groupIndices.length>0)for(const a of o.groupIndices){if(a-l>=e)break;l++}const s=e+l,c=Ne(t,s);return nt(Array.from({length:s}).map(((e,t)=>({data:i[t+c],index:t+c,offset:0,size:0}))),[],s,r,o,n)}function it(e,t,o){if(0===e.length)return[];if(!we(t))return e.map((e=>({...e,index:e.index+o,originalIndex:e.index})));const n=e[0].index,r=e[e.length-1].index,i=[],l=ne(t.groupOffsetTree,n,r);let s,c=0;for(const a of e){let e;(!s||s.end<a.index)&&(s=l.shift(),c=t.groupIndices.indexOf(s.start)),e=a.index===s.start?{index:c,type:"group"}:{groupIndex:c,index:a.index-(c+1)+o},i.push({...e,data:a.data,offset:a.offset,originalIndex:a.index,size:a.size})}return i}const lt=M((([{data:e,firstItemIndex:t,gap:o,sizes:n,totalCount:r},i,{listBoundary:l,topListHeight:s,visibleRange:c},{initialTopMostItemIndex:a,scrolledToInitialItem:d},{topListHeight:m},f,{didMount:p},{recalcInProgress:g}])=>{const x=L([]),T=L(0),S=O();I(i.topItemsIndexes,x);const w=F(z(P(p,g,A(c,xe),A(r),A(n),A(a),d,A(x),A(t),A(o),e),H((([e,t,,o,,,,,,,n])=>{const r=n&&n.length!==o;return e&&!t&&!r})),y((([,,[e,t],o,n,r,i,l,s,c,a])=>{const u=n,{offsetTree:d,sizeTree:m}=u,f=v(T);if(0===o)return{...ot,totalCount:o};if(0===e&&0===t)return 0===f?{...ot,totalCount:o}:rt(f,r,n,s,c,a||[]);if(J(m))return f>0?null:nt(function(e,t,o){if(we(t)){const n=be(e,t);return[{index:ee(t.groupOffsetTree,n)[0],offset:0,size:0},{data:null==o?void 0:o[0],index:n,offset:0,size:0}]}return[{data:null==o?void 0:o[0],index:e,offset:0,size:0}]}(Ne(r,o),u,a),[],o,c,u,s);const p=[];if(l.length>0){const e=l[0],t=l[l.length-1];let o=0;for(const n of ne(m,e,t)){const r=n.value,i=Math.max(n.start,e),l=Math.min(n.end,t);for(let e=i;e<=l;e++)p.push({data:null==a?void 0:a[e],index:e,offset:o,size:r}),o+=r}}if(!i)return nt([],p,o,c,u,s);const g=l.length>0?l[l.length-1]+1:0,x=Re(d,e,t,g);if(0===x.length)return null;const I=o-1;return nt(h([],(o=>{for(const n of x){const r=n.value;let i=r.offset,l=n.start;const s=r.size;if(r.offset<e){l+=Math.floor((e-r.offset+c)/(s+c));const t=l-n.start;i+=t*s+t*c}l<g&&(i+=(g-l)*s,l=g);const u=Math.min(n.end,I);for(let e=l;e<=u&&!(i>=t);e++)o.push({data:null==a?void 0:a[e],index:e,offset:i,size:s}),i+=s+c}})),p,o,c,u,s)})),H((e=>null!==e)),C()),ot);I(z(e,H(u),y((e=>null==e?void 0:e.length))),r),I(z(w,y((e=>e.topListHeight))),m),I(m,s),I(z(w,y((e=>[e.top,e.bottom]))),l),I(z(w,y((e=>e.items))),S);const b=j(z(w,H((({items:e})=>e.length>0)),k(r,e),H((([{items:e},t])=>e[e.length-1].originalIndex===t-1)),y((([,e,t])=>[e-1,t])),C(xe),y((([e])=>e)))),R=j(z(w,E(200),H((({items:e,topItems:t})=>e.length>0&&e[0].originalIndex===t.length)),y((({items:e})=>e[0].index)),C())),B=j(z(w,H((({items:e})=>e.length>0)),y((({items:e})=>{let t=0,o=e.length-1;for(;"group"===e[t].type&&t<o;)t++;for(;"group"===e[o].type&&o>t;)o--;return{endIndex:e[o].index,startIndex:e[t].index}})),C(ge)));return{endReached:b,initialItemCount:T,itemsRendered:S,listState:w,rangeChanged:B,startReached:R,topItemsIndexes:x,...f}}),f(je,We,tt,_e,De,Ye,Pe,ve),{singleton:!0}),st=M((([{fixedFooterHeight:e,fixedHeaderHeight:t,footerHeight:o,headerHeight:n},{listState:r}])=>{const i=O(),l=F(z(P(o,e,n,t,r),y((([e,t,o,n,r])=>e+t+o+n+r.offsetBottom+r.bottom))),0);return I(A(l),i),{totalListHeight:l,totalListHeightChanged:i}}),f(Y,lt),{singleton:!0}),ct=M((([{viewportHeight:e},{totalListHeight:t}])=>{const o=L(!1);return{alignToBottom:o,paddingTopAddition:F(z(P(o,e,t),H((([e])=>e)),y((([,e,t])=>Math.max(0,e-t))),E(0),C()),0)}}),f(Y,st),{singleton:!0});function at(e){return!!e&&("smooth"===e?"smooth":"auto")}const ut=M((([{listRefresh:e,totalCount:t,fixedItemSize:o},{atBottomState:n,isAtBottom:r},{scrollToIndex:i},{scrolledToInitialItem:l},{didMount:s,propsReady:c},{log:a},{scrollingInProgress:u}])=>{const d=L(!1),h=O();let m=null;function f(e){g(i,{align:"end",behavior:e,index:"LAST"})}function x(e){const t=T(n,(t=>{e&&!t.atBottom&&"SIZE_INCREASED"===t.notAtBottomBecause&&!m&&(v(a)("scrolling to bottom due to increased size",{},D.DEBUG),f("auto"))}));setTimeout(t,100)}return p(z(P(z(A(t),B(1)),s),k(A(d),r,l,u),y((([[e,t],o,n,r,i])=>{let l=t&&r,s="auto";return l&&(s=((e,t)=>"function"==typeof e?at(e(t)):t&&at(e))(o,n||i),l=l&&!!s),{followOutputBehavior:s,shouldFollow:l,totalCount:e}})),H((({shouldFollow:e})=>e))),(({followOutputBehavior:t,totalCount:n})=>{m&&(m(),m=null),v(o)?requestAnimationFrame((()=>{v(a)("following output to ",{totalCount:n},D.DEBUG),f(t)})):m=T(e,(()=>{v(a)("following output to ",{totalCount:n},D.DEBUG),f(t),m=null}))})),p(z(P(A(d),t,c),H((([e,,t])=>e&&t)),R((({value:e},[,t])=>({refreshed:e===t,value:t})),{refreshed:!1,value:0}),H((({refreshed:e})=>e)),k(d,t)),(([,e])=>{v(l)&&x(!1!==e)})),p(h,(()=>{x(!1!==v(d))})),p(P(A(d),n),(([e,t])=>{e&&!t.atBottom&&"VIEWPORT_HEIGHT_DECREASING"===t.notAtBottomBecause&&f("auto")})),{autoscrollToBottom:h,followOutput:d}}),f(je,Ye,De,_e,Pe,N,Y)),dt=M((([{data:e,firstItemIndex:t,gap:o,sizes:n},{initialTopMostItemIndex:r},{initialItemCount:i,listState:l},{didMount:s}])=>(I(z(s,k(i),H((([,e])=>0!==e)),k(r,n,t,o,e),y((([[,e],t,o,n,r,i=[]])=>rt(e,t,o,n,r,i)))),l),{})),f(je,_e,lt,Pe),{singleton:!0}),ht=M((([{didMount:e},{scrollTo:t},{listState:o}])=>{const n=L(0);return p(z(e,k(n),H((([,e])=>0!==e)),y((([,e])=>({top:e})))),(e=>{T(z(o,B(1),H((e=>e.items.length>1))),(()=>{requestAnimationFrame((()=>{g(t,e)}))}))})),{initialScrollTop:n}}),f(Pe,Y,lt),{singleton:!0}),mt=({itemBottom:e,itemTop:t,locationParams:{align:o,behavior:n,...r},viewportBottom:i,viewportTop:l})=>t<l?{...r,align:null!=o?o:"start",behavior:n}:e>i?{...r,align:null!=o?o:"end",behavior:n}:null,ft=M((([{gap:e,sizes:t,totalCount:o},{fixedFooterHeight:n,fixedHeaderHeight:r,headerHeight:i,scrollingInProgress:l,scrollTop:s,viewportHeight:c},{scrollToIndex:a}])=>{const u=O();return I(z(u,k(t,c,o,i,r,n,s),k(e),y((([[e,t,o,n,r,i,s,c],a])=>{const{align:u,behavior:d,calculateViewLocation:h=mt,done:m,...f}=e,p=ze(e,t,n-1),g=ye(p,t.offsetTree,a)+r+i,x=h({itemBottom:g+ee(t.sizeTree,p)[1],itemTop:g,locationParams:{align:u,behavior:d,...f},viewportBottom:c+o-s,viewportTop:c+i});return x?m&&T(z(l,H((e=>!e)),B(v(l)?1:2)),m):m&&m(),x})),H((e=>null!==e))),a),{scrollIntoView:u}}),f(je,Y,De,lt,N),{singleton:!0}),pt=M((([{scrollVelocity:e}])=>{const t=L(!1),o=O(),n=L(!1);return I(z(e,k(n,t,o),H((([e,t])=>!!t)),y((([e,t,o,n])=>{const{enter:r,exit:i}=t;if(o){if(i(e,n))return!1}else if(r(e,n))return!0;return o})),C()),t),p(z(P(t,e,o),k(n)),(([[e,t,o],n])=>{e&&n&&n.change&&n.change(t,o)})),{isSeeking:t,scrollSeekConfiguration:n,scrollSeekRangeChanged:o,scrollVelocity:e}}),f(Ye),{singleton:!0}),gt=M((([{scrollContainerState:e,scrollTo:t}])=>{const o=O(),n=O(),r=O(),i=L(!1),l=L(void 0);return I(z(P(o,n),y((([{scrollHeight:e,scrollTop:t,viewportHeight:o},{offsetTop:n}])=>({scrollHeight:e,scrollTop:Math.max(0,t-n),viewportHeight:o})))),e),I(z(t,k(n),y((([e,{offsetTop:t}])=>({...e,top:e.top+t})))),r),{customScrollParent:l,useWindowScroll:i,windowScrollContainerState:o,windowScrollTo:r,windowViewportRect:n}}),f(Y)),xt=M((([{sizeRanges:e,sizes:t},{headerHeight:o,scrollTop:n},{initialTopMostItemIndex:r},{didMount:i},{useWindowScroll:l,windowScrollContainerState:s,windowViewportRect:c}])=>{const a=O(),d=L(void 0),h=L(null),m=L(null);return I(s,h),I(c,m),p(z(a,k(t,n,l,h,m,o)),(([e,t,o,n,r,i,l])=>{const s=function(e){return ie(e).map((({k:e,v:t},o,n)=>{const r=n[o+1];return{endIndex:r?r.k-1:1/0,size:t,startIndex:e}}))}(t.sizeTree);n&&null!==r&&null!==i&&(o=r.scrollTop-i.offsetTop),e({ranges:s,scrollTop:o-=l})})),I(z(d,H(u),y(vt)),r),I(z(i,k(d),H((([,e])=>void 0!==e)),C(),y((([,e])=>e.ranges))),e),{getState:a,restoreStateFrom:d}}),f(je,Y,_e,Pe,gt));function vt(e){return{align:"start",index:0,offset:e.scrollTop}}const It=M((([{topItemsIndexes:e}])=>{const t=L(0);return I(z(t,H((e=>e>=0)),y((e=>Array.from({length:e}).map(((e,t)=>t))))),e),{topItemCount:t}}),f(lt));function Tt(e){let t,o=!1;return()=>(o||(o=!0,t=e()),t)}const St=Tt((()=>/iP(ad|od|hone)/i.test(navigator.userAgent)&&/WebKit/i.test(navigator.userAgent))),wt=M((([{deviation:e,scrollBy:t,scrollingInProgress:o,scrollTop:n},{isAtBottom:r,isScrolling:i,lastJumpDueToItemResize:l,scrollDirection:s},{listState:c},{beforeUnshiftWith:a,gap:u,shiftWithOffset:d,sizes:h},{log:m},{recalcInProgress:f}])=>{const x=j(z(c,k(l),R((([,e,t,o],[{bottom:n,items:r,offsetBottom:i,totalCount:l},s])=>{const c=n+i;let a=0;return t===l&&e.length>0&&r.length>0&&(0===r[0].originalIndex&&0===e[0].originalIndex||(a=c-o,0!==a&&(a+=s))),[a,r,l,c]}),[0,[],0,0]),H((([e])=>0!==e)),k(n,s,o,r,m,f),H((([,e,t,o,,,n])=>!n&&!o&&0!==e&&t===Ke)),y((([[e],,,,,t])=>(t("Upward scrolling compensation",{amount:e},D.DEBUG),e)))));function v(o){o>0?(g(t,{behavior:"auto",top:-o}),g(e,0)):(g(e,0),g(t,{behavior:"auto",top:-o}))}return p(z(x,k(e,i)),(([t,o,n])=>{n&&St()?g(e,o-t):v(-t)})),p(z(P(F(i,!1),e,f),H((([e,t,o])=>!e&&!o&&0!==t)),y((([e,t])=>t)),E(1)),v),I(z(d,y((e=>({top:-e})))),t),p(z(a,k(h,u),y((([e,{groupIndices:t,lastSize:o,sizeTree:n},r])=>{function i(e){return e*(o+r)}if(0===t.length)return i(e);{let o=0;const r=Q(n,0);let l=0,s=0;for(;l<e;){l++,o+=r;let n=t.length===s+1?1/0:t[s+1]-t[s]-1;l+n>e&&(o-=r,n=e-l+1),l+=n,o+=i(n),s++}return o}}))),(o=>{g(e,o),requestAnimationFrame((()=>{g(t,{top:o}),requestAnimationFrame((()=>{g(e,0),g(f,!1)}))}))})),{deviation:e}}),f(Y,Ye,lt,je,N,ve)),Ct=M((([e,t,o,n,r,i,l,s,c,a])=>({...e,...t,...o,...n,...r,...i,...l,...s,...c,...a})),f(tt,dt,Pe,pt,st,ht,ct,gt,ft,N)),Ht=M((([{data:e,defaultItemSize:t,firstItemIndex:o,fixedItemSize:n,gap:r,groupIndices:i,itemSize:l,sizeRanges:s,sizes:c,statefulTotalCount:a,totalCount:u,trackItemSizes:d},{initialItemFinalLocationReached:h,initialTopMostItemIndex:m,scrolledToInitialItem:f},p,g,x,{listState:v,topItemsIndexes:T,...S},{scrollToIndex:w},C,{topItemCount:H},{groupCounts:b},R])=>(I(S.rangeChanged,R.scrollSeekRangeChanged),I(z(R.windowViewportRect,y((e=>e.visibleHeight))),p.viewportHeight),{data:e,defaultItemHeight:t,firstItemIndex:o,fixedItemHeight:n,gap:r,groupCounts:b,initialItemFinalLocationReached:h,initialTopMostItemIndex:m,scrolledToInitialItem:f,sizeRanges:s,topItemCount:H,topItemsIndexes:T,totalCount:u,...x,groupIndices:i,itemSize:l,listState:v,scrollToIndex:w,statefulTotalCount:a,trackItemSizes:d,...S,...R,...p,sizes:c,...g})),f(je,_e,Y,xt,ut,lt,De,wt,It,We,Ct));function yt(e,t){const o={},n={};let r=0;const i=e.length;for(;r<i;)n[e[r]]=1,r+=1;for(const l in t)Object.hasOwn(n,l)||(o[l]=t[l]);return o}const bt=typeof document<"u"?r.useLayoutEffect:r.useEffect;function zt(e,t,o){const i=Object.keys(t.required||{}),s=Object.keys(t.optional||{}),c=Object.keys(t.methods||{}),u=Object.keys(t.events||{}),m=r.createContext({});function f(e,o){e.propsReady&&g(e.propsReady,!1);for(const n of i){g(e[t.required[n]],o[n])}for(const n of s)if(n in o){g(e[t.optional[n]],o[n])}e.propsReady&&g(e.propsReady,!0)}function I(e){return u.reduce(((o,n)=>(o[n]=function(e){let t,o;const n=()=>null==t?void 0:t();return function(r,i){switch(r){case 1:return i?o===i?void 0:(n(),o=i,t=p(e,i),t):(n(),d);case 2:return n(),void(o=null)}}}(e[t.events[n]]),o)),{})}const T=r.forwardRef(((d,v)=>{const{children:T,...S}=d,[w]=r.useState((()=>h(function(e){const t=new Map,o=({constructor:e,dependencies:n,id:r,singleton:i})=>{if(i&&t.has(r))return t.get(r);const l=e(n.map((e=>o(e))));return i&&t.set(r,l),l};return o(e)}(e),(e=>{f(e,S)})))),[C]=r.useState(a(I,w));bt((()=>{for(const e of u)e in S&&p(C[e],S[e]);return()=>{Object.values(C).map(x)}}),[S,C,w]),bt((()=>{f(w,S)})),r.useImperativeHandle(v,l(function(e){return c.reduce(((o,n)=>(o[n]=o=>{g(e[t.methods[n]],o)},o)),{})}(w)));const H=o;return(0,n.jsx)(m.Provider,{value:w,children:o?(0,n.jsx)(H,{...yt([...i,...s,...u],S),children:T}):T})}));return{Component:T,useEmitter:(e,t)=>{const o=r.useContext(m)[e];bt((()=>p(o,t)),[t,o])},useEmitterValue:r.version.startsWith("18")?e=>{const t=r.useContext(m)[e],o=r.useCallback((e=>p(t,e)),[t]);return r.useSyncExternalStore(o,(()=>v(t)),(()=>v(t)))}:e=>{const t=r.useContext(m)[e],[o,n]=r.useState(a(v,t));return bt((()=>p(t,(e=>{e!==o&&n(l(e))}))),[t,o]),o},usePublisher:e=>{const t=r.useContext(m);return r.useCallback((o=>{g(t[e],o)}),[t,e])}}}const Rt=r.createContext(void 0),Bt=r.createContext(void 0),Et=typeof document<"u"?r.useLayoutEffect:r.useEffect;function kt(e){return"self"in e}function Lt(e,t,o,n=d,l,s){const c=r.useRef(null),a=r.useRef(null),u=r.useRef(null),h=r.useCallback((o=>{let n,r,l;const c=o.target;if(function(e){return"body"in e}(c)||kt(c)){const e=kt(c)?c:c.defaultView;l=s?e.scrollX:e.scrollY,n=s?e.document.documentElement.scrollWidth:e.document.documentElement.scrollHeight,r=s?e.innerWidth:e.innerHeight}else l=s?c.scrollLeft:c.scrollTop,n=s?c.scrollWidth:c.scrollHeight,r=s?c.offsetWidth:c.offsetHeight;const d=()=>{e({scrollHeight:n,scrollTop:Math.max(l,0),viewportHeight:r})};o.suppressFlushSync?d():i.flushSync(d),null!==a.current&&(l===a.current||l<=0||l===n-r)&&(a.current=null,t(!0),u.current&&(clearTimeout(u.current),u.current=null))}),[e,t,s]);return r.useEffect((()=>{const e=l||c.current;return n(l||c.current),h({suppressFlushSync:!0,target:e}),e.addEventListener("scroll",h,{passive:!0}),()=>{n(null),e.removeEventListener("scroll",h)}}),[c,h,o,n,l]),{scrollByCallback:function(e){s&&(e={behavior:e.behavior,left:e.top}),c.current.scrollBy(e)},scrollerRef:c,scrollToCallback:function(o){const n=c.current;if(!n||(s?"offsetWidth"in n&&0===n.offsetWidth:"offsetHeight"in n&&0===n.offsetHeight))return;const r="smooth"===o.behavior;let i,l,d;kt(n)?(l=Math.max(Se(n.document.documentElement,s?"width":"height"),s?n.document.documentElement.scrollWidth:n.document.documentElement.scrollHeight),i=s?n.innerWidth:n.innerHeight,d=s?window.scrollX:window.scrollY):(l=n[s?"scrollWidth":"scrollHeight"],i=Se(n,s?"width":"height"),d=n[s?"scrollLeft":"scrollTop"]);const h=l-i;if(o.top=Math.ceil(Math.max(Math.min(h,o.top),0)),Ue(i,l)||o.top===d)return e({scrollHeight:l,scrollTop:d,viewportHeight:i}),void(r&&t(!0));r?(a.current=o.top,u.current&&clearTimeout(u.current),u.current=setTimeout((()=>{u.current=null,a.current=null,t(!0)}),1e3)):a.current=null,s&&(o={behavior:o.behavior,left:o.top}),n.scrollTo(o)}}}const Ft="-webkit-sticky",Ot="sticky",jt=Tt((()=>{if(typeof document>"u")return Ot;const e=document.createElement("div");return e.style.position=Ft,e.style.position===Ft?Ft:Ot}));function Mt(e){return e}const Wt=M((([e,t])=>({...e,...t})),f(Ht,M((()=>{const e=L((e=>`Item ${e}`)),t=L(null),o=L((e=>`Group ${e}`)),n=L({}),r=L(Mt),i=L("div"),l=L(d),s=(e,t=null)=>F(z(n,y((t=>t[e])),C()),t);return{components:n,computeItemKey:r,context:t,EmptyPlaceholder:s("EmptyPlaceholder"),FooterComponent:s("Footer"),GroupComponent:s("Group","div"),groupContent:o,HeaderComponent:s("Header"),HeaderFooterTag:i,ItemComponent:s("Item","div"),itemContent:e,ListComponent:s("List","div"),ScrollerComponent:s("Scroller","div"),scrollerRef:l,ScrollSeekPlaceholder:s("ScrollSeekPlaceholder"),TopItemListComponent:s("TopItemList")}})))),Pt=({height:e})=>(0,n.jsx)("div",{style:{height:e}}),At={overflowAnchor:"none",position:jt(),zIndex:1},Vt={overflowAnchor:"none"},Dt={...Vt,display:"inline-block",height:"100%"},Gt=r.memo((function({showTopList:e=!1}){const t=io("listState"),o=lo("sizeRanges"),i=io("useWindowScroll"),l=io("customScrollParent"),s=lo("windowScrollContainerState"),c=lo("scrollContainerState"),a=l||i?s:c,u=io("itemContent"),h=io("context"),m=io("groupContent"),f=io("trackItemSizes"),p=io("itemSize"),g=io("log"),x=lo("gap"),v=io("horizontalDirection"),{callbackRef:I}=K(o,p,f,e?d:a,g,x,l,v,io("skipAnimationFrameInResizeObserver")),[T,S]=r.useState(0);ro("deviation",(e=>{T!==e&&S(e)}));const w=io("EmptyPlaceholder"),C=io("ScrollSeekPlaceholder")||Pt,H=io("ListComponent"),y=io("ItemComponent"),b=io("GroupComponent"),z=io("computeItemKey"),R=io("isSeeking"),B=io("groupIndices").length>0,E=io("alignToBottom"),k=io("initialItemFinalLocationReached"),L=e?{}:{boxSizing:"border-box",...v?{display:"inline-block",height:"100%",marginLeft:0!==T?T:E?"auto":0,paddingLeft:t.offsetTop,paddingRight:t.offsetBottom,whiteSpace:"nowrap"}:{marginTop:0!==T?T:E?"auto":0,paddingBottom:t.offsetBottom,paddingTop:t.offsetTop},...k?{}:{visibility:"hidden"}};return!e&&0===t.totalCount&&w?(0,n.jsx)(w,{...qt(w,h)}):(0,n.jsx)(H,{...qt(H,h),"data-testid":e?"virtuoso-top-item-list":"virtuoso-item-list",ref:I,style:L,children:(e?t.topItems:t.items).map((e=>{const o=e.originalIndex,n=z(o+t.firstItemIndex,e.data,h);return R?(0,r.createElement)(C,{...qt(C,h),height:e.size,index:e.index,key:n,type:e.type||"item",..."group"===e.type?{}:{groupIndex:e.groupIndex}}):"group"===e.type?(0,r.createElement)(b,{...qt(b,h),"data-index":o,"data-item-index":e.index,"data-known-size":e.size,key:n,style:At},m(e.index,h)):(0,r.createElement)(y,{...qt(y,h),...$t(y,e.data),"data-index":o,"data-item-group-index":e.groupIndex,"data-item-index":e.index,"data-known-size":e.size,key:n,style:v?Dt:Vt},B?u(e.index,e.groupIndex,e.data,h):u(e.index,e.data,h))}))})})),Nt={height:"100%",outline:"none",overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},_t={outline:"none",overflowX:"auto",position:"relative"},Ut=e=>({height:"100%",position:"absolute",top:0,width:"100%",...e?{display:"flex",flexDirection:"column"}:{}}),Kt={position:jt(),top:0,width:"100%",zIndex:1};function qt(e,t){if("string"!=typeof e)return{context:t}}function $t(e,t){return{item:"string"==typeof e?void 0:t}}const Yt=r.memo((function(){const e=io("HeaderComponent"),t=lo("headerHeight"),o=io("HeaderFooterTag"),i=_(r.useMemo((()=>e=>{t(Se(e,"height"))}),[t]),!0,io("skipAnimationFrameInResizeObserver")),l=io("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...qt(e,l)})}):null})),Xt=r.memo((function(){const e=io("FooterComponent"),t=lo("footerHeight"),o=io("HeaderFooterTag"),i=_(r.useMemo((()=>e=>{t(Se(e,"height"))}),[t]),!0,io("skipAnimationFrameInResizeObserver")),l=io("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...qt(e,l)})}):null}));function Zt({useEmitter:e,useEmitterValue:t,usePublisher:o}){return r.memo((function({children:r,style:i,...l}){const s=o("scrollContainerState"),c=t("ScrollerComponent"),a=o("smoothScrollTargetReached"),u=t("scrollerRef"),d=t("context"),h=t("horizontalDirection")||!1,{scrollByCallback:m,scrollerRef:f,scrollToCallback:p}=Lt(s,a,c,u,void 0,h);return e("scrollTo",p),e("scrollBy",m),(0,n.jsx)(c,{"data-testid":"virtuoso-scroller","data-virtuoso-scroller":!0,ref:f,style:{...h?_t:Nt,...i},tabIndex:0,...l,...qt(c,d),children:r})}))}function Jt({useEmitter:e,useEmitterValue:t,usePublisher:o}){return r.memo((function({children:i,style:l,...s}){const c=o("windowScrollContainerState"),a=t("ScrollerComponent"),u=o("smoothScrollTargetReached"),d=t("totalListHeight"),h=t("deviation"),m=t("customScrollParent"),f=t("context"),p=r.useRef(null),g=t("scrollerRef"),{scrollByCallback:x,scrollerRef:v,scrollToCallback:I}=Lt(c,u,a,g,m);return Et((()=>{var e;return v.current=m||(null==(e=p.current)?void 0:e.ownerDocument.defaultView),()=>{v.current=null}}),[v,m]),e("windowScrollTo",I),e("scrollBy",x),(0,n.jsx)(a,{ref:p,"data-virtuoso-scroller":!0,style:{position:"relative",...l,...0!==d?{height:d+h}:{}},...s,...qt(a,f),children:i})}))}const Qt=({children:e})=>{const t=r.useContext(Rt),o=lo("viewportHeight"),i=lo("fixedItemHeight"),l=io("alignToBottom"),s=io("horizontalDirection"),a=_(r.useMemo((()=>c(o,(e=>Se(e,s?"width":"height")))),[o,s]),!0,io("skipAnimationFrameInResizeObserver"));return r.useEffect((()=>{t&&(o(t.viewportHeight),i(t.itemHeight))}),[t,o,i]),(0,n.jsx)("div",{"data-viewport-type":"element",ref:a,style:Ut(l),children:e})},eo=({children:e})=>{const t=r.useContext(Rt),o=lo("windowViewportRect"),i=lo("fixedItemHeight"),l=io("customScrollParent"),s=$(o,l,io("skipAnimationFrameInResizeObserver")),c=io("alignToBottom");return r.useEffect((()=>{t&&(i(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))}),[t,o,i]),(0,n.jsx)("div",{"data-viewport-type":"window",ref:s,style:Ut(c),children:e})},to=({children:e})=>{const t=io("TopItemListComponent")||"div",o=io("headerHeight"),r={...Kt,marginTop:`${o}px`},i=io("context");return(0,n.jsx)(t,{style:r,...qt(t,i),children:e})},oo=r.memo((function(e){const t=io("useWindowScroll"),o=io("topItemsIndexes").length>0,r=io("customScrollParent"),i=io("context"),l=r||t?co:so,s=r||t?eo:Qt;return(0,n.jsxs)(l,{...e,...qt(l,i),children:[o&&(0,n.jsx)(to,{children:(0,n.jsx)(Gt,{showTopList:!0})}),(0,n.jsxs)(s,{children:[(0,n.jsx)(Yt,{}),(0,n.jsx)(Gt,{}),(0,n.jsx)(Xt,{})]})]})})),{Component:no,useEmitter:ro,useEmitterValue:io,usePublisher:lo}=zt(Wt,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",itemContent:"itemContent",groupContent:"groupContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",groupCounts:"groupCounts",topItemCount:"topItemCount",firstItemIndex:"firstItemIndex",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"HeaderFooterTag",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",horizontalDirection:"horizontalDirection",skipAnimationFrameInResizeObserver:"skipAnimationFrameInResizeObserver"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",autoscrollToBottom:"autoscrollToBottom",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},oo),so=Zt({useEmitter:ro,useEmitterValue:io,usePublisher:lo}),co=Jt({useEmitter:ro,useEmitterValue:io,usePublisher:lo}),ao=no,uo=M((([e,t])=>({...e,...t})),f(Ht,M((()=>{const e=L((e=>(0,n.jsxs)("td",{children:["Item $",e]}))),t=L(null),o=L((e=>(0,n.jsxs)("td",{colSpan:1e3,children:["Group ",e]}))),r=L(null),i=L(null),l=L({}),s=L(Mt),c=L(d),a=(e,t=null)=>F(z(l,y((t=>t[e])),C()),t);return{components:l,computeItemKey:s,context:t,EmptyPlaceholder:a("EmptyPlaceholder"),FillerRow:a("FillerRow"),fixedFooterContent:i,fixedHeaderContent:r,itemContent:e,groupContent:o,ScrollerComponent:a("Scroller","div"),scrollerRef:c,ScrollSeekPlaceholder:a("ScrollSeekPlaceholder"),TableBodyComponent:a("TableBody","tbody"),TableComponent:a("Table","table"),TableFooterComponent:a("TableFoot","tfoot"),TableHeadComponent:a("TableHead","thead"),TableRowComponent:a("TableRow","tr"),GroupComponent:a("Group","tr")}})))),ho=({height:e})=>(0,n.jsx)("tr",{children:(0,n.jsx)("td",{style:{height:e}})}),mo=({height:e})=>(0,n.jsx)("tr",{children:(0,n.jsx)("td",{style:{border:0,height:e,padding:0}})}),fo={overflowAnchor:"none"},po={position:jt(),zIndex:2,overflowAnchor:"none"},go=r.memo((function({showTopList:e=!1}){const t=Co("listState"),o=Co("computeItemKey"),i=Co("firstItemIndex"),l=Co("context"),s=Co("isSeeking"),c=Co("fixedHeaderHeight"),a=Co("groupIndices").length>0,u=Co("itemContent"),d=Co("groupContent"),h=Co("ScrollSeekPlaceholder")||ho,m=Co("GroupComponent"),f=Co("TableRowComponent"),p=(e?t.topItems:[]).reduce(((e,t,o)=>(0===o?e.push(t.size):e.push(e[o-1]+t.size),e)),[]),g=(e?t.topItems:t.items).map((t=>{const n=t.originalIndex,g=o(n+i,t.data,l),x=e?0===n?0:p[n-1]:0;return s?(0,r.createElement)(h,{...qt(h,l),height:t.size,index:t.index,key:g,type:t.type||"item"}):"group"===t.type?(0,r.createElement)(m,{...qt(m,l),"data-index":n,"data-item-index":t.index,"data-known-size":t.size,key:g,style:{...po,top:c}},d(t.index,l)):(0,r.createElement)(f,{...qt(f,l),...$t(f,t.data),"data-index":n,"data-item-index":t.index,"data-known-size":t.size,"data-item-group-index":t.groupIndex,key:g,style:e?{...po,top:c+x}:fo},a?u(t.index,t.groupIndex,t.data,l):u(t.index,t.data,l))}));return(0,n.jsx)(n.Fragment,{children:g})})),xo=r.memo((function(){const e=Co("listState"),t=Co("topItemsIndexes").length>0,o=Ho("sizeRanges"),i=Co("useWindowScroll"),l=Co("customScrollParent"),s=Ho("windowScrollContainerState"),c=Ho("scrollContainerState"),a=l||i?s:c,u=Co("trackItemSizes"),d=Co("itemSize"),h=Co("log"),{callbackRef:m,ref:f}=K(o,d,u,a,h,void 0,l,!1,Co("skipAnimationFrameInResizeObserver")),[p,g]=r.useState(0);wo("deviation",(e=>{p!==e&&(f.current.style.marginTop=`${e}px`,g(e))}));const x=Co("EmptyPlaceholder"),v=Co("FillerRow")||mo,I=Co("TableBodyComponent"),T=Co("paddingTopAddition"),S=Co("statefulTotalCount"),w=Co("context");if(0===S&&x)return(0,n.jsx)(x,{...qt(x,w)});const C=(t?e.topItems:[]).reduce(((e,t)=>e+t.size),0),H=e.offsetTop+T+p-C,y=e.offsetBottom,b=H>0?(0,n.jsx)(v,{context:w,height:H},"padding-top"):null,z=y>0?(0,n.jsx)(v,{context:w,height:y},"padding-bottom"):null;return(0,n.jsxs)(I,{"data-testid":"virtuoso-item-list",ref:m,...qt(I,w),children:[b,t&&(0,n.jsx)(go,{showTopList:!0}),(0,n.jsx)(go,{}),z]})})),vo=({children:e})=>{const t=r.useContext(Rt),o=Ho("viewportHeight"),i=Ho("fixedItemHeight"),l=_(r.useMemo((()=>c(o,(e=>Se(e,"height")))),[o]),!0,Co("skipAnimationFrameInResizeObserver"));return r.useEffect((()=>{t&&(o(t.viewportHeight),i(t.itemHeight))}),[t,o,i]),(0,n.jsx)("div",{"data-viewport-type":"element",ref:l,style:Ut(!1),children:e})},Io=({children:e})=>{const t=r.useContext(Rt),o=Ho("windowViewportRect"),i=Ho("fixedItemHeight"),l=Co("customScrollParent"),s=$(o,l,Co("skipAnimationFrameInResizeObserver"));return r.useEffect((()=>{t&&(i(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))}),[t,o,i]),(0,n.jsx)("div",{"data-viewport-type":"window",ref:s,style:Ut(!1),children:e})},To=r.memo((function(e){const t=Co("useWindowScroll"),o=Co("customScrollParent"),i=Ho("fixedHeaderHeight"),l=Ho("fixedFooterHeight"),s=Co("fixedHeaderContent"),a=Co("fixedFooterContent"),u=Co("context"),d=_(r.useMemo((()=>c(i,(e=>Se(e,"height")))),[i]),!0,Co("skipAnimationFrameInResizeObserver")),h=_(r.useMemo((()=>c(l,(e=>Se(e,"height")))),[l]),!0,Co("skipAnimationFrameInResizeObserver")),m=o||t?bo:yo,f=o||t?Io:vo,p=Co("TableComponent"),g=Co("TableHeadComponent"),x=Co("TableFooterComponent"),v=s?(0,n.jsx)(g,{ref:d,style:{position:"sticky",top:0,zIndex:2},...qt(g,u),children:s()},"TableHead"):null,I=a?(0,n.jsx)(x,{ref:h,style:{bottom:0,position:"sticky",zIndex:1},...qt(x,u),children:a()},"TableFoot"):null;return(0,n.jsx)(m,{...e,...qt(m,u),children:(0,n.jsx)(f,{children:(0,n.jsxs)(p,{style:{borderSpacing:0,overflowAnchor:"none"},...qt(p,u),children:[v,(0,n.jsx)(xo,{},"TableBody"),I]})})})})),{Component:So,useEmitter:wo,useEmitterValue:Co,usePublisher:Ho}=zt(uo,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",firstItemIndex:"firstItemIndex",itemContent:"itemContent",groupContent:"groupContent",fixedHeaderContent:"fixedHeaderContent",fixedFooterContent:"fixedFooterContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",topItemCount:"topItemCount",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",groupCounts:"groupCounts",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},To),yo=Zt({useEmitter:wo,useEmitterValue:Co,usePublisher:Ho}),bo=Jt({useEmitter:wo,useEmitterValue:Co,usePublisher:Ho}),zo={bottom:0,itemHeight:0,items:[],itemWidth:0,offsetBottom:0,offsetTop:0,top:0},Ro={bottom:0,itemHeight:0,items:[{index:0}],itemWidth:0,offsetBottom:0,offsetTop:0,top:0},{ceil:Bo,floor:Eo,max:ko,min:Lo,round:Fo}=Math;function Oo(e,t,o){return Array.from({length:t-e+1}).map(((t,n)=>({data:null===o?null:o[n+e],index:n+e})))}function jo(e,t){return e&&e.width===t.width&&e.height===t.height}function Mo(e,t){return e&&e.column===t.column&&e.row===t.row}const Wo=M((([{increaseViewportBy:e,listBoundary:t,overscan:o,visibleRange:n},{footerHeight:r,headerHeight:i,scrollBy:l,scrollContainerState:s,scrollTo:c,scrollTop:a,smoothScrollTargetReached:u,viewportHeight:d},h,m,{didMount:f,propsReady:x},{customScrollParent:v,useWindowScroll:S,windowScrollContainerState:w,windowScrollTo:R,windowViewportRect:M},W])=>{const V=L(0),D=L(0),G=L(zo),N=L({height:0,width:0}),_=L({height:0,width:0}),U=O(),K=O(),q=L(0),$=L(null),Y=L({column:0,row:0}),X=O(),Z=O(),J=L(!1),Q=L(0),ee=L(!0),te=L(!1),oe=L(!1);p(z(f,k(Q),H((([e,t])=>!!t))),(()=>{g(ee,!1)})),p(z(P(f,ee,_,N,Q,te),H((([e,t,o,n,,r])=>e&&!t&&0!==o.height&&0!==n.height&&!r))),(([,,,,e])=>{g(te,!0),Ge(1,(()=>{g(U,e)})),T(z(a),(()=>{g(t,[0,0]),g(ee,!0)}))})),I(z(Z,H((e=>null!=e&&e.scrollTop>0)),b(0)),D),p(z(f,k(Z),H((([,e])=>null!=e))),(([,e])=>{e&&(g(N,e.viewport),g(_,e.item),g(Y,e.gap),e.scrollTop>0&&(g(J,!0),T(z(a,B(1)),(e=>{g(J,!1)})),g(c,{top:e.scrollTop})))})),I(z(N,y((({height:e})=>e))),d),I(z(P(A(N,jo),A(_,jo),A(Y,((e,t)=>e&&e.column===t.column&&e.row===t.row)),A(a)),y((([e,t,o,n])=>({gap:o,item:t,scrollTop:n,viewport:e})))),X),I(z(P(A(V),n,A(Y,Mo),A(_,jo),A(N,jo),A($),A(D),A(J),A(ee),A(Q)),H((([,,,,,,,e])=>!e)),y((([e,[t,o],n,r,i,l,s,,c,a])=>{const{column:u,row:d}=n,{height:h,width:m}=r,{width:f}=i;if(0===s&&(0===e||0===f))return zo;if(0===m){const t=Ne(a,e);return function(e){return{...Ro,items:e}}(Oo(t,t+Math.max(s-1,0),l))}const p=Po(f,m,u);let g,x;c?0===t&&0===o&&s>0?(g=0,x=s-1):(g=p*Eo((t+d)/(h+d)),x=p*Bo((o+d)/(h+d))-1,x=Lo(e-1,ko(x,p-1)),g=Lo(x,ko(0,g))):(g=0,x=-1);const v=Oo(g,x,l),{bottom:I,top:T}=Ao(i,n,r,v),S=Bo(e/p);return{bottom:I,itemHeight:h,items:v,itemWidth:m,offsetBottom:S*h+(S-1)*d-I,offsetTop:T,top:T}}))),G),I(z($,H((e=>null!==e)),y((e=>e.length))),V),I(z(P(N,_,G,Y),H((([e,t,{items:o}])=>o.length>0&&0!==t.height&&0!==e.height)),y((([e,t,{items:o},n])=>{const{bottom:r,top:i}=Ao(e,n,t,o);return[i,r]})),C(xe)),t);const ne=L(!1);I(z(a,k(ne),y((([e,t])=>t||0!==e))),ne);const re=j(z(P(G,V),H((([{items:e}])=>e.length>0)),k(ne),H((([[e,t],o])=>{const n=e.items[e.items.length-1].index===t-1;return(o||e.bottom>0&&e.itemHeight>0&&0===e.offsetBottom&&e.items.length===t)&&n})),y((([[,e]])=>e-1)),C())),ie=j(z(A(G),H((({items:e})=>e.length>0&&0===e[0].index)),b(0),C())),le=j(z(A(G),k(J),H((([{items:e},t])=>e.length>0&&!t)),y((([{items:e}])=>({endIndex:e[e.length-1].index,startIndex:e[0].index}))),C(ge),E(0)));I(le,m.scrollSeekRangeChanged),I(z(U,k(N,_,V,Y),y((([e,t,o,n,r])=>{const i=Ve(e),{align:l,behavior:s,offset:c}=i;let a=i.index;"LAST"===a&&(a=n-1),a=ko(0,a,Lo(n-1,a));let u=Vo(t,r,o,a);return"end"===l?u=Fo(u-t.height+o.height):"center"===l&&(u=Fo(u-t.height/2+o.height/2)),c&&(u+=c),{behavior:s,top:u}}))),c);const se=F(z(G,y((e=>e.offsetBottom+e.bottom))),0);return I(z(M,y((e=>({height:e.visibleHeight,width:e.visibleWidth})))),N),{customScrollParent:v,data:$,deviation:q,footerHeight:r,gap:Y,headerHeight:i,increaseViewportBy:e,initialItemCount:D,itemDimensions:_,overscan:o,restoreStateFrom:Z,scrollBy:l,scrollContainerState:s,scrollHeight:K,scrollTo:c,scrollToIndex:U,scrollTop:a,smoothScrollTargetReached:u,totalCount:V,useWindowScroll:S,viewportDimensions:N,windowScrollContainerState:w,windowScrollTo:R,windowViewportRect:M,...m,gridState:G,horizontalDirection:oe,initialTopMostItemIndex:Q,totalListHeight:se,...h,endReached:re,propsReady:x,rangeChanged:le,startReached:ie,stateChanged:X,stateRestoreInProgress:J,...W}}),f(tt,Y,Ye,pt,Pe,gt,N));function Po(e,t,o){return ko(1,Eo((e+o)/(Eo(t)+o)))}function Ao(e,t,o,n){const{height:r}=o;if(void 0===r||0===n.length)return{bottom:0,top:0};const i=Vo(e,t,o,n[0].index);return{bottom:Vo(e,t,o,n[n.length-1].index)+r,top:i}}function Vo(e,t,o,n){const r=Po(e.width,o.width,t.column),i=Eo(n/r),l=i*o.height+ko(0,i-1)*t.row;return l>0?l+t.row:l}const Do=M((([e,t])=>({...e,...t})),f(Wo,M((()=>{const e=L((e=>`Item ${e}`)),t=L({}),o=L(null),n=L("virtuoso-grid-item"),r=L("virtuoso-grid-list"),i=L(Mt),l=L("div"),s=L(d),c=(e,o=null)=>F(z(t,y((t=>t[e])),C()),o),a=L(!1),u=L(!1);return I(A(u),a),{components:t,computeItemKey:i,context:o,FooterComponent:c("Footer"),HeaderComponent:c("Header"),headerFooterTag:l,itemClassName:n,ItemComponent:c("Item","div"),itemContent:e,listClassName:r,ListComponent:c("List","div"),readyStateChanged:a,reportReadyState:u,ScrollerComponent:c("Scroller","div"),scrollerRef:s,ScrollSeekPlaceholder:c("ScrollSeekPlaceholder","div")}})))),Go=r.memo((function(){const e=Xo("gridState"),t=Xo("listClassName"),o=Xo("itemClassName"),i=Xo("itemContent"),l=Xo("computeItemKey"),s=Xo("isSeeking"),c=Zo("scrollHeight"),a=Xo("ItemComponent"),u=Xo("ListComponent"),d=Xo("ScrollSeekPlaceholder"),h=Xo("context"),m=Zo("itemDimensions"),f=Zo("gap"),p=Xo("log"),g=Xo("stateRestoreInProgress"),x=Zo("reportReadyState"),v=_(r.useMemo((()=>e=>{const t=e.parentElement.parentElement.scrollHeight;c(t);const o=e.firstChild;if(o){const{height:e,width:t}=o.getBoundingClientRect();m({height:e,width:t})}f({column:en("column-gap",getComputedStyle(e).columnGap,p),row:en("row-gap",getComputedStyle(e).rowGap,p)})}),[c,m,f,p]),!0,!1);return Et((()=>{e.itemHeight>0&&e.itemWidth>0&&x(!0)}),[e]),g?null:(0,n.jsx)(u,{className:t,ref:v,...qt(u,h),"data-testid":"virtuoso-item-list",style:{paddingBottom:e.offsetBottom,paddingTop:e.offsetTop},children:e.items.map((t=>{const c=l(t.index,t.data,h);return s?(0,n.jsx)(d,{...qt(d,h),height:e.itemHeight,index:t.index,width:e.itemWidth},c):(0,r.createElement)(a,{...qt(a,h),className:o,"data-index":t.index,key:c},i(t.index,t.data,h))}))})})),No=r.memo((function(){const e=Xo("HeaderComponent"),t=Zo("headerHeight"),o=Xo("headerFooterTag"),i=_(r.useMemo((()=>e=>{t(Se(e,"height"))}),[t]),!0,!1),l=Xo("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...qt(e,l)})}):null})),_o=r.memo((function(){const e=Xo("FooterComponent"),t=Zo("footerHeight"),o=Xo("headerFooterTag"),i=_(r.useMemo((()=>e=>{t(Se(e,"height"))}),[t]),!0,!1),l=Xo("context");return e?(0,n.jsx)(o,{ref:i,children:(0,n.jsx)(e,{...qt(e,l)})}):null})),Uo=({children:e})=>{const t=r.useContext(Bt),o=Zo("itemDimensions"),i=Zo("viewportDimensions"),l=_(r.useMemo((()=>e=>{i(e.getBoundingClientRect())}),[i]),!0,!1);return r.useEffect((()=>{t&&(i({height:t.viewportHeight,width:t.viewportWidth}),o({height:t.itemHeight,width:t.itemWidth}))}),[t,i,o]),(0,n.jsx)("div",{ref:l,style:Ut(!1),children:e})},Ko=({children:e})=>{const t=r.useContext(Bt),o=Zo("windowViewportRect"),i=Zo("itemDimensions"),l=Xo("customScrollParent"),s=$(o,l,!1);return r.useEffect((()=>{t&&(i({height:t.itemHeight,width:t.itemWidth}),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:t.viewportWidth}))}),[t,o,i]),(0,n.jsx)("div",{ref:s,style:Ut(!1),children:e})},qo=r.memo((function({...e}){const t=Xo("useWindowScroll"),o=Xo("customScrollParent"),r=o||t?Qo:Jo,i=o||t?Ko:Uo,l=Xo("context");return(0,n.jsx)(r,{...e,...qt(r,l),children:(0,n.jsxs)(i,{children:[(0,n.jsx)(No,{}),(0,n.jsx)(Go,{}),(0,n.jsx)(_o,{})]})})})),{Component:$o,useEmitter:Yo,useEmitterValue:Xo,usePublisher:Zo}=zt(Do,{optional:{context:"context",totalCount:"totalCount",overscan:"overscan",itemContent:"itemContent",components:"components",computeItemKey:"computeItemKey",data:"data",initialItemCount:"initialItemCount",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"headerFooterTag",listClassName:"listClassName",itemClassName:"itemClassName",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",restoreStateFrom:"restoreStateFrom",initialTopMostItemIndex:"initialTopMostItemIndex",increaseViewportBy:"increaseViewportBy"},methods:{scrollTo:"scrollTo",scrollBy:"scrollBy",scrollToIndex:"scrollToIndex"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",stateChanged:"stateChanged",readyStateChanged:"readyStateChanged"}},qo),Jo=Zt({useEmitter:Yo,useEmitterValue:Xo,usePublisher:Zo}),Qo=Jt({useEmitter:Yo,useEmitterValue:Xo,usePublisher:Zo});function en(e,t,o){return"normal"!==t&&!(null!=t&&t.endsWith("px"))&&o(`${e} was not resolved to pixel value correctly`,t,D.WARN),"normal"===t?0:parseInt(null!=t?t:"0",10)}}}]);
//# sourceMappingURL=react-virtuoso.8751b6b04868a0ac420698d3d3dd8ea4.js.map