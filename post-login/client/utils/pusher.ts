import Pusher, { Channel } from 'pusher-js'
import { configKeysStore } from '../stores/ConfigKeysStore';
import * as _ from 'lodash';

Pusher.logToConsole = true;

let pusher: Pusher | null = null

function getPusher() {
  if (pusher) {
    return pusher
  } else {
    const configKeys = configKeysStore.getConfigKeys;
    pusher = new Pusher(configKeys.pusher_key, {
      cluster: configKeys.pusher_cluster
    });
    return pusher
  }
}

interface SRPusherEventData<T> {
  event_name: string,
  event_data: T
}

let channelBindedEvents: {
  [channel_name: string]: {
    event_names: string[],
    channel_instance: Channel
  }
} = {};

function getChannel(channelName: string) {
  const pusher = getPusher();
  const channelObj = channelBindedEvents[channelName];
  if (channelObj) {
    return channelObj.channel_instance;
  } else {
    return pusher.subscribe(channelName);
  }
}

function unsubscribeChannel(channelName: string) {
  const pusher = getPusher();
  const channelObj = channelBindedEvents[channelName];
  if (channelObj)
    return pusher.unsubscribe(channelName);
  else
    return

}

export function pusherUnsubscribe(
  data: {
    channel_name: string
  }

) {
  const channel = getChannel(data.channel_name)
  channel.unbind_all()
  unsubscribeChannel(data.channel_name)
  channelBindedEvents = {}
}

export function pusherSubscribe<T>(
  input: {
    channel_name: string,
    event_name: string,
    on_event: (data: SRPusherEventData<T>) => void
  }
) {
  const channelName = input.channel_name
  const channel = getChannel(channelName)
  let channelObj = channelBindedEvents[channelName]

  if ((channelObj && !channelObj.event_names.includes(input.event_name)) || !channelObj) {
    channel.bind(input.event_name, function (response: string) {

      console.log("On-Event Response -> ", response)

      if (!channelObj) {
        channelBindedEvents[channelName] = {
          channel_instance: channel,
          event_names: [input.event_name]
        }

      } else {
        channelBindedEvents[channelName].event_names.push(input.event_name);
      }
      input.on_event({
        event_name: input.event_name,
        event_data: JSON.parse(response)
      })
    })
  }

}