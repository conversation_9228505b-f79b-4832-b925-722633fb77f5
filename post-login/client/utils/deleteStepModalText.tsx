import * as React from "react";
import { getChannelNameFromChannelType } from "./campaignStepData";
import { SRIconWarning } from "@sr/design-component";
import { Campaigns } from "@sr/shared-product-components";

export function OnDeleteStepText(channelType: Campaigns.ChannelType){

  const channel = getChannelNameFromChannelType(channelType)

  return <div className="py-[24px]"> You are about to delete a campaign {channel} step.
            
            <br />
            <br />

            {/* If you just want to change the {channel} content, please edit the content in the current step and "Save changes" instead of deleting the step. */}
            It may affect prospects who are still active in the campaign. If you only need to update the {channel} content, please edit it and save changes instead of deleting the step.
          </div>

}



export function OnDeleteDripNodeText(node?:string){

  // const channel = getChannelNameFromChannelType(channelType)

  return <div className="py-[24px]"> You’re about to delete the condition <strong>{node}</strong> from your campaign.

            <div className="flex justify-between rounded-[8px] px-[32px] py-[24px] bg-sr-warning-10 my-[16px]">
              <div className="pt-[16px]">
              <div className="flex items-center justify-center"><SRIconWarning className="text-sr-warning-70"/></div>
              <div className="sr-h4 text-sr-warning-70">WARNING</div>
              </div>
              <ul className="list-outside max-w-[300px]">
            <li><div className="pt-3 pl-3 sr-h6">This action will remove the selected condition and all steps below it. </div></li>
            <li><div className="pt-3 pl-3 sr-h6">Prospects currently active in any of these steps may be affected.</div></li>
            </ul>
            </div>


            {/* If you just want to change the {channel} content, please edit the content in the current step and "Save changes" instead of deleting the step. */}
            If you only need to update the condition logic, please edit and save changes instead of deleting it.
          </div>

}