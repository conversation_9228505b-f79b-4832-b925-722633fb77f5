"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["regexp.prototype.flags"],{73994:function(e,t,o){var r=o(25021),i=o(91642),s=Object;e.exports=r((function(){if(null==this||this!==s(this))throw new i("RegExp.prototype.flags getter called on non-object");var e="";return this.hasIndices&&(e+="d"),this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),this.dotAll&&(e+="s"),this.unicode&&(e+="u"),this.unicodeSets&&(e+="v"),this.sticky&&(e+="y"),e}),"get flags",!0)},80251:function(e,t,o){var r=o(19170),i=o(12550),s=o(73994),p=o(71856),n=o(47278),a=i(p());r(a,{getPolyfill:p,implementation:s,shim:n}),e.exports=a},71856:function(e,t,o){var r=o(73994),i=o(19170).supportsDescriptors,s=Object.getOwnPropertyDescriptor;e.exports=function(){if(i&&"gim"===/a/gim.flags){var e=s(RegExp.prototype,"flags");if(e&&"function"===typeof e.get&&"boolean"===typeof RegExp.prototype.dotAll&&"boolean"===typeof RegExp.prototype.hasIndices){var t="",o={};if(Object.defineProperty(o,"hasIndices",{get:function(){t+="d"}}),Object.defineProperty(o,"sticky",{get:function(){t+="y"}}),"dy"===t)return e.get}}return r}},47278:function(e,t,o){var r=o(19170).supportsDescriptors,i=o(71856),s=Object.getOwnPropertyDescriptor,p=Object.defineProperty,n=TypeError,a=Object.getPrototypeOf,c=/a/;e.exports=function(){if(!r||!a)throw new n("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var e=i(),t=a(c),o=s(t,"flags");return o&&o.get===e||p(t,"flags",{configurable:!0,enumerable:!1,get:e}),e}}}]);
//# sourceMappingURL=regexp.prototype.flags.3e3675db1707673e5e7541f579d161c8.js.map