import {find as lo_find} from 'lodash'
import { providerSettingsV2 } from '../data/email-account-providers-data';
import { Settings } from '@sr/shared-product-components';

export function getEmptyEmailData(defaultLowerLimitForEmailDelay?: number, defaultUpperLimitForEmailDelay?: number, currentServiceProviderData?: Settings.IEmailSettingsV2 ,service_provider?: Settings.IEmailProviderKey): Settings.IEmailData {
  console.log("service => " , service_provider )
  if(!!!currentServiceProviderData){
    return {
      first_name: '',
      last_name: '',
      error: null,
      can_send: false,
      can_receive: false,
      created_at: 0,
      email: '',
      email_address_host: '',
      id: 0,
      uuid: '',
      can_auto_start_warmup: false,
      can_auto_reconnect_via_oauth: false,
      quota_per_day: 100,
      oauth2_enabled: false,
      service_provider: '',

      min_delay_seconds: defaultLowerLimitForEmailDelay? defaultLowerLimitForEmailDelay: 30,
      max_delay_seconds: defaultUpperLimitForEmailDelay? defaultUpperLimitForEmailDelay: 90,

      // username: '',
      smtp_username: '',
      smtp_password: '',
      smtp_host: '',
      smtp_port: null,

      imap_username: '',
      imap_password: '',
      imap_host: '',
      imap_port: null,
      campaign_use_status_for_email_setting: 'is_not_assigned_to_any_campaign'

    }
  }
  else if (service_provider === "gmail_asp") {
    return {
      first_name: "",
      last_name: "",
      error: null,
      can_send: true,
      can_receive: true,
      created_at: 0,
      email: "",
      email_address_host: "",
      id: 0,
      uuid: "",
      can_auto_start_warmup: false,
      can_auto_reconnect_via_oauth: false,
      quota_per_day: 100,
      oauth2_enabled: false,
      service_provider: "",

      min_delay_seconds: defaultLowerLimitForEmailDelay? defaultLowerLimitForEmailDelay: 30,
      max_delay_seconds: defaultUpperLimitForEmailDelay? defaultUpperLimitForEmailDelay: 90,
      app_specific_password: "",
      // username: '',
      smtp_username: "email", //to skip validation
      smtp_password: "", //to enable validation on app specific passwords and passwords field.
      smtp_host: "smtp.gmail.com",
      smtp_port: 465,

      imap_username: "email", //to skip validation
      imap_password: "password", //to skip validation
      imap_host: "imap.gmail.com",
      imap_port: 993,

      bcc_emails: "",
      cc_emails: "",
      campaign_use_status_for_email_setting: 'is_not_assigned_to_any_campaign'


    };
  } else {
    return {
      first_name: "",
      last_name: "",
      error: null,
      can_send: true,
      can_receive: true,
      created_at: 0,
      email: "",
      email_address_host: "",
      id: 0,
      uuid: "",
      can_auto_start_warmup: false,
      can_auto_reconnect_via_oauth: false,
      quota_per_day: 100,
      oauth2_enabled: false,
      service_provider: "",

      min_delay_seconds: defaultLowerLimitForEmailDelay? defaultLowerLimitForEmailDelay: 30,
      max_delay_seconds: defaultUpperLimitForEmailDelay? defaultUpperLimitForEmailDelay: 90,

      // username: '',
      smtp_username: "",
      smtp_password: "",
      smtp_host: currentServiceProviderData?.smtp_host || "",
      smtp_port: currentServiceProviderData?.smtp_port != null
          ? currentServiceProviderData?.smtp_port[0]
          : null,

      imap_username: "",
      imap_password: "",
      imap_host: currentServiceProviderData?.imap_host || "",
      imap_port:
        currentServiceProviderData?.imap_port != null
          ? currentServiceProviderData?.imap_port[0]
          : null,

      bcc_emails: "",
      cc_emails: "",

      api_key: "",
      mailgun_priv_key: "",
      email_domain: "",
      mailgun_region: 'us',
      campaign_use_status_for_email_setting: 'is_not_assigned_to_any_campaign'

    };
  }
}

export function getCurrentServiceProviderData(targetSP?: Settings.IEmailProviderKey): Settings.IEmailSettingsV2{
  let currentServiceProviderData: Settings.IEmailSettingsV2 | undefined = lo_find(providerSettingsV2, (o) => {
    return o.service_provider === targetSP;
  });

  if (!currentServiceProviderData) {
    currentServiceProviderData = {
      service_provider: "other",
      smtp_host: "",
      smtp_port: null,
      actions: "send_receive",
      imap_host: "",
      imap_port: null,
      same_smtp_imap_mail: false,
    };
  }

  return currentServiceProviderData
}