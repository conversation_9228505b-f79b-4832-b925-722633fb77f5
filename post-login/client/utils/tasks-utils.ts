import { Task } from "@sr/shared-product-components";


export function getTaskName(task_type: Task.ITaskType): string {
  if (task_type === 'general_task')
    return "General Task";
  else if (task_type === 'send_email')
    return 'Send Email'
  else if (task_type === 'auto_email_magic_content')
    return 'Auto Email Magic Content'
  else if (task_type === 'auto_linkedin_view_profile')
    return 'Auto Linkedin View Profile'
  else if (task_type === 'auto_send_linkedin_connection_request')
    return 'Auto Send Linkedin Connection Request'
  else if (task_type === 'auto_send_linkedin_inmail')
    return 'Auto Send Linkedin InMail'
  else if (task_type === 'manual_send_email')
    return "Manual Send Email"
  else if (task_type === 'linkedin_view_profile')
    return "LinkedIn View Profile"
  else if (task_type === 'send_linkedin_connection_request')
    return "Send LinkedIn Connection Request"
  else if (task_type === 'send_linkedin_message')
    return "Send LinkedIn Message"
  else if (task_type === 'send_linkedin_inmail')
    return "Send LinkedIn Inmail"
  else if (task_type === 'send_sms')
    return "Send Sms"
  else if (task_type === 'send_whatsapp_message')
    return "Send WhatsApp Message"
  else if (task_type === 'call')
    return "Call"
  else if (task_type === 'auto_send_linkedin_message')
    return "Auto Send LinkedIn Message"
  else if (task_type === 'move_to_another_campaign')
    return "Move to Another Campaign"
  else if (task_type === 'manual_email_magic_content')
    return "Manual Email Magic Content"
  else return task_type as string;
}

export function getStatusColor(status: Task.ITaskStatusType): string {
  if (status === 'due')
    return '#E21D12'
  else if (status === 'done')
    return '#19994F'
  else if (status === 'snoozed')
    return '#E8C515'
  else if (status === 'skipped')
    return '#EF8983'
  else if (status === 'archived')
    return '#A9B0BC'
  else if (status === 'failed')
    return '#FF0000'
  else
    return '#A9B0BC'
}


// function to remove 0 from ids which is coming after we de-select items from report filter dropdowns
export function getIds(ids?: number[]): number[] {

  if (!!ids) {

    let ids_: number[] = [];

    ids.map(d => {

      if (d != 0) {

        ids_.push(d)
      }

    })

    return ids_;

  }

  return [];

}


export function getSubject(taskData: Task.ITaskData) {
  if ('generated_subject' in taskData && taskData.generated_subject) return taskData.generated_subject;
  if ('subject' in taskData && taskData.subject) return taskData.subject;
  if ('request_message' in taskData && taskData.request_message) return taskData.request_message;
  return '';
};

// Helper function to get body/content
export function getBody(taskData: Task.ITaskData) {
  if ('email_scheduled_text_body' in taskData && taskData.email_scheduled_text_body) return taskData.email_scheduled_text_body;
  if ('body' in taskData && taskData.body) return taskData.body;
  if ('request_message' in taskData && taskData.request_message) return taskData.request_message;
  if ('message' in taskData && taskData.message) return taskData.message;
  return 'No Content';
};


export function getStatusLabel(taskStatus: Task.ITaskStatus) {
  const status = taskStatus.status_type;
  if (status === 'done') return 'Completed at';
  if (status === 'failed') return 'Failed at';
  if (status === 'skipped') return 'Skipped at';
  if (status === 'approved') return 'Approved at';
  return '-';
};

// Get completed/failed/skipped date
export function getStatusDate(taskStatus: Task.ITaskStatus) {
  const status = taskStatus.status_type;
  if (status === 'done' && 'done_at' in taskStatus) return new Date((taskStatus as any).done_at).toLocaleString();
  if (status === 'failed' && 'failed_at' in taskStatus) return new Date((taskStatus as any).failed_at).toLocaleString();
  if (status === 'skipped' && 'skipped_at' in taskStatus) return new Date((taskStatus as any).skipped_at).toLocaleString();
  if (status === 'approved' && 'done_at' in taskStatus) return new Date((taskStatus as any).done_at).toLocaleString();
  return '-';
};



export function getFirstLine(text: string) {

  const normalizedText = text.replace(/<br \/>/g, '<br>');
  return normalizedText.split('<br>')[0] || '';
};

