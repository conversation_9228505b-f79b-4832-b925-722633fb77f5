declare namespace Conference{

  interface IOngoingCallProspectDetails {
    prospect_id: number,
    first_name?: string ,
    last_name?: string,
    phone_number?: string
  }

  type ConferenceParticipationMode = 'listen' | 'whisper' | 'barge_in' | 'initiator'
  type CallingDevice = 'extension' | 'web-app' | 'mobile'

  interface IActiveCallParticipantDetails{
    conference_uuid: string,
    participant_uuid: string,
    latest_participation_mode: ConferenceParticipationMode,
    task_id ?: string,
    isConfMuted?: boolean,
    ongoingCallProspectDetails ?: IOngoingCallProspectDetails,
    participant_account_id ?: number,
    participant_first_name?: string,
    calling_device?: CallingDevice,
    participant_last_name?: string
  }

  interface ICurrentActiveCallsInTeam{
    active_call_participant_details: IActiveCallParticipantDetails[],
    current_conference_of_account: IActiveCallParticipantDetails,
    showActiveCallBanner: boolean,
    current_call_sid?: string,
    current_call_note?: string,
    current_task_id?: string,
  }

  interface IActiveConferenceDetails{
    currentActiveCall: ICurrentActiveCallsInTeam,
    getCurrentCallDetails: (account_id: number | undefined) => Conference.IActiveCallParticipantDetails | undefined
    
    isUserInitiator: (account_id: number | undefined) => boolean,
    showCallNoteForProspect: (data: {account_id: number | undefined, prospect_id: number}) => boolean,
    setActiveCallParticipantDetails: (active_call_details: IActiveCallParticipantDetails[]) => void
    getActiveCallParticipantDetails: IActiveCallParticipantDetails[],
    setCurrentOngoingConferenceOfUser: (call_details: Conference.IActiveCallParticipantDetails) => void,
    getCurrentOngoingConferenceOfUser: IActiveCallParticipantDetails,
    setCurrentCallSid: (val: string | undefined) => void,
    getCurrentCallSid: string | undefined,
    resetCurrentCallNoteDetails:() => void,
    setCurrentCallNote: (val: string | undefined) => void,
    getCurrentCallNote: string | undefined,
    setCurrentTaskId: (val: string | undefined) => void,
    getCurrentTaskId: string | undefined,
    setShowActiveCallBanner: (val: boolean) => void,
    getShowActiveCallBanner: boolean,
    resetState: () => void
  }
}