import * as _ from 'lodash';

/**
 * Input: " First Name "
 * Output: "first_name"
 *
 */
export function genSRColumnName(columnName: string) {

  return _.chain(columnName)
    .toLower()
    .split(' ')
    .filter(splitText => !_.isEmpty(_.trim(splitText)))
    .join("_")
    .value()

}

export function autoMapIdenticalColumns(
  submitData: any[],
  csvColumns: {
    id: string;
    name: string;
    fieldName: string;
  }[],
  srColumns: {
    name: string;
    key: string;
    field_type: string;
    is_new: boolean;
  }[],
  lastCsvColumnMap?: { [key: string]: string },
  filteredColumns?: {
    name: string;
    key: string;
    field_type: string;
    is_new: boolean;
  }[]
) {

  const checkedColumns: {
    name: string;
    key: string;
    field_type: string;
    is_new: boolean;
  }[] = []

  const updatedSubmitData = _.cloneDeep(submitData)

  for (let i = 0; i < srColumns.length; i++) {
    for (let j = 0; j < csvColumns.length; j++) {
      if (genSRColumnName(csvColumns[j].fieldName) === genSRColumnName(srColumns[i].key)) {
        checkedColumns.push(srColumns[i])

        updatedSubmitData[j].key = srColumns[i].key;
        updatedSubmitData[j].display_name = srColumns[i].name;
        updatedSubmitData[j].is_new = srColumns[i].is_new;
        updatedSubmitData[j].field_type = srColumns[i].field_type;
        updatedSubmitData[j].checked = true;

      } else if (!!lastCsvColumnMap && lastCsvColumnMap[srColumns[i].key] === csvColumns[j].name) {
        checkedColumns.push(srColumns[i])

        updatedSubmitData[j].key = srColumns[i].key;
        updatedSubmitData[j].display_name = srColumns[i].name;
        updatedSubmitData[j].is_new = srColumns[i].is_new;
        updatedSubmitData[j].field_type = srColumns[i].field_type;
        updatedSubmitData[j].checked = true;
      }
    }
  }

  const filteredColumnsUpdated = filteredColumns?.filter(c => !checkedColumns.includes(c))

  return [updatedSubmitData, filteredColumnsUpdated]

}

export function getColumnDisplayNames(selectedUniqueColumns: string[]): string[] {
  const columnNames: string[] = [];

  if (selectedUniqueColumns.includes('email')) {
    columnNames.push('Email');
  }
  if (selectedUniqueColumns.includes('phone')) {
    columnNames.push('Phone');
  }
  if (selectedUniqueColumns.includes('linkedin_url')) {
    columnNames.push('LinkedIn');
  }
  if (selectedUniqueColumns.includes('company_firstname_lastname')) {
    columnNames.push('First name, Last name and Company');
  }

  return columnNames;
}

export function formatColumnNames(names: string[]): string {
  if (names.length === 0) return '';
  if (names.length === 1) return names[0];
  if (names.length === 2) return `${names[0]} and ${names[1]}`;

  // For 3 or more items: "A, B, and C"
  const lastItem = names[names.length - 1];
  const firstItems = names.slice(0, -1);
  return `${firstItems.join(', ')} and ${lastItem}`;
}
