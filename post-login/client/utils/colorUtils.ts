export const prospectCategoryColors = [
    '#1f78b4',
    '#aec6e8',
    '#fe7f0e',
    '#ffbc78',
    '#2ba02d',
    '#97df89',
    '#d52728',
    '#ff9897',
    '#9467bc',
    '#c4b0d5',
    '#8c564a',
    '#c49c94',
    '#e377c2',
    '#f7b7d2',
    '#7f7f7f',
    '#c7c7c7',
    '#bcbd23',
    '#dadb8d',
    '#17bed0',
    '#9edae5',
];


// REF: https://stackoverflow.com/a/5624139
function hexToRgb(hex: string) {
    // Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
    var shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    hex = hex.replace(shorthandRegex, function (m, r, g, b) {
        return r + r + g + g + b + b;
    });

    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}


// REF: https://trendct.org/2016/01/22/how-to-choose-a-label-color-to-contrast-with-background/
function isBrightColor(hex: string): boolean {

    const c = hexToRgb(hex)

    if (!c) {
        return true
    } else {
        return ((c.r * 299 + c.g * 587 + c.b * 114) / 1000) > 160
    }
}

export function labelTextColor(backgroundColorHex: string) {

    if (isBrightColor(backgroundColorHex)) {
        return 'black';
    } else {
        return 'white';
    }

}

export const reportColors = {
    sentColor: '#5599c7',
    bouncedColor: '#CC0000',
    openedColor: '#61B861',
    positiveColor: '#21B920',
    clickColor: '#795894',
    unsubscribeColor: '#AEACAC',
    repliedColor: '#FF9E4A'
}

export const linkedInReportColors = {
    connectionRequestColor: '#3E9B12', 
    messageColor: '#D20761',             
    inMailColor: '#FAA00F',            
    profileVisitColor: '#0F69FA'
}

// #f6f8fb;
