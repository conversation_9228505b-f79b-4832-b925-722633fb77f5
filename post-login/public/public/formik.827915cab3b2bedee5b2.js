"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["formik"],{9834:function(e,t,r){r.d(t,{Bc:function(){return J},gN:function(){return x},l0:function(){return H},J9:function(){return V},U$:function(){return Z}});var n=r(89526),a=r(15439),i=r.n(a),u=r(6674),o=r(44199),l=r(37337),c=r(72975),s=r(96249),f=r(41281),d=r.n(f),p=r(52965);function v(){return v=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},v.apply(this,arguments)}function h(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function m(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}function y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var E=function(e){return Array.isArray(e)&&0===e.length},S=function(e){return"function"===typeof e},T=function(e){return null!==e&&"object"===typeof e},b=function(e){return String(Math.floor(Number(e)))===e},g=function(e){return"[object String]"===Object.prototype.toString.call(e)},_=function(e){return 0===n.Children.count(e)},R=function(e){return T(e)&&S(e.then)};function A(e,t,r,n){void 0===n&&(n=0);for(var a=(0,c.Z)(t);e&&n<a.length;)e=e[a[n++]];return void 0===e?r:e}function F(e,t,r){for(var n=(0,l.Z)(e),a=n,i=0,u=(0,c.Z)(t);i<u.length-1;i++){var o=u[i],s=A(e,u.slice(0,i+1));if(s&&(T(s)||Array.isArray(s)))a=a[o]=(0,l.Z)(s);else{var f=u[i+1];a=a[o]=b(f)&&Number(f)>=0?[]:{}}}return(0===i?e:a)[u[i]]===r?e:(void 0===r?delete a[u[i]]:a[u[i]]=r,0===i&&void 0===r&&delete n[u[i]],n)}function O(e,t,r,n){void 0===r&&(r=new WeakMap),void 0===n&&(n={});for(var a=0,i=Object.keys(e);a<i.length;a++){var u=i[a],o=e[u];T(o)?r.get(o)||(r.set(o,!0),n[u]=Array.isArray(o)?[]:{},O(o,t,r,n[u])):n[u]=t}return n}var k=(0,n.createContext)(void 0);k.displayName="FormikContext";var C=k.Provider,I=k.Consumer;function M(){var e=(0,n.useContext)(k);return e||(0,s.Z)(!1),e}function D(e,t){switch(t.type){case"SET_VALUES":return v({},e,{values:t.payload});case"SET_TOUCHED":return v({},e,{touched:t.payload});case"SET_ERRORS":return i()(e.errors,t.payload)?e:v({},e,{errors:t.payload});case"SET_STATUS":return v({},e,{status:t.payload});case"SET_ISSUBMITTING":return v({},e,{isSubmitting:t.payload});case"SET_ISVALIDATING":return v({},e,{isValidating:t.payload});case"SET_FIELD_VALUE":return v({},e,{values:F(e.values,t.payload.field,t.payload.value)});case"SET_FIELD_TOUCHED":return v({},e,{touched:F(e.touched,t.payload.field,t.payload.value)});case"SET_FIELD_ERROR":return v({},e,{errors:F(e.errors,t.payload.field,t.payload.value)});case"RESET_FORM":return v({},e,t.payload);case"SET_FORMIK_STATE":return t.payload(e);case"SUBMIT_ATTEMPT":return v({},e,{touched:O(e.values,!0),isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":case"SUBMIT_SUCCESS":return v({},e,{isSubmitting:!1});default:return e}}var P={},U={};function L(e){var t=e.validateOnChange,r=void 0===t||t,a=e.validateOnBlur,o=void 0===a||a,l=e.validateOnMount,c=void 0!==l&&l,s=e.isInitialValid,f=e.enableReinitialize,d=void 0!==f&&f,p=e.onSubmit,h=m(e,["validateOnChange","validateOnBlur","validateOnMount","isInitialValid","enableReinitialize","onSubmit"]),y=v({validateOnChange:r,validateOnBlur:o,validateOnMount:c,onSubmit:p},h),E=(0,n.useRef)(y.initialValues),b=(0,n.useRef)(y.initialErrors||P),_=(0,n.useRef)(y.initialTouched||U),O=(0,n.useRef)(y.initialStatus),k=(0,n.useRef)(!1),C=(0,n.useRef)({});(0,n.useEffect)((function(){return k.current=!0,function(){k.current=!1}}),[]);var I=(0,n.useReducer)(D,{values:y.initialValues,errors:y.initialErrors||P,touched:y.initialTouched||U,status:y.initialStatus,isSubmitting:!1,isValidating:!1,submitCount:0}),M=I[0],L=I[1],V=(0,n.useCallback)((function(e,t){return new Promise((function(r,n){var a=y.validate(e,t);null==a?r(P):R(a)?a.then((function(e){r(e||P)}),(function(e){n(e)})):r(a)}))}),[y.validate]),B=(0,n.useCallback)((function(e,t){var r=y.validationSchema,n=S(r)?r(t):r,a=t&&n.validateAt?n.validateAt(t,e):function(e,t,r,n){void 0===r&&(r=!1);void 0===n&&(n={});var a=w(e);return t[r?"validateSync":"validate"](a,{abortEarly:!1,context:n})}(e,n);return new Promise((function(e,t){a.then((function(){e(P)}),(function(r){"ValidationError"===r.name?e(function(e){var t={};if(e.inner){if(0===e.inner.length)return F(t,e.path,e.message);var r=e.inner,n=Array.isArray(r),a=0;for(r=n?r:r[Symbol.iterator]();;){var i;if(n){if(a>=r.length)break;i=r[a++]}else{if((a=r.next()).done)break;i=a.value}var u=i;A(t,u.path)||(t=F(t,u.path,u.message))}}return t}(r)):t(r)}))}))}),[y.validationSchema]),Z=(0,n.useCallback)((function(e,t){return new Promise((function(r){return r(C.current[e].validate(t))}))}),[]),x=(0,n.useCallback)((function(e){var t=Object.keys(C.current).filter((function(e){return S(C.current[e].validate)})),r=t.length>0?t.map((function(t){return Z(t,A(e,t))})):[Promise.resolve("DO_NOT_DELETE_YOU_WILL_BE_FIRED")];return Promise.all(r).then((function(e){return e.reduce((function(e,r,n){return"DO_NOT_DELETE_YOU_WILL_BE_FIRED"===r||r&&(e=F(e,t[n],r)),e}),{})}))}),[Z]),H=(0,n.useCallback)((function(e){return Promise.all([x(e),y.validationSchema?B(e):{},y.validate?V(e):{}]).then((function(e){var t=e[0],r=e[1],n=e[2];return u.Z.all([t,r,n],{arrayMerge:j})}))}),[y.validate,y.validationSchema,x,V,B]),G=N((function(e){return void 0===e&&(e=M.values),L({type:"SET_ISVALIDATING",payload:!0}),H(e).then((function(e){return k.current&&(L({type:"SET_ISVALIDATING",payload:!1}),L({type:"SET_ERRORS",payload:e})),e}))}));(0,n.useEffect)((function(){c&&!0===k.current&&i()(E.current,y.initialValues)&&G(E.current)}),[c,G]);var W=(0,n.useCallback)((function(e){var t=e&&e.values?e.values:E.current,r=e&&e.errors?e.errors:b.current?b.current:y.initialErrors||{},n=e&&e.touched?e.touched:_.current?_.current:y.initialTouched||{},a=e&&e.status?e.status:O.current?O.current:y.initialStatus;E.current=t,b.current=r,_.current=n,O.current=a;var i=function(){L({type:"RESET_FORM",payload:{isSubmitting:!!e&&!!e.isSubmitting,errors:r,touched:n,status:a,values:t,isValidating:!!e&&!!e.isValidating,submitCount:e&&e.submitCount&&"number"===typeof e.submitCount?e.submitCount:0}})};if(y.onReset){var u=y.onReset(M.values,se);R(u)?u.then(i):i()}else i()}),[y.initialErrors,y.initialStatus,y.initialTouched]);(0,n.useEffect)((function(){!0!==k.current||i()(E.current,y.initialValues)||(d&&(E.current=y.initialValues,W()),c&&G(E.current))}),[d,y.initialValues,W,c,G]),(0,n.useEffect)((function(){d&&!0===k.current&&!i()(b.current,y.initialErrors)&&(b.current=y.initialErrors||P,L({type:"SET_ERRORS",payload:y.initialErrors||P}))}),[d,y.initialErrors]),(0,n.useEffect)((function(){d&&!0===k.current&&!i()(_.current,y.initialTouched)&&(_.current=y.initialTouched||U,L({type:"SET_TOUCHED",payload:y.initialTouched||U}))}),[d,y.initialTouched]),(0,n.useEffect)((function(){d&&!0===k.current&&!i()(O.current,y.initialStatus)&&(O.current=y.initialStatus,L({type:"SET_STATUS",payload:y.initialStatus}))}),[d,y.initialStatus,y.initialTouched]);var K=N((function(e){if(C.current[e]&&S(C.current[e].validate)){var t=A(M.values,e),r=C.current[e].validate(t);return R(r)?(L({type:"SET_ISVALIDATING",payload:!0}),r.then((function(e){return e})).then((function(t){L({type:"SET_FIELD_ERROR",payload:{field:e,value:t}}),L({type:"SET_ISVALIDATING",payload:!1})}))):(L({type:"SET_FIELD_ERROR",payload:{field:e,value:r}}),Promise.resolve(r))}return y.validationSchema?(L({type:"SET_ISVALIDATING",payload:!0}),B(M.values,e).then((function(e){return e})).then((function(t){L({type:"SET_FIELD_ERROR",payload:{field:e,value:t[e]}}),L({type:"SET_ISVALIDATING",payload:!1})}))):Promise.resolve()})),z=(0,n.useCallback)((function(e,t){var r=t.validate;C.current[e]={validate:r}}),[]),Y=(0,n.useCallback)((function(e){delete C.current[e]}),[]),J=N((function(e,t){return L({type:"SET_TOUCHED",payload:e}),(void 0===t?o:t)?G(M.values):Promise.resolve()})),$=(0,n.useCallback)((function(e){L({type:"SET_ERRORS",payload:e})}),[]),q=N((function(e,t){var n=S(e)?e(M.values):e;return L({type:"SET_VALUES",payload:n}),(void 0===t?r:t)?G(n):Promise.resolve()})),Q=(0,n.useCallback)((function(e,t){L({type:"SET_FIELD_ERROR",payload:{field:e,value:t}})}),[]),X=N((function(e,t,n){return L({type:"SET_FIELD_VALUE",payload:{field:e,value:t}}),(void 0===n?r:n)?G(F(M.values,e,t)):Promise.resolve()})),ee=(0,n.useCallback)((function(e,t){var r,n=t,a=e;if(!g(e)){e.persist&&e.persist();var i=e.target?e.target:e.currentTarget,u=i.type,o=i.name,l=i.id,c=i.value,s=i.checked,f=(i.outerHTML,i.options),d=i.multiple;n=t||(o||l),a=/number|range/.test(u)?(r=parseFloat(c),isNaN(r)?"":r):/checkbox/.test(u)?function(e,t,r){if("boolean"===typeof e)return Boolean(t);var n=[],a=!1,i=-1;if(Array.isArray(e))n=e,a=(i=e.indexOf(r))>=0;else if(!r||"true"==r||"false"==r)return Boolean(t);if(t&&r&&!a)return n.concat(r);if(!a)return n;return n.slice(0,i).concat(n.slice(i+1))}(A(M.values,n),s,c):f&&d?function(e){return Array.from(e).filter((function(e){return e.selected})).map((function(e){return e.value}))}(f):c}n&&X(n,a)}),[X,M.values]),te=N((function(e){if(g(e))return function(t){return ee(t,e)};ee(e)})),re=N((function(e,t,r){return void 0===t&&(t=!0),L({type:"SET_FIELD_TOUCHED",payload:{field:e,value:t}}),(void 0===r?o:r)?G(M.values):Promise.resolve()})),ne=(0,n.useCallback)((function(e,t){e.persist&&e.persist();var r=e.target,n=r.name,a=r.id,i=(r.outerHTML,t||(n||a));re(i,!0)}),[re]),ae=N((function(e){if(g(e))return function(t){return ne(t,e)};ne(e)})),ie=(0,n.useCallback)((function(e){S(e)?L({type:"SET_FORMIK_STATE",payload:e}):L({type:"SET_FORMIK_STATE",payload:function(){return e}})}),[]),ue=(0,n.useCallback)((function(e){L({type:"SET_STATUS",payload:e})}),[]),oe=(0,n.useCallback)((function(e){L({type:"SET_ISSUBMITTING",payload:e})}),[]),le=N((function(){return L({type:"SUBMIT_ATTEMPT"}),G().then((function(e){var t=e instanceof Error;if(!t&&0===Object.keys(e).length){var r;try{if(void 0===(r=fe()))return}catch(n){throw n}return Promise.resolve(r).then((function(e){return k.current&&L({type:"SUBMIT_SUCCESS"}),e})).catch((function(e){if(k.current)throw L({type:"SUBMIT_FAILURE"}),e}))}if(k.current&&(L({type:"SUBMIT_FAILURE"}),t))throw e}))})),ce=N((function(e){e&&e.preventDefault&&S(e.preventDefault)&&e.preventDefault(),e&&e.stopPropagation&&S(e.stopPropagation)&&e.stopPropagation(),le().catch((function(e){console.warn("Warning: An unhandled error was caught from submitForm()",e)}))})),se={resetForm:W,validateForm:G,validateField:K,setErrors:$,setFieldError:Q,setFieldTouched:re,setFieldValue:X,setStatus:ue,setSubmitting:oe,setTouched:J,setValues:q,setFormikState:ie,submitForm:le},fe=N((function(){return p(M.values,se)})),de=N((function(e){e&&e.preventDefault&&S(e.preventDefault)&&e.preventDefault(),e&&e.stopPropagation&&S(e.stopPropagation)&&e.stopPropagation(),W()})),pe=(0,n.useCallback)((function(e){return{value:A(M.values,e),error:A(M.errors,e),touched:!!A(M.touched,e),initialValue:A(E.current,e),initialTouched:!!A(_.current,e),initialError:A(b.current,e)}}),[M.errors,M.touched,M.values]),ve=(0,n.useCallback)((function(e){return{setValue:function(t,r){return X(e,t,r)},setTouched:function(t,r){return re(e,t,r)},setError:function(t){return Q(e,t)}}}),[X,re,Q]),he=(0,n.useCallback)((function(e){var t=T(e),r=t?e.name:e,n=A(M.values,r),a={name:r,value:n,onChange:te,onBlur:ae};if(t){var i=e.type,u=e.value,o=e.as,l=e.multiple;"checkbox"===i?void 0===u?a.checked=!!n:(a.checked=!(!Array.isArray(n)||!~n.indexOf(u)),a.value=u):"radio"===i?(a.checked=n===u,a.value=u):"select"===o&&l&&(a.value=a.value||[],a.multiple=!0)}return a}),[ae,te,M.values]),me=(0,n.useMemo)((function(){return!i()(E.current,M.values)}),[E.current,M.values]),ye=(0,n.useMemo)((function(){return"undefined"!==typeof s?me?M.errors&&0===Object.keys(M.errors).length:!1!==s&&S(s)?s(y):s:M.errors&&0===Object.keys(M.errors).length}),[s,me,M.errors,y]);return v({},M,{initialValues:E.current,initialErrors:b.current,initialTouched:_.current,initialStatus:O.current,handleBlur:ae,handleChange:te,handleReset:de,handleSubmit:ce,resetForm:W,setErrors:$,setFormikState:ie,setFieldTouched:re,setFieldValue:X,setFieldError:Q,setStatus:ue,setSubmitting:oe,setTouched:J,setValues:q,submitForm:le,validateForm:G,validateField:K,isValid:ye,dirty:me,unregisterField:Y,registerField:z,getFieldProps:he,getFieldMeta:pe,getFieldHelpers:ve,validateOnBlur:o,validateOnChange:r,validateOnMount:c})}function V(e){var t=L(e),r=e.component,a=e.children,i=e.render,u=e.innerRef;return(0,n.useImperativeHandle)(u,(function(){return t})),(0,n.createElement)(C,{value:t},r?(0,n.createElement)(r,t):i?i(t):a?S(a)?a(t):_(a)?null:n.Children.only(a):null)}function w(e){var t=Array.isArray(e)?[]:{};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){var n=String(r);!0===Array.isArray(e[n])?t[n]=e[n].map((function(e){return!0===Array.isArray(e)||(0,o.Z)(e)?w(e):""!==e?e:void 0})):(0,o.Z)(e[n])?t[n]=w(e[n]):t[n]=""!==e[n]?e[n]:void 0}return t}function j(e,t,r){var n=e.slice();return t.forEach((function(t,a){if("undefined"===typeof n[a]){var i=!1!==r.clone&&r.isMergeableObject(t);n[a]=i?(0,u.Z)(Array.isArray(t)?[]:{},t,r):t}else r.isMergeableObject(t)?n[a]=(0,u.Z)(e[a],t,r):-1===e.indexOf(t)&&n.push(t)})),n}var B="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement?n.useLayoutEffect:n.useEffect;function N(e){var t=(0,n.useRef)(e);return B((function(){t.current=e})),(0,n.useCallback)((function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return t.current.apply(void 0,r)}),[])}function Z(e){var t=M(),r=t.getFieldProps,a=t.getFieldMeta,i=t.getFieldHelpers,u=t.registerField,o=t.unregisterField,l=T(e)?e:{name:e},c=l.name,f=l.validate;return(0,n.useEffect)((function(){return c&&u(c,{validate:f}),function(){c&&o(c)}}),[u,o,c,f]),c||(0,s.Z)(!1),[r(l),a(c),i(c)]}function x(e){var t=e.validate,r=e.name,a=e.render,i=e.children,u=e.as,o=e.component,l=m(e,["validate","name","render","children","as","component"]),c=m(M(),["validate","validationSchema"]);var s=c.registerField,f=c.unregisterField;(0,n.useEffect)((function(){return s(r,{validate:t}),function(){f(r)}}),[s,f,r,t]);var d=c.getFieldProps(v({name:r},l)),p=c.getFieldMeta(r),h={field:d,form:c};if(a)return a(v({},h,{meta:p}));if(S(i))return i(v({},h,{meta:p}));if(o){if("string"===typeof o){var y=l.innerRef,E=m(l,["innerRef"]);return(0,n.createElement)(o,v({ref:y},d,E),i)}return(0,n.createElement)(o,v({field:d,form:c},l),i)}var T=u||"input";if("string"===typeof T){var b=l.innerRef,g=m(l,["innerRef"]);return(0,n.createElement)(T,v({ref:b},d,g),i)}return(0,n.createElement)(T,v({},d,l),i)}var H=(0,n.forwardRef)((function(e,t){var r=e.action,a=m(e,["action"]),i=null!=r?r:"#",u=M(),o=u.handleReset,l=u.handleSubmit;return(0,n.createElement)("form",Object.assign({onSubmit:l,ref:t,onReset:o,action:i},a))}));function G(e){var t=function(t){return(0,n.createElement)(I,null,(function(r){return r||(0,s.Z)(!1),(0,n.createElement)(e,Object.assign({},t,{formik:r}))}))},r=e.displayName||e.name||e.constructor&&e.constructor.name||"Component";return t.WrappedComponent=e,t.displayName="FormikConnect("+r+")",d()(t,e)}H.displayName="Form";var W=function(e,t,r){var n=K(e);return n.splice(t,0,r),n},K=function(e){if(e){if(Array.isArray(e))return[].concat(e);var t=Object.keys(e).map((function(e){return parseInt(e)})).reduce((function(e,t){return t>e?t:e}),0);return Array.from(v({},e,{length:t+1}))}return[]},z=function(e){function t(t){var r;return(r=e.call(this,t)||this).updateArrayField=function(e,t,n){var a=r.props,i=a.name;(0,a.formik.setFormikState)((function(r){var a="function"===typeof n?n:e,u="function"===typeof t?t:e,o=F(r.values,i,e(A(r.values,i))),l=n?a(A(r.errors,i)):void 0,c=t?u(A(r.touched,i)):void 0;return E(l)&&(l=void 0),E(c)&&(c=void 0),v({},r,{values:o,errors:n?F(r.errors,i,l):r.errors,touched:t?F(r.touched,i,c):r.touched})}))},r.push=function(e){return r.updateArrayField((function(t){return[].concat(K(t),[(0,p.Z)(e)])}),!1,!1)},r.handlePush=function(e){return function(){return r.push(e)}},r.swap=function(e,t){return r.updateArrayField((function(r){return function(e,t,r){var n=K(e),a=n[t];return n[t]=n[r],n[r]=a,n}(r,e,t)}),!0,!0)},r.handleSwap=function(e,t){return function(){return r.swap(e,t)}},r.move=function(e,t){return r.updateArrayField((function(r){return function(e,t,r){var n=K(e),a=n[t];return n.splice(t,1),n.splice(r,0,a),n}(r,e,t)}),!0,!0)},r.handleMove=function(e,t){return function(){return r.move(e,t)}},r.insert=function(e,t){return r.updateArrayField((function(r){return W(r,e,t)}),(function(t){return W(t,e,null)}),(function(t){return W(t,e,null)}))},r.handleInsert=function(e,t){return function(){return r.insert(e,t)}},r.replace=function(e,t){return r.updateArrayField((function(r){return function(e,t,r){var n=K(e);return n[t]=r,n}(r,e,t)}),!1,!1)},r.handleReplace=function(e,t){return function(){return r.replace(e,t)}},r.unshift=function(e){var t=-1;return r.updateArrayField((function(r){var n=r?[e].concat(r):[e];return t<0&&(t=n.length),n}),(function(e){var r=e?[null].concat(e):[null];return t<0&&(t=r.length),r}),(function(e){var r=e?[null].concat(e):[null];return t<0&&(t=r.length),r})),t},r.handleUnshift=function(e){return function(){return r.unshift(e)}},r.handleRemove=function(e){return function(){return r.remove(e)}},r.handlePop=function(){return function(){return r.pop()}},r.remove=r.remove.bind(y(r)),r.pop=r.pop.bind(y(r)),r}h(t,e);var r=t.prototype;return r.componentDidUpdate=function(e){this.props.validateOnChange&&this.props.formik.validateOnChange&&!i()(A(e.formik.values,e.name),A(this.props.formik.values,this.props.name))&&this.props.formik.validateForm(this.props.formik.values)},r.remove=function(e){var t;return this.updateArrayField((function(r){var n=r?K(r):[];return t||(t=n[e]),S(n.splice)&&n.splice(e,1),n}),!0,!0),t},r.pop=function(){var e;return this.updateArrayField((function(t){var r=t;return e||(e=r&&r.pop&&r.pop()),r}),!0,!0),e},r.render=function(){var e={push:this.push,pop:this.pop,swap:this.swap,move:this.move,insert:this.insert,replace:this.replace,unshift:this.unshift,remove:this.remove,handlePush:this.handlePush,handlePop:this.handlePop,handleSwap:this.handleSwap,handleMove:this.handleMove,handleInsert:this.handleInsert,handleReplace:this.handleReplace,handleUnshift:this.handleUnshift,handleRemove:this.handleRemove},t=this.props,r=t.component,a=t.render,i=t.children,u=t.name,o=v({},e,{form:m(t.formik,["validate","validationSchema"]),name:u});return r?(0,n.createElement)(r,o):a?a(o):i?"function"===typeof i?i(o):_(i)?null:n.Children.only(i):null},t}(n.Component);z.defaultProps={validateOnChange:!0};var Y=function(e){function t(){return e.apply(this,arguments)||this}h(t,e);var r=t.prototype;return r.shouldComponentUpdate=function(e){return A(this.props.formik.errors,this.props.name)!==A(e.formik.errors,this.props.name)||A(this.props.formik.touched,this.props.name)!==A(e.formik.touched,this.props.name)||Object.keys(this.props).length!==Object.keys(e).length},r.render=function(){var e=this.props,t=e.component,r=e.formik,a=e.render,i=e.children,u=e.name,o=m(e,["component","formik","render","children","name"]),l=A(r.touched,u),c=A(r.errors,u);return l&&c?a?S(a)?a(c):null:i?S(i)?i(c):null:t?(0,n.createElement)(t,o,c):c:null},t}(n.Component),J=G(Y);n.Component}}]);
//# sourceMappingURL=formik.2f93599b9e1174b4bdbaffcbc3c9719e.js.map